"use client";

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>R<PERSON>, Loader2 } from "lucide-react";
import { Button } from "@kalos/ui/button";

interface NavigationFooterProps {
    onPrevious?: () => void;
    previousText?: string;
    showPrevious?: boolean;
    onNext?: () => void;
    nextText?: string;
    nextDisabled?: boolean;
    nextLoading?: boolean;
    showNext?: boolean;
    className?: string;
}

export function NavigationFooter({
    onPrevious,
    previousText = "Previous",
    showPrevious = true,
    onNext,
    nextText = "Next",
    nextDisabled = false,
    nextLoading = false,
    showNext = true,
    className = "",
}: NavigationFooterProps) {
    const baseClasses = "sticky bottom-0 left-0 right-0 z-10 flex h-16 w-full items-center justify-between border-t bg-background p-4 ";

    return (
        <div className={`${baseClasses} ${className}`}>
            {showPrevious && onPrevious ? (
                <Button
                    onClick={onPrevious}
                    className="border-1 border border-blue-200 bg-blue-100 text-primary hover:bg-blue-200"
                >
                    <ArrowLeft width="16" className="mr-2" />
                    {previousText}
                </Button>
            ) : (
                <div />
            )}
            {showNext && onNext ? (
                <Button
                    onClick={onNext}
                    disabled={nextDisabled || nextLoading}
                    className={nextDisabled ? "cursor-not-allowed opacity-50" : ""}
                >
                    {nextLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    {nextText}
                    {!nextLoading && <ArrowRight width="16" className="ml-2" />}
                </Button>
            ) : (
                <div />
            )}
        </div>
    );
} 