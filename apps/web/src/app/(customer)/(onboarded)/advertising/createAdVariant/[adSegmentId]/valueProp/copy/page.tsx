"use client";

import type { Dispatch, SetStateAction } from "react";
import React, {
  createContext,
  useContext,
  useEffect,
  useRef,
  useState,
} from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { snakeCaseToWords } from "@/app/utils/snakeCaseToWords";
import LeadGenFormsDialog from "@/features/advertising/campaignGroup/components/leadGenFormsDialog";
import { useOrganization } from "@/features/organization/hooks/useOrganization";
import { api } from "@/trpc/client";
import { getBaseUrl } from "@/trpc/provider";
import { Label } from "@radix-ui/react-dropdown-menu";
import { ArrowLeft, ArrowRight, WandIcon } from "lucide-react";

import {
  CampaignGroupSegmentSegment,
  CampaignSegment,
} from "@kalos/advertising";
import { Button } from "@kalos/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@kalos/ui/card";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>rigger,
} from "@kalos/ui/dialog";
import { cn } from "@kalos/ui/index";
import { Input } from "@kalos/ui/input";
import { Popover, PopoverContent, PopoverTrigger } from "@kalos/ui/popover";
import { RadioGroup, RadioGroupItem } from "@kalos/ui/radiogroup";
import { Textarea } from "@kalos/ui/textarea";

export default function ConversationAdPage({
  params,
}: {
  params: { adSegmentId: string };
}) {
  return (
    <AdCopyContextProvider>
      {" "}
      <AdComp params={params} />
    </AdCopyContextProvider>
  );
}

function AdComp({ params }: { params: { adSegmentId: string } }) {
  const adSegmentQuery = api.v2.ads.adSegment.getOne.useQuery({
    adSegmentId: params.adSegmentId,
  });

  const adsQuery =
    api.v2.ads.adSegmentValueProp.getAdSegmentValuePropsForAdSegment.useQuery({
      adSegmentId: params.adSegmentId,
      status: "DRAFT",
    });

  const bestVariantQuery = api.v2.ads.adSegmentBestVariant.getOne.useQuery({
    adSegmentId: params.adSegmentId,
    adFormatType: "conversationSubject",
  });

  const conversationSubjectCopyQuery =
    api.v2.ads.conversationSubjectCopy.getOne.useQuery(
      {
        conversationSubjectCopyId: bestVariantQuery.data?.variantId ?? "",
        status: "ACTIVE",
      },
      {
        enabled: !!bestVariantQuery.data?.variantId,
      },
    );

  const apiUtils = api.useUtils();
  const createNewVarientsMutation =
    api.v2.ads.adSegment.createNewVarients.useMutation({
      onSuccess: async () => {
        await apiUtils.v2.ads.invalidate();
        router.push(`/advertising/performance/${params.adSegmentId}`);
      },
    });

  const adProgramQuery = api.v2.ads.linkedInAdProgram.getOne.useQuery(
    {
      id: adSegmentQuery.data?.linkedInAdProgramId ?? "",
    },
    {
      enabled: !!adSegmentQuery.data?.linkedInAdProgramId,
    },
  );

  const router = useRouter();

  return (
    <div className=" w-full flex-col items-start justify-between">
      <div className="flex h-[calc(100vh-64px)] w-full flex-col items-start justify-start space-y-4 overflow-auto px-4 pb-20 pt-4">
        {adSegmentQuery.data && adProgramQuery.data && (
          <SegmentComponent
            segment={{
              id: adSegmentQuery.data.id,
              adProgramId: adSegmentQuery.data.linkedInAdProgramId,
              segmentId: adSegmentQuery.data.segmentId,
            }}
            campaignGroupId={adProgramQuery.data.id}
            adAccountId={adProgramQuery.data.linkedInAdAccountId}
          />
        )}
      </div>
      <div className="sticky bottom-0 left-0 right-0 z-10 flex h-16 w-full items-center justify-between border-t bg-background p-4">
        <Button
          onClick={() =>
            router.push(
              `/advertising/createAdVariant/${params.adSegmentId}/valueProp/select`,
            )
          }
          className="border-1 border border-blue-200 bg-blue-100 text-primary hover:bg-blue-200"
        >
          <ArrowLeft width="16" className="mr-2" /> Previous
        </Button>
        <Button
          onClick={() => {
            if (!conversationSubjectCopyQuery.data) {
              alert("No conversation subject copy found");
              return;
            }
            if (!adsQuery.data) {
              alert("No ads found");
              return;
            }
            createNewVarientsMutation.mutate({
              adSegmentId: params.adSegmentId,
              adFormatType: "SPONSORED_INMAIL",
              data: {
                type: "valueProp",
                data: {
                  subjectType: conversationSubjectCopyQuery.data.type,
                  data: adsQuery.data.map((each) => ({
                    valuePropId: each.id,
                    messageCopyType: "standard",
                    ctaType: "standard",
                  })),
                },
              },
            });
          }}
        >
          Submit
        </Button>
      </div>
    </div>
  );
}
/*
 <AdText
          adSite="LinkedIn"
          topic=""
          targetAudience={["Banking"]}
          valuePropositions={["Compliance"]}
          productPositioning="We sell products"
          adId="tst"
        />
        */

function SegmentComponent({
  segment,
  campaignGroupId,
  adAccountId,
  index,
}: {
  segment: {
    id: string;
    adProgramId: string;
    segmentId: string;
  };
  campaignGroupId: string;
  adAccountId: string;
  index?: number;
}) {
  const adsQuery =
    api.v2.ads.adSegmentValueProp.getAdSegmentValuePropsForAdSegment.useQuery({
      adSegmentId: segment.id,
      status: "DRAFT",
    });
  const segmentQuery = api.v2.core.segment.getSegment.useQuery({
    segmentId: segment.segmentId,
  });

  const bestVariantQuery = api.v2.ads.adSegmentBestVariant.getOne.useQuery({
    adSegmentId: segment.id,
    adFormatType: "conversationSubject",
  });

  const conversationSubjectCopyQuery =
    api.v2.ads.conversationSubjectCopy.getOne.useQuery(
      {
        conversationSubjectCopyId: bestVariantQuery.data?.variantId ?? "",
        status: "ACTIVE",
      },
      {
        enabled: !!bestVariantQuery.data?.variantId,
      },
    );

  const [isRefreshingCopy, setIsRefreshingCopy] = useState(false);
  return (
    <Card className="w-full">
      <CardHeader className="w-full">
        {index !== undefined && (
          <div className="mr-2 text-xs text-muted-foreground">
            Segment {index + 1}
          </div>
        )}
        <CardTitle className="flex w-full items-center justify-between">
          <div className="flex items-center">
            {segmentQuery.data && (
              <SegmentDetails row={{ original: segmentQuery.data }} />
            )}
          </div>
          <EditBaseCopyDialog
            adSegmentId={segment.id}
            setIsRefreshingCopy={setIsRefreshingCopy}
          />
        </CardTitle>
      </CardHeader>
      <CardContent
        data-content-segment-container
        className="flex w-full flex-wrap items-start justify-start gap-4"
      >
        {!adsQuery.isRefetching &&
          !isRefreshingCopy &&
          !adsQuery.isLoading &&
          !adsQuery.isFetching &&
          adsQuery.data &&
          bestVariantQuery.data &&
          conversationSubjectCopyQuery.data &&
          conversationSubjectCopyQuery.data !== null &&
          adsQuery.data[0] && (
            <>
              {adsQuery.data &&
                adsQuery.data[0] &&
                conversationSubjectCopyQuery.data &&
                adsQuery.data.map((each) => {
                  return (
                    <AdCard
                      key={each.id}
                      valuePropId={each.id}
                      valueProp={each.valueProp}
                      adSegmentId={segment.id}
                      conversationSubjectCopyType={
                        conversationSubjectCopyQuery.data!.type
                      }
                      leadGenFormUrn={
                        conversationSubjectCopyQuery.data!.leadGenForm ??
                        undefined
                      }
                      adAccountId={adAccountId}
                      conversationSubjectCopyContent={
                        conversationSubjectCopyQuery.data!.content
                      }
                    />
                  );
                })}
            </>
          )}
      </CardContent>
    </Card>
  );
}

function AdCard({
  valuePropId,
  valueProp,
  adSegmentId,
  conversationSubjectCopyType,
  adAccountId,
  leadGenFormUrn,
  conversationSubjectCopyContent,
}: {
  valuePropId: string;
  valueProp: string;
  adSegmentId: string;
  conversationSubjectCopyType: string;
  adAccountId: string;
  leadGenFormUrn?: string;
  conversationSubjectCopyContent: string;
}) {
  return (
    <Card className="self-stretch">
      <CardHeader>
        <CardTitle>{snakeCaseToWords(valueProp)}</CardTitle>
      </CardHeader>
      <CardContent className=" h-full">
        <AdText
          valuePropId={valuePropId}
          adSegmentId={adSegmentId}
          conversationSubjectCopyType={conversationSubjectCopyType}
          adAccountId={adAccountId}
          leadGenFormUrn={leadGenFormUrn}
          conversationSubjectCopyContent={conversationSubjectCopyContent}
        />
      </CardContent>
    </Card>
  );
}

function AdText({
  valuePropId,
  adSegmentId,
  conversationSubjectCopyType,
  adAccountId,
  leadGenFormUrn,
  conversationSubjectCopyContent,
}: {
  valuePropId: string;
  adSegmentId: string;
  conversationSubjectCopyType: string;
  adAccountId: string;
  leadGenFormUrn?: string;
  conversationSubjectCopyContent: string;
}) {
  const organizationUser = api.v2.core.user.getUser.useQuery();
  const [generating, setGenerating] = useState(true);
  const [adCopy, setAdCopy] = useState<{
    title: string;
    body: string;
    callToAction: string;
    leadGenForm: string | null;
    done: boolean;
  }>({
    title: "",
    body: "",
    callToAction: "",
    leadGenForm: null,
    done: false,
  });
  async function getAdCopy() {
    if (!leadGenFormUrn) {
      alert("No lead gen form Urn");
      return;
    }
    const response = await fetch(`${getBaseUrl()}/convostream`, {
      method: "POST",
      headers: {
        Accept: "application/json, text/plain, */*",
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        valuePropId: valuePropId,
        organizationId: organizationUser.data?.organizationId,
        subjectType: conversationSubjectCopyType,
        messageType: "standard",
        leadGenFormUrn: leadGenFormUrn,
        injectedSubject: conversationSubjectCopyContent,
      }),
    });
    if (!response.ok || !response.body) {
      throw response.statusText;
    }

    setAdCopy({
      title: "",
      body: "",
      callToAction: "",
      leadGenForm: null,
      done: false,
    });

    // Here we start prepping for the streaming response
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    const loopRunner = true;

    while (loopRunner) {
      // Here we start reading the stream, until its done.
      const { value, done } = await reader.read();
      if (done) {
        break;
      }
      const decodedChunk = decoder
        .decode(value, { stream: true })
        .split("!JSON_LINE_END!");
      for (const each of decodedChunk) {
        try {
          const json = JSON.parse(each);
          setAdCopy((prev) => ({
            title: prev.title + json.subject,
            body: prev.body + json.message,
            callToAction: prev.callToAction + json.callToAction,
            leadGenForm: json.leadGenForm,
            done: prev.done + json.done,
          }));
        } catch (e) { }
      }
      setGenerating(false);
    }
  }

  const [baseCopyRefetchInterval, setBaseCopyRefetchInterval] = useState<
    number | undefined
  >(undefined);
  const [readyToGetAdCopy, setReadyToGetAdCopy] = useState(false);

  const baseCopyQuery = api.v2.ads.conversationCopy.getBaseCopy.useQuery(
    {
      adSegmentId: adSegmentId,
    },
    {
      refetchInterval: baseCopyRefetchInterval,
    },
  );

  const [getCopyRan, setGetCopyRan] = useState(false);

  useEffect(() => {
    if (baseCopyQuery.data && organizationUser.data?.organizationId) {
      setBaseCopyRefetchInterval(undefined);
      setReadyToGetAdCopy(true);
    }
  }, [baseCopyQuery.data, organizationUser.data]);

  useEffect(() => {
    if (readyToGetAdCopy && !getCopyRan) {
      getAdCopy();
      setGetCopyRan(true);
    }
  }, [readyToGetAdCopy, getCopyRan]);

  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  const apiUtils = api.useUtils();
  const updateAdCopy =
    api.v2.ads.conversationCopy.updateConversationCopy.useMutation({
      onSuccess: async () => {
        await apiUtils.v2.ads.invalidate();
        setIsEditing(false);
        setIsSaving(false);
      },
    });

  const paragraphRef = useRef<HTMLParagraphElement>(null);
  const [textareaHeight, setTextareaHeight] = useState<number | undefined>(
    undefined,
  );

  // Update height when content changes or editing mode changes
  useEffect(() => {
    if (paragraphRef.current) {
      const height = paragraphRef.current.offsetHeight;
      const computedStyle = window.getComputedStyle(paragraphRef.current);
      console.log("Paragraph height:", height);
      console.log("Paragraph computed height:", computedStyle.height);
      console.log("Paragraph line height:", computedStyle.lineHeight);
      console.log("Paragraph padding:", computedStyle.padding);
      setTextareaHeight(height);
    }
  }, [isEditing, adCopy.body]);

  const udpateLeadGenMutation =
    api.v2.ads.conversationCopy.updateConversationSubjectCopyLeadGenForm.useMutation(
      {
        onSuccess: async () => {
          await apiUtils.v2.ads.invalidate();
        },
      },
    );

  const getLeadGenFormQuery =
    api.v2.ads.conversationCopy.getConversationSubjectCopyLeadGenForm.useQuery({
      valuePropId: valuePropId,
      conversationCopyType: conversationSubjectCopyType,
    });

  function handleSave(leadGenFormUrn: string) {
    udpateLeadGenMutation.mutate({
      valuePropId: valuePropId,
      conversationCopyType: conversationSubjectCopyType,
      leadGenFormUrn: leadGenFormUrn,
    });
  }

  const [feedbackMode, setFeedbackMode] = useState<"body" | "title">("body");
  const [isFeedbackOpen, setIsFeedbackOpen] = useState(false);
  const [feedback, setFeedback] = useState("");

  async function submitFeedback() {
    setIsFeedbackOpen(false);
    const baseBody = feedbackMode == "body" ? adCopy.body : adCopy.title;

    const response = await fetch(`${getBaseUrl()}/stream-refined`, {
      method: "POST",
      headers: {
        Accept: "application/json, text/plain, */*",
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        baseCopy: baseBody,
        feedback: feedback,
        config: {
          type: "socialPost",
          field: feedbackMode,
        },
      }),
    });
    if (!response.ok || !response.body) {
      throw response.statusText;
    }

    if (feedbackMode == "body") {
      setAdCopy((prev) => ({
        ...prev,
        body: "",
      }));
    } else {
      setAdCopy((prev) => ({
        ...prev,
        title: "",
      }));
    }

    // Here we start prepping for the streaming response
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    const loopRunner = true;

    while (loopRunner) {
      // Here we start reading the stream, until its done.
      const { value, done } = await reader.read();
      if (done) {
        break;
      }
      const decodedChunk = decoder.decode(value, { stream: true });
      for (const each of decodedChunk) {
        try {
          if (feedbackMode == "body") {
            setAdCopy((prev) => ({
              ...prev,
              body: prev.body + each,
            }));
          } else {
            setAdCopy((prev) => ({
              ...prev,
              title: prev.title + each,
            }));
          }
        } catch (e) { }
      }
    }
  }
  return (
    <div
      className={cn(
        generating ? "opacity-50" : "",
        "flex  w-[480px] flex-col items-start justify-between ",
      )}
    >
      <div className="flex w-full flex-col justify-start space-y-2 ">
        <div className="flex items-center justify-between">
          {isEditing && (
            <>
              <Popover open={isFeedbackOpen} onOpenChange={setIsFeedbackOpen}>
                <PopoverTrigger asChild>
                  <Button className="border border-blue-500 bg-blue-200 text-black">
                    <WandIcon width="16" height="16" className="mr-1" />
                    Ask Blue to Rewrite
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="flex flex-col items-start justify-start space-y-2">
                  <Textarea
                    value={feedback}
                    onChange={(e) => setFeedback(e.target.value)}
                  />
                  <div className="flex w-full items-center justify-between gap-2">
                    <div className="flex items-center justify-center gap-2">
                      <RadioGroup
                        defaultValue="body"
                        value={feedbackMode}
                        onValueChange={(value) =>
                          setFeedbackMode(value as "body" | "title")
                        }
                      >
                        <div className="flex items-center space-x-2">
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="body" id="r1" />
                            <Label className="text-sm">Message</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="title" id="r2" />
                            <Label className="text-sm">Subject</Label>
                          </div>
                        </div>
                      </RadioGroup>
                    </div>
                    <Button onClick={submitFeedback}>Submit</Button>
                  </div>
                </PopoverContent>
              </Popover>
              <div className="flex items-center justify-end gap-2">
                <Button
                  variant="outline"
                  disabled={isSaving || generating}
                  onClick={() => {
                    getAdCopy();
                    setIsEditing(false);
                  }}
                >
                  Cancel
                </Button>
                <Button
                  disabled={isSaving || generating}
                  onClick={() => {
                    updateAdCopy.mutate({
                      linkedInAdSegmentValuePropId: valuePropId,
                      conversationMessageCopyType: "standard",
                      message: adCopy.body,
                      subject: adCopy.title,
                      callToAction: adCopy.callToAction,
                      conversationSubjectCopyType: conversationSubjectCopyType,
                      conversationCallToActionCopyType: "standard",
                    });
                    setIsSaving(true);
                  }}
                >
                  Save
                </Button>
              </div>
            </>
          )}
        </div>
        <div className="flex items-center justify-end gap-2">
          {!isEditing && (
            <Button
              disabled={generating || isSaving}
              onClick={() => setIsEditing(true)}
            >
              Edit
            </Button>
          )}
        </div>

        <h1 className="text-sm font-semibold">Subject</h1>
        {!isEditing && (
          <p className="min-h-[1px] w-full resize-none p-0 text-sm shadow-none focus:focus-visible:ring-0 disabled:bg-transparent disabled:text-foreground ">
            {adCopy.title}
          </p>
        )}
        {isEditing && (
          <Textarea
            value={adCopy.title}
            onChange={(e) => setAdCopy({ ...adCopy, title: e.target.value })}
            className="min-h-[1px] w-full resize-none p-0 shadow-none focus-visible:ring-0 disabled:bg-transparent disabled:text-foreground "
          />
        )}
      </div>
      <div className="flex w-full flex-col justify-start space-y-2 pb-4 pt-2">
        <h1 className="text-sm font-semibold">Message</h1>
        <p
          ref={paragraphRef}
          className="min-h-[1px] w-full resize-none p-0 text-sm leading-normal shadow-none"
          style={{
            position: isEditing ? "absolute" : "static",
            visibility: isEditing ? "hidden" : "visible",
            boxSizing: "border-box",
            lineHeight: "21px",
          }}
        >
          {adCopy.body.split("\n").map((line, index) => (
            <React.Fragment key={index}>
              {line}
              <br />
            </React.Fragment>
          ))}
        </p>
        {isEditing && (
          <Textarea
            value={adCopy.body}
            onChange={(e) => setAdCopy({ ...adCopy, body: e.target.value })}
            style={{
              height: `310px`, // Exact height from debug
              minHeight: "100px",
              padding: "8px 12px",
              boxSizing: "border-box",
              lineHeight: "21px", // Exact line height from debug
              width: "100%",
              resize: "none",
            }}
            className="w-full leading-normal"
          />
        )}
      </div>
      <h1 className="text-sm font-semibold">Call to Action</h1>
      {!isEditing && (
        <p className="min-h-[1px] w-full resize-none p-0 text-sm shadow-none focus:focus-visible:ring-0 disabled:bg-transparent disabled:text-foreground ">
          {adCopy.callToAction}
        </p>
      )}
      {isEditing && (
        <Textarea
          value={adCopy.callToAction}
          onChange={(e) =>
            setAdCopy({ ...adCopy, callToAction: e.target.value })
          }
          className="min-h-[1px] w-full resize-none p-0 shadow-none focus-visible:ring-0 disabled:bg-transparent disabled:text-foreground "
        />
      )}
    </div>
  );
}

function EditBaseCopyDialog({
  adSegmentId,
  setIsRefreshingCopy,
}: {
  adSegmentId: string;
  setIsRefreshingCopy: Dispatch<SetStateAction<boolean>>;
}) {
  const baseCopyQuery = api.v2.ads.conversationCopy.getBaseCopy.useQuery({
    adSegmentId: adSegmentId,
  });

  const apiUtils = api.useUtils();

  const updateBaseCopy =
    api.v2.ads.conversationCopy.updateBaseConversationCopy.useMutation({
      onSuccess: async () => {
        await apiUtils.v2.ads.invalidate();
        setIsRefreshingCopy(false);
        setIsSaving(false);
        setDialogOpen(false);
      },
    });

  const [editText, setEditText] = useState("");
  const [isSaving, setIsSaving] = useState(false);
  const [feedback, setFeedback] = useState("");

  const [dialogOpen, setDialogOpen] = useState(false);

  useEffect(() => {
    setEditText(baseCopyQuery.data?.baseCopy || "");
  }, [baseCopyQuery.data]);

  function saveBaseCopy() {
    setIsSaving(true);
    console.log("edit text", editText);
    setIsRefreshingCopy(true);
    updateBaseCopy.mutate({
      adSegmentId: adSegmentId,
      baseCopy: editText,
    });
  }

  const [isFeedbackOpen, setIsFeedbackOpen] = useState(false);

  async function submitFeedback() {
    setEditText("");
    const response = await fetch(`${getBaseUrl()}/stream-refined`, {
      method: "POST",
      headers: {
        Accept: "application/json, text/plain, */*",
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        baseCopy: editText,
        feedback: feedback,
        config: {
          type: "conversation",
          field: "message",
        },
      }),
    });
    if (!response.ok || !response.body) {
      throw response.statusText;
    }

    // Here we start prepping for the streaming response
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    const loopRunner = true;

    while (loopRunner) {
      // Here we start reading the stream, until its done.
      const { value, done } = await reader.read();
      if (done) {
        break;
      }
      const decodedChunk = decoder.decode(value, { stream: true });
      for (const each of decodedChunk) {
        try {
          setEditText((prev) => prev + each);
        } catch (e) { }
      }
    }
  }

  useEffect(() => {
    if (!dialogOpen) {
      if (baseCopyQuery.data) {
        setEditText(baseCopyQuery.data.baseCopy);
      }
    }
  }, [dialogOpen]);

  useEffect(() => {
    setFeedback("");
  }, [isFeedbackOpen]);

  return (
    <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
      <DialogTrigger asChild>
        <Button>Edit Base Copy</Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Edit Base Copy</DialogTitle>
        </DialogHeader>
        <div className="flex w-full justify-between">
          <Popover open={isFeedbackOpen} onOpenChange={setIsFeedbackOpen}>
            <PopoverTrigger asChild>
              <Button className="border border-blue-500 bg-blue-200 text-black">
                <WandIcon width="16" height="16" className="mr-1" />
                Ask Blue to Rewrite
              </Button>
            </PopoverTrigger>
            <PopoverContent className="flex flex-col items-start justify-start space-y-2">
              <Textarea
                value={feedback}
                onChange={(e) => setFeedback(e.target.value)}
              />
              <div className="flex w-full items-center justify-end gap-2">
                <Button onClick={submitFeedback}>Submit</Button>
              </div>
            </PopoverContent>
          </Popover>
          <div className="flex w-full items-center justify-end gap-2">
            <Button variant="outline" onClick={() => setDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={saveBaseCopy} disabled={isSaving}>
              Save
            </Button>
          </div>
        </div>

        <span className="text-sm font-semibold">Base Copy</span>
        <Textarea
          value={editText}
          onChange={(e) => setEditText(e.target.value)}
          style={{
            height: `310px`, // Exact height from debug
            minHeight: "100px",
            padding: "8px 12px",
            boxSizing: "border-box",
            lineHeight: "21px", // Exact line height from debug
            width: "100%",
            resize: "none",
          }}
        />
      </DialogContent>
    </Dialog>
  );
}

function SegmentDetails({
  row,
}: {
  row: {
    original: {
      name?: string | null;
      jobFunction?: string | null;
      jobSeniority?: string | null;
      verticals?: string[] | null;
      annualContractValueLowBound?: number | null;
      annualContractValueHighBound?: number | null;
      annualRevenueLowBound?: number | null;
      annualRevenueHighBound?: number | null;
      numberOfEmployeesLowBound?: number | null;
      numberOfEmployeesHighBound?: number | null;
    };
  };
}) {
  console.log(row.original);
  if (!row.original.name) {
    const nameArray: string[] = [];

    if (row.original.jobFunction) {
      nameArray.push(snakeCaseToWords(row.original.jobFunction));
    }
    if (row.original.jobSeniority) {
      nameArray.push(snakeCaseToWords(row.original.jobSeniority));
    }

    if (row.original.verticals) {
      nameArray.push(...row.original.verticals);
    }

    if (
      row.original.annualContractValueLowBound &&
      row.original.annualContractValueHighBound
    ) {
      nameArray.push(
        `$${row.original.annualContractValueLowBound
          .toString()
          .replace(
            /\B(?=(\d{3})+(?!\d))/g,
            ",",
          )}-$${row.original.annualContractValueHighBound
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} ACV`,
      );
    }
    if (
      row.original.annualContractValueLowBound &&
      !row.original.annualContractValueHighBound
    ) {
      nameArray.push(
        `$${row.original.annualContractValueLowBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")}+ ACV`,
      );
    }
    if (
      !row.original.annualContractValueLowBound &&
      row.original.annualContractValueHighBound
    ) {
      nameArray.push(
        `<$${row.original.annualContractValueHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} ACV`,
      );
    }

    if (
      row.original.annualRevenueLowBound &&
      row.original.annualRevenueHighBound
    ) {
      nameArray.push(
        `$${row.original.annualRevenueLowBound
          .toString()
          .replace(
            /\B(?=(\d{3})+(?!\d))/g,
            ",",
          )}-$${row.original.annualRevenueHighBound
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} prospect revenue`,
      );
    }
    if (
      row.original.annualRevenueLowBound &&
      !row.original.annualRevenueHighBound
    ) {
      nameArray.push(
        `$${row.original.annualRevenueLowBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")}+ prospect revenue`,
      );
    }
    if (
      !row.original.annualRevenueLowBound &&
      row.original.annualRevenueHighBound
    ) {
      nameArray.push(
        `<$${row.original.annualRevenueHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} prospect revenue`,
      );
    }

    if (
      row.original.numberOfEmployeesLowBound &&
      row.original.numberOfEmployeesHighBound
    ) {
      nameArray.push(
        `${row.original.numberOfEmployeesLowBound
          .toString()
          .replace(
            /\B(?=(\d{3})+(?!\d))/g,
            ",",
          )}-${row.original.numberOfEmployeesHighBound
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} employees`,
      );
    }
    if (
      row.original.numberOfEmployeesLowBound &&
      !row.original.numberOfEmployeesHighBound
    ) {
      nameArray.push(
        `${row.original.numberOfEmployeesLowBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")}+ employees`,
      );
    }
    if (
      !row.original.numberOfEmployeesLowBound &&
      row.original.numberOfEmployeesHighBound
    ) {
      nameArray.push(
        `<${row.original.numberOfEmployeesHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} employees`,
      );
    }

    if (nameArray.length == 0) {
      return (
        <div className="space-y-1">
          <span className="w-[80px]">All</span>
        </div>
      );
    }
    return (
      <div className="w-full space-y-1 text-wrap py-1">
        <span className="">{nameArray.join(" • ")}</span>
      </div>
    );
  }
  return (
    <div className="space-y-1">
      <span className="w-[80px]">{row.original.name}</span>
    </div>
  );
}

const AdCopyContext = createContext<{
  adCopy: {
    adId: string;
    headline: string;
    introductoryText: string;
    description: string;
  }[];
  setAdCopy: Dispatch<
    SetStateAction<
      {
        adId: string;
        headline: string;
        introductoryText: string;
        description: string;
      }[]
    >
  >;
}>({
  adCopy: [],
  setAdCopy: () => { },
});

function useAdCopyContext() {
  return useContext(AdCopyContext);
}

function AdCopyContextProvider({ children }: { children: React.ReactNode }) {
  const [adCopy, setAdCopy] = useState<
    {
      adId: string;
      headline: string;
      introductoryText: string;
      description: string;
    }[]
  >([]);

  return (
    <AdCopyContext.Provider value={{ adCopy, setAdCopy }}>
      {children}
    </AdCopyContext.Provider>
  );
}
