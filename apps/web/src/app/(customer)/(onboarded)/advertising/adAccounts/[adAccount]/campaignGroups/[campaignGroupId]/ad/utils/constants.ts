// LinkedIn character limits (Official LinkedIn Documentation 2024/2025)
// Sources: 
// - https://www.linkedin.com/help/lms/answer/a426534 (Single Image Ads/Sponsored Content)
// - https://www.linkedin.com/help/linkedin/answer/a426057 (Conversation Ads)

// Sponsored Content / Single Image Ads Limits
export const SPONSORED_CONTENT_LIMITS = {
    INTRODUCTORY_TEXT: 600,     // Max 600, 150 recommended to avoid truncation
    HEADLINE: 200,              // Max 200, 70 recommended to avoid truncation
} as const;

// Conversation Ads Limits (for conversation-ad page)
export const CONVERSATION_ADS_LIMITS = {
    SUBJECT: 60,               // Max 60 characters
    INTRO_MESSAGE: 8000,       // Max 8,000 characters
    CTA_BUTTON: 25,           // Max 25 characters per CTA button
} as const;

// Legacy/Generic limits (for backward compatibility)
export const LINKEDIN_LIMITS = {
    POST_TEXT: 600,           // Introductory text for sponsored content
    CTA: 25,                 // Call to action button text (for conversation ads)
    SUBJECT: 60,             // Subject line
    HEADLINE: 200,           // Headline text
} as const;

export const VALIDATION_RULES = {
    MIN_CHARACTERS: 5,        // Minimum characters required for feedback submission
} as const;