"use client";

import { use<PERSON><PERSON>back, useEffect, use<PERSON>emo, useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { snakeCaseToWords } from "@/app/utils/snakeCaseToWords";
import { formatDestinationUrl } from "@/features/advertising/utils/url-formatter";
import { api } from "@/trpc/client";
import { ReloadIcon } from "@radix-ui/react-icons";
import { UseQueryResult } from "@tanstack/react-query";
import { UseTRPCQueryResult } from "@trpc/react-query/shared";
import { ArrowLeft } from "lucide-react";
import { NavigationFooter } from "@/components/ui/navigation-footer";
import { z } from "zod";

import type { CampaignGroupSegmentSegment } from "@kalos/advertising";
import { Button } from "@kalos/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@kalos/ui/card";
import { Input } from "@kalos/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@kalos/ui/select";
import { Separator } from "@kalos/ui/seperator";

import { SuccessScreen } from "./_components/SuccessScreen";

interface DestinationUrl {
  adSegmentId: string;
  url: string;
}

interface PreviewPageProps {
  params: {
    adAccount: string;
    campaignGroupId: string;
  };
}

export default function PreviewPage({ params }: PreviewPageProps) {
  return (
    <Preview
      adAccount={params.adAccount}
      campaignGroupId={params.campaignGroupId}
    />
  );
}

interface PreviewProps {
  adAccount: string;
  campaignGroupId: string;
}

function Preview({ adAccount, campaignGroupId }: PreviewProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDeployed, setIsDeployed] = useState(false);
  const [showSuccessScreen, setShowSuccessScreen] = useState(false);
  const [sender, setSender] = useState<string>("");
  const [senderError, setSenderError] = useState<boolean>(false);
  const [budgetError, setBudgetError] = useState<boolean>(false);
  const [destinationUrlError, setDestinationUrlError] = useState<string[]>([]);
  const [destinationUrls, setDestinationUrls] = useState<DestinationUrl[]>([]);
  const [refetchInterval, setRefetchInterval] = useState<number | undefined>(
    1000,
  );
  const [campaignGroupSegmentBudgets, setCampaignGroupSegmentBudgets] =
    useState<Record<string, number>>({});

  // State to store audience counts per segment
  const [audienceCountMap, setAudienceCountMap] = useState<
    Record<string, number>
  >({});

  // Store value props counts for each segment
  const [valuePropsCountMap, setValuePropsCountMap] = useState<
    Record<string, number>
  >({});

  const adProgram = api.v2.ads.linkedInAdProgram.getOne.useQuery({
    id: campaignGroupId,
  });

  // Fetch campaign group data
  const campaignGroupQuery = api.v2.ads.linkedInAdProgram.getOne.useQuery({
    id: campaignGroupId,
  });

  // Fetch segments data
  const segmentsQuery =
    api.v2.ads.adSegment.getAllAdSegmentsForAdProgram.useQuery({
      adProgramId: campaignGroupId,
    });

  // Fetch senders data
  const senderQuery = api.v2.ads.linkedInApi.getSenders.useQuery();

  // Fix the API method name and parameter name to match the controller
  const creativesQuery =
    api.v2.ads.linkedInAdProgramCreative.getForAdProgram.useQuery({
      linkedInAdProgramId: campaignGroupId,
    });

  const firstSegmentId = segmentsQuery.data?.[0]?.id;

  // Only run the audiences query if we have a segment ID
  const audiencesQuery = api.v2.ads.adAudience.getAllForAdSegment.useQuery(
    {
      adSegmentId: firstSegmentId || "",
    },
    {
      refetchInterval: refetchInterval,
      enabled: !!firstSegmentId,
    },
  );

  // Deploy mutation
  const deployMutation = api.v2.ads.linkedInAdProgram.deploy.useMutation({
    onSuccess: () => {
      setIsSubmitting(false);
      setIsDeployed(true);
      setTimeout(() => {
        setShowSuccessScreen(true);
      }, 500);
    },
  });

  // Initialize destination URLs when segments data is loaded
  useEffect(() => {
    if (segmentsQuery.data) {
      setDestinationUrls(
        segmentsQuery.data.map((segment) => ({
          url: "",
          adSegmentId: segment.id,
        })),
      );
    }
  }, [segmentsQuery.data]);

  const handleValuePropsCountChange = useCallback(
    (segmentId: string, count: number): void => {
      setValuePropsCountMap((prev) => {
        if (prev[segmentId] === count) {
          return prev;
        }
        return {
          ...prev,
          [segmentId]: count,
        };
      });
    },
    [],
  );

  const handleAudienceCountChange = useCallback(
    (segmentId: string, count: number): void => {
      setAudienceCountMap((prev) => {
        if (prev[segmentId] === count) {
          return prev;
        }
        return {
          ...prev,
          [segmentId]: count,
        };
      });
    },
    [],
  );

  // Stop polling for audiences once we have data or segments are ready
  useEffect(() => {
    if (audiencesQuery.data && audiencesQuery.data.length > 0) {
      setRefetchInterval(undefined);
    } else if (segmentsQuery.data?.every((segment) => segment.ready)) {
      setRefetchInterval(undefined);
    }
  }, [audiencesQuery.data, segmentsQuery.data]);

  // Distribute budget evenly among segments
  useEffect(() => {
    // Only proceed if we have both segments and campaign group data
    if (!segmentsQuery.data || !campaignGroupQuery.data) {
      return;
    }

    // Check if we have segments to distribute the budget to
    if (segmentsQuery.data.length === 0) {
      return;
    }

    const budgets: Record<string, number> = {};
    const segments = segmentsQuery.data;
    const segmentCount = segments.length;

    // Get the total budget based on campaign type
    const totalBudget =
      campaignGroupQuery.data.type === "EVERGREEN"
        ? campaignGroupQuery.data.monthlyBudget || 0
        : campaignGroupQuery.data.totalBudget || 0;

    if (totalBudget === 0) {
      // Set all budgets to 0 if no budget is allocated
      segments.forEach((segment) => {
        budgets[segment.id] = 0;
      });
    } else {
      // Calculate budget per segment
      const budgetPerSegment = Math.floor(totalBudget / segmentCount);

      // Calculate remainder to handle rounding errors
      const remainder = totalBudget - budgetPerSegment * segmentCount;

      // Distribute budget evenly
      segments.forEach((segment) => {
        budgets[segment.id] = budgetPerSegment;
      });

      // Handle remainder by finding the first segment with a valid ID
      if (remainder > 0) {
        // Find the first segment with a valid ID without using array indexing
        const firstSegmentWithId = segments.find(
          (segment) => segment && typeof segment.id === "string",
        );
        if (firstSegmentWithId) {
          budgets[firstSegmentWithId.id] =
            (budgets[firstSegmentWithId.id] || 0) + remainder;
        }
      }
    }

    setCampaignGroupSegmentBudgets(budgets);
  }, [segmentsQuery.data, campaignGroupQuery.data]);

  // Form submission handler
  function onSubmit() {
    if (!segmentsQuery.data) return;

    let hasErrors = false;

    // Validate destination URLs for non-lead generation campaigns (single image and video formats)
    if (
      campaignGroupQuery.data?.objectiveType !== "LEAD_GENERATION" &&
      (campaignGroupQuery.data?.adFormat.format === "SINGLE_IMAGE" ||
        campaignGroupQuery.data?.adFormat.format === "VIDEO")
    ) {
      const urlValidator = z.string().url();
      const errors: string[] = [];

      for (const segment of segmentsQuery.data) {
        const curr = destinationUrls.find(
          (url) => url.adSegmentId === segment.id,
        );

        if (!curr?.url) {
          errors.push(segment.id);
          hasErrors = true;
        } else {
          try {
            urlValidator.parse(curr.url);
            console.log("curr.url", curr.url)
          } catch (e) {
            errors.push(segment.id);
            hasErrors = true;
          }
        }
      }

      setDestinationUrlError(errors);
    }

    // Validate sender selection
    if (
      !sender &&
      campaignGroupQuery.data?.adFormat.type == "SPONSORED_INMAIL"
    ) {
      setSenderError(true);
      hasErrors = true;
    } else {
      setSenderError(false);
    }

    // Validate budget allocation
    const totalBudget = Object.values(campaignGroupSegmentBudgets).reduce(
      (acc, curr) => acc + curr,
      0,
    );

    if (campaignGroupQuery.data?.type == "EVERGREEN") {
      if (totalBudget !== (campaignGroupQuery.data.monthlyBudget || 0)) {
        setBudgetError(true);
        hasErrors = true;
      } else {
        setBudgetError(false);
      }
    } else {
      if (totalBudget !== (campaignGroupQuery.data?.totalBudget || 0)) {
        setBudgetError(true);
        hasErrors = true;
      } else {
        setBudgetError(false);
      }
    }

    if (!hasErrors) {
      setIsSubmitting(true);

      deployMutation.mutate({
        adProgramId: campaignGroupId,
        adSegmentBudgets: campaignGroupSegmentBudgets,
        destinationUrls,
        sender,
      });
    }
  }

  // Handle budget change for a segment
  function handleBudgetChange(segmentId: string, value: string) {
    const numericValue = parseInt(value, 10) || 0;
    setCampaignGroupSegmentBudgets((prev) => ({
      ...prev,
      [segmentId]: numericValue,
    }));
  }

  // Handle destination URL change for a segment
  function handleDestinationUrlChange(segmentId: string, value: string) {
    const formattedUrl = formatDestinationUrl(value);
    setDestinationUrls((prev) =>
      prev.map((url) =>
        url.adSegmentId === segmentId ? { ...url, url: formattedUrl || value } : url,
      ),
    );
  }

  // Updated total experiments calculation using per-segment audience counts
  const totalExperiments = useMemo(() => {
    if (!segmentsQuery.data) return 0;

    let total = 0;
    const isConversationAd =
      campaignGroupQuery.data?.adFormat.type === "SPONSORED_INMAIL";
    const creativeCount = creativesQuery.data?.length || 1;

    segmentsQuery.data.forEach((segment) => {
      const audienceCount = audienceCountMap[segment.id] || 0;

      if (isConversationAd) {
        const subjectCount = 5;
        total += audienceCount * subjectCount;
      } else {
        const valuePropsCount = valuePropsCountMap[segment.id] || 0;
        total += audienceCount * valuePropsCount * creativeCount;
      }
    });

    return total;
  }, [
    segmentsQuery.data,
    creativesQuery.data,
    valuePropsCountMap,
    audienceCountMap,
    campaignGroupQuery.data?.adFormat.type,
  ]);



  // If success screen should be shown, render it instead of the preview
  if (showSuccessScreen) {
    return (
      <SuccessScreen
        adAccount={adAccount}
        campaignGroupId={campaignGroupId}
        campaignName={campaignGroupQuery.data?.title}
        startDate={campaignGroupQuery.data?.startDatetime.toISOString()}
        endDate={
          campaignGroupQuery.data?.type == "EVERGREEN"
            ? undefined
            : !campaignGroupQuery.data?.endDatetime
              ? undefined
              : campaignGroupQuery.data.endDatetime.toISOString()
        }
      />
    );
  }

  return (
    <>
      <div className="flex h-[calc(100vh-64px)] w-full flex-col items-start justify-start space-y-4 overflow-auto px-4 pb-20 pt-4">
        {/* Header */}
        <div className="flex w-full items-center justify-between border-b pb-4">
          <div>
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="icon"
                asChild
                className="h-9 w-9 transition-all hover:bg-gray-100"
              >
                <Link
                  href={
                    adProgram.data?.adFormat.type == "SPONSORED_INMAIL"
                      ? `/advertising/adAccounts/${adAccount}/campaignGroups/${campaignGroupId}/conversation-ad`
                      : `/advertising/adAccounts/${adAccount}/campaignGroups/${campaignGroupId}/assets`
                  }
                >
                  <ArrowLeft className="h-5 w-5" />
                </Link>
              </Button>
              <h1 className="text-2xl font-semibold tracking-tight">
                Preview & Launch
              </h1>
            </div>
            <p className="ml-12 mt-1 text-sm text-muted-foreground">
              {campaignGroupQuery.data?.type === "EVERGREEN" ||
                !campaignGroupQuery.data?.endDatetime ? (
                <>
                  Evergreen campaign starts on{" "}
                  <span className="font-semibold">
                    {campaignGroupQuery.data?.startDatetime &&
                      new Date(
                        campaignGroupQuery.data.startDatetime,
                      ).toLocaleDateString()}
                  </span>
                </>
              ) : (
                <>
                  Campaign running from{" "}
                  <span className="font-semibold">
                    {campaignGroupQuery.data?.startDatetime &&
                      new Date(
                        campaignGroupQuery.data.startDatetime,
                      ).toLocaleDateString()}{" "}
                    to{" "}
                    {campaignGroupQuery.data?.endDatetime &&
                      new Date(
                        campaignGroupQuery.data.endDatetime,
                      ).toLocaleDateString()}
                  </span>
                </>
              )}
            </p>
          </div>

          {/* Total Budget Input */}
          <div className="w-64">
            <label
              htmlFor="total-budget"
              className="mb-1 block text-sm font-medium text-gray-700"
            >
              {campaignGroupQuery.data?.type == "EVERGREEN"
                ? "Monthly Budget"
                : "Total Budget"}
            </label>
            <div className="relative">
              <span className="absolute left-3 top-1/2 -translate-y-1/2 text-base font-medium text-gray-700">
                $
              </span>
              <Input
                id="total-budget"
                type="text"
                value={
                  campaignGroupQuery.data?.type == "EVERGREEN"
                    ? formatNumber(campaignGroupQuery.data.monthlyBudget || 0)
                    : formatNumber(campaignGroupQuery.data?.totalBudget || 0)
                }
                disabled
                className="h-11 bg-gray-50 pl-8 text-lg font-medium"
              />
            </div>
          </div>
        </div>
        {/* Segments */}
        {segmentsQuery.isLoading ? (
          <div className="flex h-40 w-full items-center justify-center rounded-lg border bg-gray-50">
            <div className="flex items-center gap-2 text-base">
              <ReloadIcon className="h-5 w-5 animate-spin text-blue-500" />
              <span>Loading segments...</span>
            </div>
          </div>
        ) : (
          <div className="w-full space-y-6">
            {segmentsQuery.data?.map((segment, index) => (
              <SegmentCard
                key={segment.id}
                segment={segment}
                index={index}
                campaignGroupQuery={campaignGroupQuery}
                destinationUrls={destinationUrls}
                handleDestinationUrlChange={handleDestinationUrlChange}
                destinationUrlError={destinationUrlError}
                campaignGroupSegmentBudgets={campaignGroupSegmentBudgets}
                handleBudgetChange={handleBudgetChange}
                budgetError={budgetError}
                handleAudienceCountChange={handleAudienceCountChange}
                audienceCountMap={audienceCountMap}
                handleValuePropsCountChange={handleValuePropsCountChange}
                valuePropsCountMap={valuePropsCountMap}
                creativesQuery={creativesQuery}
                refetchInterval={refetchInterval}
              />
            ))}
          </div>
        )}
        {/* Lead generation sender selection */}
        {campaignGroupQuery.data?.adFormat.type === "SPONSORED_INMAIL" && (
          <Card className="w-full border border-gray-200 shadow-sm">
            <CardHeader className="bg-gray-50 px-6 py-5">
              <CardTitle className="text-lg font-medium text-gray-800">
                Lead Form Sender
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <div className="space-y-2">
                <label
                  htmlFor="sender-select"
                  className="mb-1 block text-sm font-medium text-gray-700"
                >
                  Select who will receive lead notifications
                </label>
                <Select
                  value={sender}
                  onValueChange={(value) => {
                    setSender(value);
                    setSenderError(false);
                  }}
                >
                  <SelectTrigger
                    id="sender-select"
                    className={`h-10 ${senderError ? "border-red-500 ring-1 ring-red-500" : "border-gray-300"}`}
                  >
                    <SelectValue placeholder="Select a sender" />
                  </SelectTrigger>
                  <SelectContent>
                    {senderQuery.data?.map((s) => (
                      <SelectItem key={s.urn} value={s.urn}>
                        {s.firstName} {s.lastName}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {senderError && (
                  <p className="mt-1 text-xs text-red-600">
                    Please select a sender for lead forms
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        )}
        <div className="h-64" aria-hidden="true"></div>
      </div>

      <NavigationFooter
        onPrevious={() =>
          adProgram.data?.adFormat.type == "SPONSORED_INMAIL"
            ? router.push(
              `/advertising/adAccounts/${adAccount}/campaignGroups/${campaignGroupId}/conversation-ad`,
            )
            : router.push(
              `/advertising/adAccounts/${adAccount}/campaignGroups/${campaignGroupId}/assets`,
            )
        }
        onNext={onSubmit}
        nextText={isDeployed ? "Deployed" : "Launch Campaign"}
        nextDisabled={isSubmitting || isDeployed}
        nextLoading={isSubmitting}
      />
    </>
  );
}

function formatNumber(num: number): string {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

type AdSegment = {
  id: string;
  segmentId: string;
  adProgramId: string;
  ready: boolean;
};

interface SegmentCardProps {
  segment: AdSegment;
  index: number;
  campaignGroupQuery: UseTRPCQueryResult<
    {
      id: string;
      type: "EVERGREEN" | "EVENT_DRIVEN";
      title: string;
      linkedInAdAccountId: string;
      startDatetime: Date;
      adFormat: {
        type: string;
        format: string;
      };
      objectiveType: string;
      endDatetime?: Date | null;
      totalBudget?: number | null;
      monthlyBudget?: number | null;
    } | null,
    any
  >; // Match actual tRPC return type
  destinationUrls: Array<{
    adSegmentId: string;
    url: string;
  }>;
  handleDestinationUrlChange: (segmentId: string, value: string) => void;
  destinationUrlError: string[];
  campaignGroupSegmentBudgets: Record<string, number>;
  handleBudgetChange: (segmentId: string, value: string) => void;
  budgetError: boolean;
  handleAudienceCountChange: (segmentId: string, count: number) => void;
  audienceCountMap: Record<string, number>;
  handleValuePropsCountChange: (segmentId: string, count: number) => void;
  valuePropsCountMap: Record<string, number>;
  creativesQuery: UseTRPCQueryResult<
    Array<{
      id: string;
      linkedInAdProgramId: string;
      adCreativeId: string;
    }>,
    any
  >;
  refetchInterval?: number;
}
function SegmentCard({
  segment,
  index,
  campaignGroupQuery,
  destinationUrls,
  handleDestinationUrlChange,
  destinationUrlError,
  campaignGroupSegmentBudgets,
  handleBudgetChange,
  budgetError,
  handleAudienceCountChange,
  audienceCountMap,
  handleValuePropsCountChange,
  valuePropsCountMap,
  creativesQuery,
  refetchInterval,
}: SegmentCardProps) {
  const segmentQuery = api.v2.core.segment.getSegment.useQuery({
    segmentId: segment.segmentId,
  });

  const adSegmentValuePropsQuery =
    api.v2.ads.adSegmentValueProp.getAdSegmentValuePropsForAdSegment.useQuery({
      adSegmentId: segment.id,
      status: "DRAFT",
    });

  const adSegmentValuePropCreatives =
    api.v2.ads.adSegmentValuePropCreative.getForAdSegment.useQuery({
      adSegmentId: segment.id,
    });

  return (
    <Card className="overflow-hidden border border-gray-200 shadow-sm transition-all hover:shadow-md">
      {/* Segment Header */}
      <CardHeader className="bg-gray-50 px-6 py-5">
        <div className="flex items-center justify-between">
          <div>
            <p className="mb-1 text-xs font-normal text-gray-500">
              Segment {index + 1}
            </p>
            <h3 className="text-lg font-medium text-gray-800">
              {segmentQuery.data && (
                <SegmentDetails row={{ original: segmentQuery.data }} />
              )}
            </h3>
          </div>

          <div className="w-52">
            <label
              htmlFor={`budget-${segment.id}`}
              className="mb-1 block text-sm font-medium text-gray-600"
            >
              Segment Budget
            </label>
            <div className="relative">
              <span className="absolute left-3 top-1/2 -translate-y-1/2 text-base font-medium text-gray-700">
                $
              </span>
              <Input
                id={`budget-${segment.id}`}
                type="text"
                value={formatNumber(
                  campaignGroupSegmentBudgets[segment.id] || 0,
                )}
                onChange={(e) =>
                  handleBudgetChange(
                    segment.id,
                    e.target.value.replace(/,/g, ""),
                  )
                }
                className="h-10 border-gray-300 pl-8 text-lg font-medium focus:border-blue-500 focus:ring-blue-500"
              />
            </div>
            {budgetError && (
              <p className="mt-1 text-xs text-red-600">
                Total budget must equal campaign budget
              </p>
            )}
          </div>
        </div>
      </CardHeader>

      <Separator className="h-px bg-gray-200" />

      <CardContent className="p-6">
        <div className="space-y-6">
          {/* Destination URL - only for non-lead generation campaigns */}
          {campaignGroupQuery.data?.objectiveType !== "LEAD_GENERATION" && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <label
                  htmlFor={`url-${segment.id}`}
                  className="text-sm font-medium text-gray-700"
                >
                  Destination URL
                </label>
                <div className="w-full max-w-md">
                  <Input
                    id={`url-${segment.id}`}
                    type="url"
                    placeholder="https://example.com/landing-page"
                    value={
                      destinationUrls.find(
                        (url) => url.adSegmentId === segment.id,
                      )?.url || ""
                    }
                    onChange={(e) =>
                      handleDestinationUrlChange(segment.id, e.target.value)
                    }
                    className={`border-gray-300 ${destinationUrlError.includes(segment.id)
                      ? "border-red-500 ring-1 ring-red-500"
                      : ""
                      }`}
                  />
                  {destinationUrlError.includes(segment.id) && (
                    <p className="mt-1 text-xs text-red-600">
                      Please enter a valid URL
                    </p>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Ad Experiments */}
          <div className="flex items-center gap-4">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-50 text-2xl font-semibold text-blue-600">
              {(() => {
                const audienceCount = audienceCountMap[segment.id] || 0;
                const isConversationAd =
                  campaignGroupQuery.data?.adFormat.type === "SPONSORED_INMAIL";

                if (isConversationAd) {
                  return audienceCount * 5; // 5 subjects
                } else {
                  const valuePropCreativeCount =
                    adSegmentValuePropCreatives.data?.length || 0;
                  return audienceCount * valuePropCreativeCount;
                }
              })() || "..."}
            </div>
            <div>
              <p className="text-base font-medium text-gray-800">
                Ads being tested to maximize conversions
              </p>
              {/* <p className="text-sm text-gray-500">
                Est.{" "}
                {campaignGroupQuery.data?.endDatetime &&
                campaignGroupQuery.data.startDatetime
                  ? `${Math.ceil((campaignGroupQuery.data.endDatetime.getTime() - campaignGroupQuery.data.startDatetime.getTime()) / (1000 * 60 * 60 * 24 * 30))} Month`
                  : "1 Month"}
              </p> */}
            </div>
          </div>

          <div className="flex flex-wrap gap-3">
            {/* Always show Audiences */}
            <div className="flex items-center gap-2 rounded-full border border-gray-200 bg-gray-50 px-4 py-2 text-sm">
              <span className="h-2.5 w-2.5 rounded-full bg-blue-500"></span>
              <AudienceCount
                segmentId={segment.id}
                onCountChange={(count: number) =>
                  handleAudienceCountChange(segment.id, count)
                }
                currentCount={audienceCountMap[segment.id] || 0}
              />
            </div>

            {/* Conditional pills based on ad format */}
            {campaignGroupQuery.data?.adFormat.type === "SPONSORED_INMAIL" ? (
              /* For Conversation Ads */
              <div className="flex items-center gap-2 rounded-full border border-gray-200 bg-gray-50 px-4 py-2 text-sm">
                <span className="h-2.5 w-2.5 rounded-full bg-blue-500"></span>
                <span>5 Subjects</span>
              </div>
            ) : (
              /* For Sponsored Content */
              <>
                <div className="flex items-center gap-2 rounded-full border border-gray-200 bg-gray-50 px-4 py-2 text-sm">
                  <span className="h-2.5 w-2.5 rounded-full bg-blue-500"></span>
                  <span>
                    {adSegmentValuePropCreatives.data
                      ? Object.keys(
                        adSegmentValuePropCreatives.data?.reduce(
                          (acc, creative) => {
                            if (!acc[creative.adProgramCreativeId]) {
                              acc[creative.adProgramCreativeId] = [];
                            }
                            acc[creative.adProgramCreativeId]!.push(creative);
                            return acc;
                          },
                          {} as Record<
                            string,
                            typeof adSegmentValuePropCreatives.data
                          >,
                        ),
                      ).length
                      : 0}{" "}
                    Creatives
                  </span>
                </div>
                <div className="flex items-center gap-2 rounded-full border border-gray-200 bg-gray-50 px-4 py-2 text-sm">
                  <span className="h-2.5 w-2.5 rounded-full bg-blue-500"></span>
                  <ValuePropositionsCount
                    segmentId={segment.id}
                    onCountChange={(count: number) =>
                      handleValuePropsCountChange(segment.id, count)
                    }
                    currentCount={valuePropsCountMap[segment.id] || 0}
                  />
                </div>
              </>
            )}
          </div>

          <div className="flex items-center gap-3 rounded-lg bg-gray-50 p-4">
            <span className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 text-lg text-blue-600">
              💬
            </span>
            <p className="text-sm text-gray-600">
              We'll notify you after these tests have completed with the next
              batch of ads.
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Fixed ValuePropositionsCount component that prevents infinite update cycles
function ValuePropositionsCount({
  segmentId,
  onCountChange,
  currentCount,
}: {
  segmentId: string;
  onCountChange: (count: number) => void;
  currentCount: number;
}) {
  const valuePropsQuery =
    api.v2.ads.adSegmentValueProp.getAdSegmentValuePropsForAdSegment.useQuery({
      adSegmentId: segmentId,
      status: "DRAFT",
    });

  useEffect(() => {
    if (valuePropsQuery.data) {
      const newCount = valuePropsQuery.data.length;
      if (newCount !== currentCount) {
        onCountChange(newCount);
      }
    }
  }, [valuePropsQuery.data, segmentId, onCountChange, currentCount]);

  return (
    <span>
      {valuePropsQuery.isLoading
        ? "Loading..."
        : `${valuePropsQuery.data?.length || 0} Value Propositions`}
    </span>
  );
}

function AudienceCount({
  segmentId,
  onCountChange,
  currentCount,
}: {
  segmentId: string;
  onCountChange: (count: number) => void;
  currentCount: number;
}) {
  const audienceQuery = api.v2.ads.adAudience.getAllForAdSegment.useQuery(
    {
      adSegmentId: segmentId,
    },
    {
      enabled: !!segmentId,
    },
  );

  useEffect(() => {
    if (audienceQuery.data) {
      const newCount = audienceQuery.data.length;
      if (newCount !== currentCount) {
        onCountChange(newCount);
      }
    }
  }, [audienceQuery.data, segmentId, onCountChange, currentCount]);

  return (
    <span>
      {audienceQuery.isLoading
        ? "Loading..."
        : `${audienceQuery.data?.filter((audience) => audience.toBeUsed == true).length || 0} Audiences`}
    </span>
  );
}

function SegmentDetails({
  row,
}: {
  row: {
    original: {
      name?: string | null;
      jobFunction?: string | null;
      jobSeniority?: string | null;
      verticals?: string[] | null;
      annualContractValueLowBound?: number | null;
      annualContractValueHighBound?: number | null;
      annualRevenueLowBound?: number | null;
      annualRevenueHighBound?: number | null;
      numberOfEmployeesLowBound?: number | null;
      numberOfEmployeesHighBound?: number | null;
    };
  };
}) {
  if (!row.original.name) {
    const nameArray: string[] = [];

    if (row.original.jobFunction) {
      nameArray.push(snakeCaseToWords(row.original.jobFunction));
    }
    if (row.original.jobSeniority) {
      nameArray.push(snakeCaseToWords(row.original.jobSeniority));
    }

    if (row.original.verticals) {
      nameArray.push(...row.original.verticals);
    }

    if (
      row.original.annualContractValueLowBound &&
      row.original.annualContractValueHighBound
    ) {
      nameArray.push(
        `$${row.original.annualContractValueLowBound
          .toString()
          .replace(
            /\B(?=(\d{3})+(?!\d))/g,
            ",",
          )}-$${row.original.annualContractValueHighBound
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} ACV`,
      );
    }
    if (
      row.original.annualContractValueLowBound &&
      !row.original.annualContractValueHighBound
    ) {
      nameArray.push(
        `$${row.original.annualContractValueLowBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")}+ ACV`,
      );
    }
    if (
      !row.original.annualContractValueLowBound &&
      row.original.annualContractValueHighBound
    ) {
      nameArray.push(
        `<$${row.original.annualContractValueHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} ACV`,
      );
    }

    if (
      row.original.annualRevenueLowBound &&
      row.original.annualRevenueHighBound
    ) {
      nameArray.push(
        `$${row.original.annualRevenueLowBound
          .toString()
          .replace(
            /\B(?=(\d{3})+(?!\d))/g,
            ",",
          )}-$${row.original.annualRevenueHighBound
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} prospect revenue`,
      );
    }
    if (
      row.original.annualRevenueLowBound &&
      !row.original.annualRevenueHighBound
    ) {
      nameArray.push(
        `$${row.original.annualRevenueLowBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")}+ prospect revenue`,
      );
    }
    if (
      !row.original.annualRevenueLowBound &&
      row.original.annualRevenueHighBound
    ) {
      nameArray.push(
        `<$${row.original.annualRevenueHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} prospect revenue`,
      );
    }

    if (
      row.original.numberOfEmployeesLowBound &&
      row.original.numberOfEmployeesHighBound
    ) {
      nameArray.push(
        `${row.original.numberOfEmployeesLowBound
          .toString()
          .replace(
            /\B(?=(\d{3})+(?!\d))/g,
            ",",
          )}-${row.original.numberOfEmployeesHighBound
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} employees`,
      );
    }
    if (
      row.original.numberOfEmployeesLowBound &&
      !row.original.numberOfEmployeesHighBound
    ) {
      nameArray.push(
        `${row.original.numberOfEmployeesLowBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")}+ employees`,
      );
    }
    if (
      !row.original.numberOfEmployeesLowBound &&
      row.original.numberOfEmployeesHighBound
    ) {
      nameArray.push(
        `<${row.original.numberOfEmployeesHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} employees`,
      );
    }

    if (nameArray.length == 0) {
      return "All";
    }
    return nameArray.join(" • ");
  }
  return row.original.name;
}
