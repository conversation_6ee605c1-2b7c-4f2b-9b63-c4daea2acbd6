import React, { forwardRef, useImperativeHandle, useRef } from "react";

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@kalos/ui/card";

import { AdText } from "./AdText";

interface AdCardProps {
  valuePropId: string;
  valueProp: string;
  adSegmentId: string;
  adAccountId: string;
}

export const AdCard = forwardRef<
  { saveAdCopy: () => Promise<void> },
  AdCardProps
>(function AdCard({ valuePropId, valueProp, adSegmentId, adAccountId }, ref) {
  const adTextRef = useRef<{ saveAdCopy: () => Promise<void> }>(null);

  useImperativeHandle(ref, () => ({
    saveAdCopy: async () => {
      if (adTextRef.current?.saveAdCopy) {
        await adTextRef.current.saveAdCopy();
      }
    },
  }));

  return (
    <Card>
      <CardHeader>
        <CardTitle>{valueProp}</CardTitle>
      </CardHeader>
      <CardContent className="h-full">
        <AdText
          ref={adTextRef}
          valuePropId={valuePropId}
          adSegmentId={adSegmentId}
          adAccountId={adAccountId}
        />
      </CardContent>
    </Card>
  );
});
