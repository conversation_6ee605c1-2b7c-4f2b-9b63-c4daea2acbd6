"use client";

import { useEffect, useRef, useState } from "react";
import {
  AudienceCount,
  AudienceHeader,
  AudienceTargetingDialog,
  AudienceTargets,
  compareTargetingCriteria,
  ExcludedTargets,
  handleAddCriteria,
  handleAddExcludeCriteria,
} from "@/app/(customer)/(onboarded)/advertising/adAccounts/[adAccount]/campaignGroups/[campaignGroupId]/audience/page";
import { AudienceTargetingCriteria } from "@/app/(customer)/(onboarded)/advertising/adAccounts/[adAccount]/campaignGroups/[campaignGroupId]/audience/types/audience";
import { api } from "@/trpc/client";
import { set } from "date-fns";
import { XIcon } from "lucide-react";

interface AudienceProps {
  adSegmentId: string;
  adAccount: string;
  adProgramId: string;
  prefetchedAudiences?: {
    id: string;
    linkedInAdSegmentId: string;
    toBeUsed: boolean;
    audiencePopulated: boolean;
    targetingCriteria: any;
  }[];
  prefetchedFacets?: {
    adTargetingFacetUrn: string;
    facetName: string;
    availableEntityFinders: ("TYPEAHEAD" | "AD_TARGETING_FACET")[];
  }[];
}

export function Audience({
  adSegmentId,
  adAccount,
  adProgramId,
  prefetchedAudiences,
  prefetchedFacets,
}: AudienceProps) {
  const apiUtils = api.useUtils();

  // Only query if we don't have prefetched data
  const audiencesQuery = api.v2.ads.adAudience.getAllForAdSegment.useQuery(
    {
      adSegmentId,
    },
    {
      enabled: !prefetchedAudiences,
    },
  );

  // Use prefetched data if available, otherwise use query data
  const audiences = prefetchedAudiences || audiencesQuery.data;

  return (
    <div className="space-y-4">
      {audiences
        ?.filter((audience) => audience.toBeUsed)
        .map((audience, index) => (
          <AudienceItem
            key={audience.id}
            audience={audience}
            index={index}
            prefetchedFacets={prefetchedFacets}
          />
        ))}
    </div>
  );
}

interface AudienceItemProps {
  audience: {
    id: string;
    linkedInAdSegmentId: string;
    toBeUsed: boolean;
    audiencePopulated: boolean;
    audienceTargetingCriteria?: any;
    targetingCriteria?: any;
  };
  index: number;
  prefetchedFacets?: {
    adTargetingFacetUrn: string;
    facetName: string;
    availableEntityFinders: ("TYPEAHEAD" | "AD_TARGETING_FACET")[];
  }[];
}

function AudienceItem({
  audience,
  index,
  prefetchedFacets,
}: AudienceItemProps) {
  const [isTargetingDialogOpen, setIsTargetingDialogOpen] = useState(false);
  const apiUtils = api.useUtils();
  const [targetingCriteria, setTargetingCriteria] =
    useState<AudienceTargetingCriteria | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [editingInProgress, setEditingInProgress] = useState(false);

  // Prefetch audience details
  const audienceDetailsQuery = api.v2.ads.adAudience.getOne.useQuery({
    id: audience.id,
  });

  useEffect(() => {
    console.log("targetingCriteria", targetingCriteria);
    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    if (!audienceDetailsQuery.data?.audienceTargetCriteria) {
      return;
    }
    if (!targetingCriteria) {
      setTargetingCriteria(audienceDetailsQuery.data?.audienceTargetCriteria);
      return;
    }
    if (
      compareTargetingCriteria(
        targetingCriteria,
        audienceDetailsQuery.data.audienceTargetCriteria,
      )
    ) {
      console.log("No changes detected");
      return;
    }
    setEditingInProgress(true);
    // Create a new timeout that will trigger after 10 seconds
    timeoutRef.current = setTimeout(() => {
      // Only run if the data still differs from server data
      if (
        targetingCriteria &&
        audienceDetailsQuery.data?.audienceTargetCriteria &&
        !compareTargetingCriteria(
          targetingCriteria,
          audienceDetailsQuery.data.audienceTargetCriteria,
        )
      ) {
        console.log("Detected unsaved changes after 10 seconds of inactivity", {
          local: JSON.stringify(targetingCriteria, null, 2),
          server: JSON.stringify(
            audienceDetailsQuery.data.audienceTargetCriteria,
            null,
            2,
          ),
        });

        // Update the server with our local changes
        updateAudienceCompleteMutation.mutate({
          id: audience.id,
          targetCriteria: targetingCriteria,
        });
      }
      setEditingInProgress(false);
    }, 2500); // Wait 10 seconds

    // Clean up function
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [targetingCriteria, audienceDetailsQuery.data]);

  // Prefetch audience count
  const audienceCountQuery = api.v2.ads.linkedInApi.getAudienceCount.useQuery(
    {
      targetingCriteria: targetingCriteria,
    },
    {
      enabled: !!targetingCriteria,
    },
  );

  // Only query facets if we don't have prefetched data
  const facetsQuery =
    api.v2.ads.linkedInApi.adTargeting.getAdTargetingFacets.useQuery(
      undefined,
      {
        enabled: !prefetchedFacets,
      },
    );

  // Use prefetched facets if available
  const facets = prefetchedFacets || facetsQuery.data;

  const updateAudienceCompleteMutation =
    api.v2.ads.adAudience.updateTargetCriteriaComplete.useMutation({
      onSuccess: async () => {
        await apiUtils.v2.ads.adAudience.invalidate();
      },
    });

  // Function to handle dialog open with prefetching
  const handleOpenDialog = async () => {
    // Ensure all data is prefetched before opening dialog
    await Promise.all([
      apiUtils.v2.ads.adAudience.getOne.prefetch({ id: audience.id }),
      apiUtils.v2.ads.linkedInApi.getAudienceCount.prefetch({
        targetingCriteria: audience.audienceTargetingCriteria,
      }),
      apiUtils.v2.ads.linkedInApi.adTargeting.getAdTargetingFacets.prefetch(),
    ]);

    setIsTargetingDialogOpen(true);
  };

  if (!targetingCriteria) {
    return null;
  }

  return (
    <div className="rounded-lg border p-4">
      <div className="mb-4 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <AudienceHeader
            audienceTargetingCriteria={targetingCriteria}
            audienceIndex={index + 1}
          />
        </div>
        <AudienceTargetingDialog
          audienceId={audience.id}
          isOpen={isTargetingDialogOpen}
          setIsOpen={setIsTargetingDialogOpen}
          onAddIncludeSubmit={(data) => {
            const currentTargetCriteria = structuredClone(targetingCriteria);
            handleAddCriteria(
              data,
              currentTargetCriteria,
              setTargetingCriteria,
            );
          }}
          onAddExcludeSubmit={(data) => {
            const currentTargetCriteria = structuredClone(targetingCriteria);
            handleAddExcludeCriteria(
              data,
              currentTargetCriteria,
              setTargetingCriteria,
            );
          }}
          disabled={!audience.toBeUsed}
          onOpen={handleOpenDialog}
        />
      </div>
      <AudienceCount
        targetingCriteria={targetingCriteria}
        editingInProgress={false}
      />
      <AudienceTargets
        audienceId={audience.id}
        targetingCriteria={targetingCriteria}
        setAudienceTargetingCriteria={setTargetingCriteria}
      />
      {targetingCriteria.exclude && (
        <ExcludedTargets
          audienceId={audience.id}
          targetingCriteria={targetingCriteria}
          setAudienceTargetingCriteria={setTargetingCriteria}
        />
      )}
    </div>
  );
}