"use client";

import type {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
} from "@tanstack/react-table";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { snakeCaseToWords } from "@/app/utils/snakeCaseToWords";
import { useSegmentsActions, useSegmentsState } from "@/hooks/useCampaignStore";
import { api } from "@/trpc/client";
import { ScrollArea } from "@radix-ui/react-scroll-area";
import {
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import {
  Filter,
  X,
} from "lucide-react";

import { Badge } from "@kalos/ui/badge";
import { Button } from "@kalos/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@kalos/ui/card";
import { Checkbox } from "@kalos/ui/checkbox";
import { DataTable } from "@kalos/ui/table/datatable";
import { NavigationFooter } from "@/components/ui/navigation-footer";

interface Segment {
  id: string;
  name?: string | null;
  verticals: string[];
  annualRevenueLowBound?: number | null;
  annualRevenueHighBound?: number | null;
  numberOfEmployeesLowBound?: number | null;
  numberOfEmployeesHighBound?: number | null;
  annualContractValueLowBound?: number | null;
  annualContractValueHighBound?: number | null;
  jobFunction?: string | null;
  jobSeniority?: string | null;
  industry?: string | null;
  department?: string | null;
  revenue?: string | null;
}
interface SegmentWithStats extends Segment {
  numberOfAccounts?: string | null;
  averageAcv?: string | null;
  totalAcv?: string | null;
  winRate?: string;
  averageDealLength?: string;
}

interface SelectedSegmentProps {
  segment: Segment;
  removeSegment: (id: string) => void;
}

function SelectedSegmentItem({ segment, removeSegment }: SelectedSegmentProps) {
  const { primary, secondary } = getSegmentDisplayDetails(segment);

  return (
    <div className="mb-2 flex w-full items-center justify-between rounded-md border border-gray-200 bg-white p-3">
      <div className="flex flex-col">
        <span className="text-sm font-medium">{primary}</span>
        {secondary && (
          <span className="text-xs text-muted-foreground">{secondary}</span>
        )}
      </div>
      <Button
        variant="ghost"
        size="icon"
        className="h-6 w-6 rounded-full hover:bg-muted-foreground/20"
        onClick={() => removeSegment(segment.id)}
      >
        <X className="h-4 w-4" />
        <span className="sr-only">Remove</span>
      </Button>
    </div>
  );
}

export function SelectSegments({
  data,
  adAccount,
  campaignGroupId,
}: {
  data: Segment[];
  adAccount: string;
  campaignGroupId: string;
}) {
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);

  const orgId = api.v2.core.user.getUser.useQuery();
  const nowDate = new Date(Date.now());
  nowDate.setMonth(nowDate.getMonth() - 12);
  const [dateRange, setDateRange] = useState<{
    startDate?: Date;
    endDate?: Date;
  }>({ startDate: nowDate, endDate: undefined });

  // Use custom hooks for segments state and actions
  const { selectedSegments } = useSegmentsState();
  const { setSegments, addSegment, removeSegment } = useSegmentsActions();
  const [isLoading, setIsLoading] = useState(false);

  const [disableCheckboxes, setDisableCheckboxes] = useState<boolean>(true);

  const campaignGroupSegmentsQuery =
    api.v2.ads.adSegment.getAllAdSegmentsForAdProgram.useQuery({
      adProgramId: campaignGroupId,
    });

  useEffect(() => {
    if (campaignGroupSegmentsQuery.data) {
      setDisableCheckboxes(false);
      // Update the store with segments from the API
      setSegments(
        campaignGroupSegmentsQuery.data.map(
          (campaignGroupSegment) => campaignGroupSegment.segmentId,
        ),
      );
    }
  }, [campaignGroupSegmentsQuery.data, setSegments]);

  // Set loading state
  useEffect(() => {
    setIsLoading(campaignGroupSegmentsQuery.isLoading);
  }, [campaignGroupSegmentsQuery.isLoading]);

  const columns: ColumnDef<Segment>[] = [
    {
      accessorKey: "id",
      header: undefined,
      cell: ({ row }) => {
        return (
          <Checkbox
            disabled={disableCheckboxes}
            checked={selectedSegments.includes(row.original.id)}
            onCheckedChange={(checked) => {
              return checked
                ? addSegment(row.original.id)
                : removeSegment(row.original.id);
            }}
          />
        );
      },
    },
    {
      header: "Segment",
      cell: ({ row }) => {
        if (!row.original.name) {
          const nameArray: string[] = [];

          if (row.original.jobFunction) {
            nameArray.push(snakeCaseToWords(row.original.jobFunction));
          }
          if (row.original.jobSeniority) {
            nameArray.push(snakeCaseToWords(row.original.jobSeniority));
          }

          if (row.original.verticals) {
            nameArray.push(row.original.verticals.join(", "));
          }

          if (
            row.original.annualContractValueLowBound &&
            row.original.annualContractValueHighBound
          ) {
            nameArray.push(
              `$${row.original.annualContractValueLowBound
                .toString()
                .replace(
                  /\B(?=(\d{3})+(?!\d))/g,
                  ",",
                )}-$${row.original.annualContractValueHighBound
                  .toString()
                  .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} ACV`,
            );
          }
          if (
            row.original.annualContractValueLowBound &&
            !row.original.annualContractValueHighBound
          ) {
            nameArray.push(
              `$${row.original.annualContractValueLowBound
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ",")}+ ACV`,
            );
          }
          if (
            !row.original.annualContractValueLowBound &&
            row.original.annualContractValueHighBound
          ) {
            nameArray.push(
              `<$${row.original.annualContractValueHighBound
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} ACV`,
            );
          }

          if (
            row.original.annualRevenueLowBound &&
            row.original.annualRevenueHighBound
          ) {
            nameArray.push(
              `$${row.original.annualRevenueLowBound
                .toString()
                .replace(
                  /\B(?=(\d{3})+(?!\d))/g,
                  ",",
                )}-$${row.original.annualRevenueHighBound
                  .toString()
                  .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} prospect revenue`,
            );
          }
          if (
            row.original.annualRevenueLowBound &&
            !row.original.annualRevenueHighBound
          ) {
            nameArray.push(
              `$${row.original.annualRevenueLowBound
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ",")}+ prospect revenue`,
            );
          }
          if (
            !row.original.annualRevenueLowBound &&
            row.original.annualRevenueHighBound
          ) {
            nameArray.push(
              `<$${row.original.annualRevenueHighBound
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} prospect revenue`,
            );
          }

          if (
            row.original.numberOfEmployeesLowBound &&
            row.original.numberOfEmployeesHighBound
          ) {
            nameArray.push(
              `${row.original.numberOfEmployeesLowBound
                .toString()
                .replace(
                  /\B(?=(\d{3})+(?!\d))/g,
                  ",",
                )}-${row.original.numberOfEmployeesHighBound
                  .toString()
                  .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} employees`,
            );
          }
          if (
            row.original.numberOfEmployeesLowBound &&
            !row.original.numberOfEmployeesHighBound
          ) {
            nameArray.push(
              `${row.original.numberOfEmployeesLowBound
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ",")}+ employees`,
            );
          }
          if (
            !row.original.numberOfEmployeesLowBound &&
            row.original.numberOfEmployeesHighBound
          ) {
            nameArray.push(
              `<${row.original.numberOfEmployeesHighBound
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} employees`,
            );
          }

          let acvQueryParam = "";
          if (
            row.original.annualContractValueHighBound == 20000 &&
            row.original.annualContractValueLowBound == undefined
          ) {
            acvQueryParam = "1";
          } else if (
            row.original.annualContractValueHighBound == 50000 &&
            row.original.annualContractValueLowBound == 20000
          ) {
            acvQueryParam = "2";
          } else if (
            row.original.annualContractValueHighBound == 100000 &&
            row.original.annualContractValueLowBound == 50000
          ) {
            acvQueryParam = "3";
          } else if (
            row.original.annualContractValueHighBound == undefined &&
            row.original.annualContractValueLowBound == 100000
          ) {
            acvQueryParam = "4";
          }

          let empoyeesQueryParam = "";
          if (
            row.original.numberOfEmployeesHighBound == 100 &&
            row.original.numberOfEmployeesLowBound == undefined
          ) {
            empoyeesQueryParam = "1";
          } else if (
            row.original.numberOfEmployeesHighBound == 500 &&
            row.original.numberOfEmployeesLowBound == 100
          ) {
            empoyeesQueryParam = "2";
          } else if (
            row.original.numberOfEmployeesHighBound == undefined &&
            row.original.numberOfEmployeesLowBound == 500
          ) {
            empoyeesQueryParam = "3";
          }

          let prospectRevenue = "";
          if (
            row.original.annualRevenueLowBound == 1000000 &&
            row.original.annualRevenueHighBound == 10000000
          ) {
            prospectRevenue = "1";
          } else if (
            row.original.annualRevenueLowBound == 10000000 &&
            row.original.annualRevenueHighBound == 200000000
          ) {
            prospectRevenue = "2";
          } else if (
            row.original.annualRevenueLowBound == 200000000 &&
            row.original.annualRevenueHighBound == undefined
          ) {
            prospectRevenue = "3";
          }

          if (nameArray.length == 0) {
            return (
              <div className="space-y-1">
                <span className="w-[80px]">All</span>
              </div>
            );
          }
          return (
            <div className="w-[380px] space-y-1 text-wrap ">
              <span className="50 w-[380px] text-wrap">
                {nameArray.join(" • ")}
              </span>
            </div>
          );
        }
        return (
          <div className="space-y-1">
            <span className="w-[80px]">{row.original.name}</span>
          </div>
        );
      },
    },
  ];
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [sorting, setSorting] = useState<SortingState>([]);
  const apiUtils = api.useUtils();
  const router = useRouter();

  const createCampaign =
    api.v2.ads.adSegment.setAdSegmentsForAdProgram.useMutation({
      onSuccess: async (data) => {
        await apiUtils.v2.ads.invalidate();
        router.push(
          `/advertising/adAccounts/${adAccount}/campaignGroups/${campaignGroupId}/audience`,
        );
        setIsSubmitting(false);
      },
    });

  const handleSubmit = () => {
    setIsSubmitting(true);
    createCampaign.mutate({
      adProgramId: campaignGroupId,
      segmentIds: selectedSegments,
    });
  };

  const table = useReactTable({
    data,
    columns,
    enableRowSelection: false,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    onSortingChange: setSorting,
    state: {
      columnFilters,
      sorting,
    },
  });

  function updateDateRange(value: string) {
    if (value !== "all") {
      const nowDate = new Date(Date.now());
      nowDate.setMonth(nowDate.getMonth() - parseInt(value));
      setDateRange({ startDate: nowDate, endDate: undefined });
    } else {
      setDateRange({ startDate: undefined, endDate: undefined });
    }
  }

  // Function to remove a segment from selection
  const handleRemoveSegment = (id: string) => {
    removeSegment(id);
  };

  return (
    <div className="w-full flex-col items-start justify-between">
      <div className="flex h-[calc(100vh-64px)] w-full flex-col gap-4 overflow-auto p-4 pb-20 md:flex-row">
        {/* Left column - All Segments */}
        <div className="flex-1">
          <Card className="h-full">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-xl font-bold">All Segments</CardTitle>
              <Button variant="outline" size="sm">
                <Filter className="mr-2 h-4 w-4" />
                Filter
              </Button>
            </CardHeader>
            <CardContent className="flex-1 overflow-auto">
              <div className="rounded-md border">
                <DataTable
                  columns={columns}
                  table={table}
                  rowHeight="16"
                  noHover
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Right column - Selected Segments */}
        <div className="w-full md:w-72">
          <Card className="h-full">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-xl font-bold">
                Selected Segments
                {selectedSegments.length > 0 && (
                  <Badge variant="secondary" className="ml-2">
                    {selectedSegments.length}
                  </Badge>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent className="flex-1">
              {selectedSegments.length > 0 ? (
                <div className="flex h-[calc(100%-48px)] flex-col">
                  <ScrollArea className="h-full pr-2">
                    <div className="flex flex-col gap-1">
                      {selectedSegments.map((segmentId) => (
                        <SelectedSegmentItem
                          key={segmentId}
                          segment={
                            data.find((s) => s.id === segmentId) ||
                            ({} as Segment)
                          }
                          removeSegment={handleRemoveSegment}
                        />
                      ))}
                    </div>
                  </ScrollArea>
                </div>
              ) : (
                <div className="flex min-h-[100px] items-center justify-center rounded-md border border-dashed bg-muted/30">
                  <p className="text-muted-foreground">No segments selected</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
      <NavigationFooter
        onPrevious={() =>
          router.push(
            `/advertising/adAccounts/${adAccount}/campaignGroups/${campaignGroupId}/edit`,
          )
        }
        onNext={handleSubmit}
        nextDisabled={selectedSegments.length === 0 || isSubmitting}
        nextLoading={isSubmitting}
      />
    </div>
  );
}

// Helper function to get display details for a segment
function getSegmentDisplayDetails(segment: Segment): {
  primary: string;
  secondary: string;
} {
  const nameArray: string[] = [];
  const secondaryArray: string[] = [];

  if (segment.name) {
    return {
      primary: segment.name,
      secondary: "",
    };
  }

  // Add main identifier (job function, seniority, verticals)
  if (segment.jobFunction) {
    nameArray.push(snakeCaseToWords(segment.jobFunction));
  }
  if (segment.jobSeniority) {
    nameArray.push(snakeCaseToWords(segment.jobSeniority));
  }
  if (segment.verticals && segment.verticals.length > 0) {
    nameArray.push(segment.verticals.join(", "));
  }

  // Add secondary details
  if (
    segment.annualContractValueLowBound ||
    segment.annualContractValueHighBound
  ) {
    const acvString = formatRangeValue(
      segment.annualContractValueLowBound,
      segment.annualContractValueHighBound,
      "ACV",
    );
    secondaryArray.push(acvString);
  }

  if (segment.annualRevenueLowBound || segment.annualRevenueHighBound) {
    const revenueString = formatRangeValue(
      segment.annualRevenueLowBound,
      segment.annualRevenueHighBound,
      "prospect revenue",
    );
    secondaryArray.push(revenueString);
  }

  if (segment.numberOfEmployeesLowBound || segment.numberOfEmployeesHighBound) {
    const employeesString = formatRangeValue(
      segment.numberOfEmployeesLowBound,
      segment.numberOfEmployeesHighBound,
      "employees",
      false,
    );
    secondaryArray.push(employeesString);
  }

  return {
    primary: nameArray.length > 0 ? nameArray.join(" • ") : "All",
    secondary: secondaryArray.join(" • "),
  };
}

// Helper function to format range values
function formatRangeValue(
  lowBound: number | null | undefined,
  highBound: number | null | undefined,
  label: string,
  includesDollarSign: boolean = true,
): string {
  const prefix = includesDollarSign ? "$" : "";

  if (lowBound && highBound) {
    return `${prefix}${lowBound.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",")}-${prefix}${highBound.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",")} ${label}`;
  }

  if (lowBound && !highBound) {
    return `${prefix}${lowBound.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",")}+ ${label}`;
  }

  if (!lowBound && highBound) {
    return `<${prefix}${highBound.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",")} ${label}`;
  }

  return "";
}
