"use client";

import { Suspense } from "react";
import { api } from "@/trpc/client";

import { Skeleton } from "@kalos/ui/skeleton";

import AbTestClient from "./AbTestClient";

export default function AbTestPage({
  params,
}: {
  params: {
    adAccount: string;
    campaignGroupId: string;
  };
}) {
  return (
    <Suspense fallback={<Skeleton />}>
      <AbTestClient params={params} />
    </Suspense>
  );
}
