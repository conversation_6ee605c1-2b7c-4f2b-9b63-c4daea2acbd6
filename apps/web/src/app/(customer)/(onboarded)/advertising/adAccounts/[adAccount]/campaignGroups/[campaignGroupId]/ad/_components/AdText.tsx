import React, { useEffect, useRef, useState, forwardRef, useImperativeHandle } from "react";
import LeadGenFormsDialog from "@/features/advertising/campaignGroup/components/leadGenFormsDialog";
import { Label } from "@radix-ui/react-dropdown-menu";
import { WandIcon } from "lucide-react";
import { Button } from "@kalos/ui/button";
import { cn } from "@kalos/ui/index";
import { Popover, PopoverContent, PopoverTrigger } from "@kalos/ui/popover";
import { RadioGroup, RadioGroupItem } from "@kalos/ui/radiogroup";
import { Textarea } from "@kalos/ui/textarea";
import { useAdCopyGeneration } from "../_hooks/useAdCopyGeneration";
import { VALIDATION_RULES, SPONSORED_CONTENT_LIMITS } from "../utils/constants";

interface AdTextProps {
  valuePropId: string;
  adSegmentId: string;
  adAccountId: string;
}

export const AdText = forwardRef<
  { saveAdCopy: () => Promise<void> },
  AdTextProps
>(function AdText({ valuePropId, adSegmentId, adAccountId }, ref) {
  const {
    adCopy,
    setAdCopy,
    generating,
    isEditing,
    setIsEditing,
    isSaving,
    feedbackMode,
    setFeedbackMode,
    getAdCopy,
    submitFeedback,
    saveAdCopy,
    cancelEditing,
    adProgramQuery,
    handleSaveLeadGenForm,
  } = useAdCopyGeneration(valuePropId, adSegmentId, adAccountId);

  useImperativeHandle(ref, () => ({
    saveAdCopy: async () => {
      try {
        await saveAdCopy();
      } catch (error) {
        console.error("AdText.saveAdCopy failed:", error);
        throw error;
      }
    },
  }));

  const paragraphRef = useRef<HTMLParagraphElement>(null);
  const [textareaHeight, setTextareaHeight] = useState<number | undefined>(
    undefined,
  );
  const hasLoggedRef = useRef(false);

  const [isFeedbackOpen, setIsFeedbackOpen] = useState(false);
  const [feedback, setFeedback] = useState("");
  const [showFeedbackError, setShowFeedbackError] = useState(false);

  // Log adProgramQuery.data once when it exists
  useEffect(() => {
    if (adProgramQuery.data && !hasLoggedRef.current) {
      console.log("adProgramQuery.data", adProgramQuery.data);
      console.log(
        "true or false",
        JSON.stringify(adProgramQuery.data).includes("leadGenForm"),
      );
      hasLoggedRef.current = true;
    }
  }, [adProgramQuery.data]);

  // Update height when content changes or editing mode changes
  useEffect(() => {
    if (paragraphRef.current) {
      const height = paragraphRef.current.offsetHeight;
      setTextareaHeight(height);
    }
  }, [isEditing, adCopy.body]);

  useEffect(() => {
    setFeedback("");
  }, [isFeedbackOpen]);

  // Add this near the top of the component
  useEffect(() => {
    // Log the status of adProgramQuery
    console.log("adProgramQuery status:", {
      isLoading: adProgramQuery.isLoading,
      isError: adProgramQuery.isError,
      error: adProgramQuery.error,
      adSegmentId: adSegmentId,
      parsedId: adSegmentId.split(":")[0] ?? "",
    });
  }, [adProgramQuery, adSegmentId]);

  const handleFeedbackChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setFeedback(e.target.value);
    if (showFeedbackError) {
      setShowFeedbackError(false);
    }
  };

  const isFeedbackValid = feedback.length >= VALIDATION_RULES.MIN_CHARACTERS;

  async function handleSubmitFeedback() {
    if (!isFeedbackValid) {
      setShowFeedbackError(true);
      return;
    }
    setIsFeedbackOpen(false);
    await submitFeedback(feedback);
  }

  // Handle text changes with character limit enforcement
  const handlePostTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newText = e.target.value;
    if (newText.length <= SPONSORED_CONTENT_LIMITS.INTRODUCTORY_TEXT) {
      setAdCopy({ ...adCopy, body: newText });
    }
  };

  const handleCtaTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newText = e.target.value;
    if (newText.length <= SPONSORED_CONTENT_LIMITS.HEADLINE) {
      setAdCopy({ ...adCopy, title: newText });
    }
  };

  return (
    <div
      className={cn(
        generating ? "opacity-50" : "",
        "flex w-[480px] flex-col items-start justify-between",
      )}
    >
      <div className="flex w-full flex-col justify-start space-y-2 ">
        <div className="flex items-center justify-between">
          {isEditing && (
            <>
              <Popover open={isFeedbackOpen} onOpenChange={setIsFeedbackOpen}>
                <PopoverTrigger asChild>
                  <Button className="border border-blue-500 bg-blue-200 text-black">
                    <WandIcon width="16" height="16" className="mr-1" />
                    Ask Blue to Rewrite
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="flex flex-col items-start justify-start space-y-2">
                  <Textarea
                    value={feedback}
                    onChange={handleFeedbackChange}
                    error={`Feedback must be at least ${VALIDATION_RULES.MIN_CHARACTERS} characters long.`}
                    showError={showFeedbackError}
                    placeholder="Describe how you'd like Blue to improve the copy..."
                  />
                  <div className="flex w-full items-center justify-between gap-2">
                    <div className="flex items-center justify-center gap-2">
                      <RadioGroup
                        defaultValue="body"
                        value={feedbackMode}
                        onValueChange={(value) =>
                          setFeedbackMode(value as "body" | "title")
                        }
                      >
                        <div className="flex items-center space-x-2">
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="body" id="r1" />
                            <Label className="text-sm">Body</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="title" id="r2" />
                            <Label className="text-sm">Title</Label>
                          </div>
                        </div>
                      </RadioGroup>
                    </div>
                    <Button onClick={handleSubmitFeedback}>Submit</Button>
                  </div>
                </PopoverContent>
              </Popover>
              <div className="flex items-center justify-end gap-2">
                <Button
                  variant="outline"
                  disabled={isSaving || generating}
                  onClick={(e) => {
                    e.preventDefault();
                    cancelEditing();
                  }}
                >
                  Cancel
                </Button>
                <Button
                  disabled={isSaving || generating}
                  onClick={(e) => {
                    e.preventDefault();
                    saveAdCopy();
                  }}
                >
                  Save
                </Button>
              </div>
            </>
          )}
        </div>
        <div className="flex items-center justify-end gap-2">
          {!isEditing && (
            <Button
              disabled={generating || isSaving}
              onClick={() => setIsEditing(true)}
            >
              Edit
            </Button>
          )}
        </div>
        <div className="flex w-full flex-col justify-start space-y-2 pb-4 pt-2">
          <h1 className="text-sm font-semibold">Post</h1>
          <p
            ref={paragraphRef}
            className="min-h-[1px] w-full resize-none p-0 text-sm leading-normal shadow-none"
            style={{
              position: isEditing ? "absolute" : "static",
              visibility: isEditing ? "hidden" : "visible",
              boxSizing: "border-box",
              lineHeight: "21px",
            }}
          >
            {adCopy.body.split("\n").map((line, index) => (
              <React.Fragment key={index}>
                {line}
                <br />
              </React.Fragment>
            ))}
          </p>
          {isEditing && (
            <Textarea
              value={adCopy.body}
              onChange={handlePostTextChange}
              maxLength={SPONSORED_CONTENT_LIMITS.INTRODUCTORY_TEXT}
              style={{
                height: `310px`,
                minHeight: "100px",
                padding: "8px 12px",
                boxSizing: "border-box",
                lineHeight: "21px",
                width: "100%",
                resize: "none",
              }}
              className="w-full leading-normal"
            />
          )}
        </div>
        <h1 className="text-sm font-semibold">Call to action</h1>
        {!isEditing && (
          <p className="min-h-[1px] w-full resize-none p-0 text-sm shadow-none focus:focus-visible:ring-0 disabled:bg-transparent disabled:text-foreground ">
            {adCopy.title}
          </p>
        )}
        {isEditing && (
          <Textarea
            value={adCopy.title}
            onChange={handleCtaTextChange}
            maxLength={SPONSORED_CONTENT_LIMITS.HEADLINE}
            className="min-h-[1px] w-full resize-none p-0 shadow-none focus-visible:ring-0 disabled:bg-transparent disabled:text-foreground "
          />
        )}
      </div>

      {(adCopy.leadGenForm ||
        adProgramQuery?.data?.objectiveType === "LEAD_GENERATION" ||
        adProgramQuery?.data?.leadGenForm ||
        (adProgramQuery?.data &&
          JSON.stringify(adProgramQuery.data).includes("leadGenForm"))) && (
          <>
            <h1 className="pt-4 text-sm font-semibold">Lead Gen Form</h1>
            <LeadGenFormsDialog
              setSelectedFormId={(formId) => {
                // Prevent default behavior that might cause a refresh
                if (formId) {
                  handleSaveLeadGenForm(formId);
                }
              }}
              selectedFormId={adCopy.leadGenForm ?? null}
              disabled={false}
              adAccountId={adAccountId}
            />
          </>
        )}
    </div>
  );
});
