"use client";

import type { Dispatch, SetStateAction } from "react";
import { use, useEffect, useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { snakeCaseToWords } from "@/app/utils/snakeCaseToWords";
import { api } from "@/trpc/client";
import { ReloadIcon } from "@radix-ui/react-icons";
import { ArrowLeft, ArrowRight, PlusIcon } from "lucide-react";

import type {
  CampaignGroupSegmentSegment,
  CampaignSegment,
} from "@kalos/advertising";
import { Button } from "@kalos/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@kalos/ui/card";
import { Checkbox } from "@kalos/ui/checkbox";
import { Input } from "@kalos/ui/input";

export default function AbTestPage({
  params,
}: {
  params: {
    adAccount: string;
    campaignGroupId: string;
  };
}) {
  return (
    <SubjectTest
      adAccount={params.adAccount}
      CampaignGroupId={params.campaignGroupId}
    />
  );
}

function SubjectTest({
  CampaignGroupId: campaignGroupId,
  adAccount: adAccount,
}: {
  CampaignGroupId: string;
  adAccount: string;
}) {
  const subjectsQuery =
    api.v2.ads.adSegmentSelectedConversationSubjectType.getForAdProgram.useQuery(
      {
        adProgramId: campaignGroupId,
      },
    );
  const adProgramQuery = api.v2.ads.linkedInAdProgram.getOne.useQuery({
    id: campaignGroupId,
  });

  const [loadingAds, setLoadingAds] = useState(true);

  const [selectedAds, setSelectedAds] = useState<
    { campaignGroupSegmentId: string; subjectType: string }[]
  >([]);

  useEffect(() => {
    if (subjectsQuery.data && loadingAds) {
      setSelectedAds(
        subjectsQuery.data.map((ad) => ({
          campaignGroupSegmentId: ad.adSegmentId,
          subjectType: ad.type,
        })),
      );
      setLoadingAds(false);
    }
  }, [subjectsQuery.data]);

  const apiUtils = api.useUtils();

  const [isSubmitting, setIsSubmitting] = useState(false);

  // ... existing code ...
  const adMutation =
    api.v2.ads.adSegmentSelectedConversationSubjectType.setAdSegmentSelectedConversationSubjectTypesForAdSegment.useMutation(
      {
        onSuccess: async (mutationData) => {
          try {
            // Explicitly refetch subjectsQuery and wait for its completion.
            // The `refetch` method returns an object including the new `data`.
            const { data: newSubjectsData, isSuccess: subjectsRefetchSuccess } =
              await subjectsQuery.refetch();

            // Explicitly refetch adProgramQuery and wait for its completion.
            const {
              data: newAdProgramData,
              isSuccess: adProgramRefetchSuccess,
            } = await adProgramQuery.refetch();

            // Now, use the newSubjectsData for your alert/logging
            if (subjectsRefetchSuccess && newSubjectsData) {
            } else {
              console.warn(
                "subjectsQuery.refetch() did not succeed or returned no data.",
              );
            }

            // And use newAdProgramData for your console.log and routing logic
            console.log(
              "Data from adProgramQuery.refetch():",
              newAdProgramData,
            );

            if (adProgramRefetchSuccess && newAdProgramData) {
              if (newAdProgramData.adFormat.type == "SPONSORED_INMAIL") {
                router.push(
                  `/advertising/adAccounts/${adAccount}/campaignGroups/${campaignGroupId}/conversation-ad`,
                );
              } else {
                router.push(
                  `/advertising/adAccounts/${adAccount}/campaignGroups/${campaignGroupId}/ad`,
                );
              }
            } else {
              console.error(
                "adProgramQuery.refetch() did not succeed or returned no data. Navigation may be incorrect.",
              );
              // Fallback navigation or error display might be needed here
            }
          } catch (error) {
            console.error("Error during refetch or in onSuccess logic:", error);
            // Potentially show an error message to the user
          } finally {
            setIsSubmitting(false);
          }
        },
        onError: (error) => {
          console.error("Mutation failed:", error);
          setIsSubmitting(false);
          // Handle mutation error, e.g., show a toast to the user
        },
      },
    );

  function onSubmit() {
    setIsSubmitting(true);
    const campaignsWithAds: {
      campaignGroupSegmentId: string;
      subjectTypes: string[];
    }[] = [];
    for (const selectedAd of selectedAds) {
      const campaign = campaignsWithAds.find(
        (campaign) =>
          campaign.campaignGroupSegmentId == selectedAd.campaignGroupSegmentId,
      );
      if (campaign) {
        campaign.subjectTypes.push(selectedAd.subjectType);
      } else {
        campaignsWithAds.push({
          campaignGroupSegmentId: selectedAd.campaignGroupSegmentId,
          subjectTypes: [selectedAd.subjectType],
        });
      }
    }
    console.log(campaignsWithAds);
    adMutation.mutate({
      adProgramId: campaignGroupId,
      adSegments: campaignsWithAds.map((each) => ({
        adSegmentId: each.campaignGroupSegmentId,
        conversationSubjectCopyTypes: each.subjectTypes,
      })),
    });
  }

  function thing() {
    const reduced = selectedAds.reduce<
      Record<string, { adSegmentId: string; subjectTypes: string[] }>
    >((acc, each) => {
      let curr = acc[each.campaignGroupSegmentId];
      if (!curr) {
        curr = {
          adSegmentId: each.campaignGroupSegmentId,
          subjectTypes: [],
        };
      }
      curr.subjectTypes.push(each.subjectType);
      acc[each.campaignGroupSegmentId] = curr;
      return acc;
    }, {});
    return Object.values(reduced);
  }

  useEffect(() => {
    console.log(selectedAds);
  }, [selectedAds]);
  const router = useRouter();
  const segmentQuery =
    api.v2.ads.adSegment.getAllAdSegmentsForAdProgram.useQuery({
      adProgramId: campaignGroupId,
    });
  return (
    <div className="flex h-full w-full flex-col items-start justify-between space-y-4">
      <div className="flex h-full w-full flex-col items-start justify-start space-y-4 px-4  py-6">
        <div className="flex w-full flex-col items-start justify-start">
          <h1 className=" space-y-2 text-lg font-medium">Offer Testing</h1>
          <h2>
            Select the offers to test. View ad copy for each on the next screen.
          </h2>
        </div>
        <div className="flex flex-wrap items-start justify-start  gap-x-4 gap-y-4 ">
          {adProgramQuery.data &&
            segmentQuery.data?.map((segment) => {
              return (
                <ValueProps
                  segment={segment}
                  selectedAds={selectedAds}
                  setSelectedAds={setSelectedAds}
                  disabled={isSubmitting || loadingAds}
                  customTopics={
                    subjectsQuery.data
                      ?.filter((each) => each.adSegmentId == segment.id)
                      .map((ad) => ad.type) ?? []
                  }
                  isSubmitting={isSubmitting}
                  adProgramFormat={
                    adProgramQuery.data?.adFormat.type as
                    | "SPONSORED_INMAIL"
                    | "SPONSORED_POST"
                  }
                />
              );
            })}
        </div>
      </div>
      <div className="bg-backround align-end flex h-16 w-full items-center justify-between border-t p-4">
        <Button
          onClick={() =>
            router.push(
              `/advertising/adAccounts/${adAccount}/campaignGroups/${campaignGroupId}/abTest`,
            )
          }
          className="border-1 border border-blue-200 bg-blue-100 text-primary hover:bg-blue-200"
        >
          <ArrowLeft width="16" className="mr-2" /> Previous
        </Button>
        <Button
          onClick={onSubmit}
          disabled={selectedAds.length == 0 || isSubmitting}
        >
          {isSubmitting && <ReloadIcon className="mr-2 h-4 w-4 animate-spin" />}
          Next
          <ArrowRight width="16" className="ml-2" />
        </Button>
      </div>
    </div>
  );
}

function ValueProps({
  segment,
  selectedAds,
  setSelectedAds,
  disabled,
  customTopics,
  isSubmitting,
  adProgramFormat,
}: {
  segment: {
    id: string;
    adProgramId: string;
    segmentId: string;
  };
  selectedAds: { campaignGroupSegmentId: string; subjectType: string }[];
  setSelectedAds: Dispatch<
    SetStateAction<{ campaignGroupSegmentId: string; subjectType: string }[]>
  >;
  customTopics: string[];
  disabled: boolean;
  isSubmitting: boolean;
  adProgramFormat: "SPONSORED_INMAIL" | "SPONSORED_POST";
}) {
  // THIS IS the query FOR AVAILABLE VALUE PROPS IN SEGMENT
  const valuePropsQuery =
    api.v2.core.segment.getSegmentValuePropForSegment.useQuery({
      segmentId: segment.segmentId,
    });

  const sQuery = api.v2.core.segment.getSegmentsForOrganization.useQuery();

  const [userAddedTopics, setUserAddedTopics] = useState<string[]>([]);

  useEffect(() => {
    if (isSubmitting) {
      setUserAddedTopics([]);
    }
  }, [isSubmitting]);

  return (
    <Card className="h-[280] w-[424px]  px-4 py-4">
      <CardHeader>
        <CardTitle>
          <SegmentDetails
            row={{
              original: sQuery.data?.find((s) => s.id == segment.segmentId) ?? {
                id: segment.segmentId,
                name: "",
                verticals: [],
                annualRevenueLowBound: null,
                annualRevenueHighBound: null,
                numberOfEmployeesLowBound: null,
                numberOfEmployeesHighBound: null,
                annualContractValueLowBound: null,
                annualContractValueHighBound: null,
                jobFunction: null,
                jobSeniority: null,
              },
            }}
          />
        </CardTitle>
      </CardHeader>
      <CardContent className="flex h-[175px] flex-col items-start justify-start space-y-2 overflow-auto text-sm">
        {[
          "Hard Offer: Free Trial",
          "Soft Offer: Free Audit",
          "No Offer: Intrigue",
          "No Offer: Value Proposition",
          "Hard Offer: Company Gear",
        ]
          .filter((each) => {
            return !valuePropsQuery.data?.find((v) => v.name == each);
          })
          .map((each) => {
            return (
              <div className="flex items-center justify-start space-x-2">
                <Checkbox
                  disabled={disabled}
                  checked={
                    selectedAds.find(
                      (selectedAd) =>
                        selectedAd.subjectType == each &&
                        selectedAd.campaignGroupSegmentId == segment.id,
                    ) != undefined
                  }
                  onCheckedChange={(checked) => {
                    if (checked) {
                      setSelectedAds([
                        ...selectedAds,
                        {
                          campaignGroupSegmentId: segment.id,
                          subjectType: each,
                        },
                      ]);
                    } else {
                      setSelectedAds(
                        selectedAds.filter(
                          (selectedAd) =>
                            selectedAd.subjectType != each ||
                            selectedAd.campaignGroupSegmentId != segment.id,
                        ),
                      );
                    }
                  }}
                />
                <p>{each}</p>
              </div>
            );
          })}
        {valuePropsQuery.data &&
          customTopics
            .filter(
              (each) =>
                ![
                  "Hard Offer: Free Trial",
                  "Soft Offer: Free Audit",
                  "No Offer: Intrigue",
                  "No Offer: Value Proposition",
                  "Hard Offer: Company Gear",
                ].includes(each),
            )
            .map((each) => {
              return (
                <div className="flex items-center justify-start space-x-2">
                  <Checkbox
                    disabled={disabled}
                    checked={
                      selectedAds.find(
                        (selectedAd) =>
                          selectedAd.subjectType == each &&
                          selectedAd.campaignGroupSegmentId == segment.id,
                      ) != undefined
                    }
                    onCheckedChange={(checked) => {
                      if (checked) {
                        setSelectedAds([
                          ...selectedAds,
                          {
                            campaignGroupSegmentId: segment.id,
                            subjectType: each,
                          },
                        ]);
                      } else {
                        setSelectedAds(
                          selectedAds.filter(
                            (selectedAd) =>
                              selectedAd.subjectType != each ||
                              selectedAd.campaignGroupSegmentId != segment.id,
                          ),
                        );
                      }
                    }}
                  />
                  <p>{each}</p>
                </div>
              );
            })}

        {userAddedTopics.map((each, index) => {
          return (
            <div className="flex items-center justify-start space-x-2">
              <Checkbox
                checked={
                  selectedAds.find(
                    (selectedAd) =>
                      selectedAd.subjectType == each &&
                      selectedAd.campaignGroupSegmentId == segment.id,
                  ) != undefined
                }
                disabled={each == "" || disabled}
                onCheckedChange={(checked) => {
                  if (checked) {
                    setSelectedAds([
                      ...selectedAds,
                      {
                        campaignGroupSegmentId: segment.id,
                        subjectType: each,
                      },
                    ]);
                  } else {
                    setSelectedAds(
                      selectedAds.filter(
                        (selectedAd) =>
                          selectedAd.subjectType != each ||
                          selectedAd.campaignGroupSegmentId != segment.id,
                      ),
                    );
                  }
                }}
              />
              <Input
                onChange={(event) => {
                  const newTopics = [...userAddedTopics];
                  newTopics[index] = event.target.value;
                  setUserAddedTopics(newTopics);
                }}
                className="h-7 w-full"
                value={userAddedTopics[index]}
              />
            </div>
          );
        })}
        <Button
          variant="ghost"
          disabled={disabled}
          className=" hover:bg-transpatent  p-0 text-xs text-primary hover:text-blue-300"
          onClick={() => {
            setUserAddedTopics([...userAddedTopics, ""]);
          }}
        >
          <PlusIcon className="m-0 mr-2 p-0" height="16" width="16" /> Add more
        </Button>
      </CardContent>
    </Card>
  );
}

function SegmentDetails({
  row,
}: {
  row: {
    original: {
      id: string;
      name?: string | null;
      verticals: string[];
      annualRevenueLowBound?: number | null;
      annualRevenueHighBound?: number | null;
      numberOfEmployeesLowBound?: number | null;
      numberOfEmployeesHighBound?: number | null;
      annualContractValueLowBound?: number | null;
      annualContractValueHighBound?: number | null;
      jobFunction?: string | null;
      jobSeniority?: string | null;
    };
  };
}) {
  if (!row.original.name) {
    const nameArray: string[] = [];
    if (row.original.jobFunction) {
      nameArray.push(snakeCaseToWords(row.original.jobFunction));
    }
    if (row.original.jobSeniority) {
      nameArray.push(snakeCaseToWords(row.original.jobSeniority));
    }

    if (row.original.verticals) {
      nameArray.push(row.original.verticals.join(", "));
    }

    if (
      row.original.annualContractValueLowBound &&
      row.original.annualContractValueHighBound
    ) {
      nameArray.push(
        `$${row.original.annualContractValueLowBound
          .toString()
          .replace(
            /\B(?=(\d{3})+(?!\d))/g,
            ",",
          )}-$${row.original.annualContractValueHighBound
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} ACV`,
      );
    }
    if (
      row.original.annualContractValueLowBound &&
      !row.original.annualContractValueHighBound
    ) {
      nameArray.push(
        `$${row.original.annualContractValueLowBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")}+ ACV`,
      );
    }
    if (
      !row.original.annualContractValueLowBound &&
      row.original.annualContractValueHighBound
    ) {
      nameArray.push(
        `<$${row.original.annualContractValueHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} ACV`,
      );
    }

    if (
      row.original.annualRevenueLowBound &&
      row.original.annualRevenueHighBound
    ) {
      nameArray.push(
        `$${row.original.annualRevenueLowBound
          .toString()
          .replace(
            /\B(?=(\d{3})+(?!\d))/g,
            ",",
          )}-$${row.original.annualRevenueHighBound
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} prospect revenue`,
      );
    }
    if (
      row.original.annualRevenueLowBound &&
      !row.original.annualRevenueHighBound
    ) {
      nameArray.push(
        `$${row.original.annualRevenueLowBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")}+ prospect revenue`,
      );
    }
    if (
      !row.original.annualRevenueLowBound &&
      row.original.annualRevenueHighBound
    ) {
      nameArray.push(
        `<$${row.original.annualRevenueHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} prospect revenue`,
      );
    }

    if (
      row.original.numberOfEmployeesLowBound &&
      row.original.numberOfEmployeesHighBound
    ) {
      nameArray.push(
        `${row.original.numberOfEmployeesLowBound
          .toString()
          .replace(
            /\B(?=(\d{3})+(?!\d))/g,
            ",",
          )}-${row.original.numberOfEmployeesHighBound
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} employees`,
      );
    }
    if (
      row.original.numberOfEmployeesLowBound &&
      !row.original.numberOfEmployeesHighBound
    ) {
      nameArray.push(
        `${row.original.numberOfEmployeesLowBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")}+ employees`,
      );
    }
    if (
      !row.original.numberOfEmployeesLowBound &&
      row.original.numberOfEmployeesHighBound
    ) {
      nameArray.push(
        `<${row.original.numberOfEmployeesHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} employees`,
      );
    }

    let acvQueryParam = "";
    if (
      row.original.annualContractValueHighBound == 20000 &&
      row.original.annualContractValueLowBound == undefined
    ) {
      acvQueryParam = "1";
    } else if (
      row.original.annualContractValueHighBound == 50000 &&
      row.original.annualContractValueLowBound == 20000
    ) {
      acvQueryParam = "2";
    } else if (
      row.original.annualContractValueHighBound == 100000 &&
      row.original.annualContractValueLowBound == 50000
    ) {
      acvQueryParam = "3";
    } else if (
      row.original.annualContractValueHighBound == undefined &&
      row.original.annualContractValueLowBound == 100000
    ) {
      acvQueryParam = "4";
    }

    let empoyeesQueryParam = "";
    if (
      row.original.numberOfEmployeesHighBound == 100 &&
      row.original.numberOfEmployeesLowBound == undefined
    ) {
      empoyeesQueryParam = "1";
    } else if (
      row.original.numberOfEmployeesHighBound == 500 &&
      row.original.numberOfEmployeesLowBound == 100
    ) {
      empoyeesQueryParam = "2";
    } else if (
      row.original.numberOfEmployeesHighBound == undefined &&
      row.original.numberOfEmployeesLowBound == 500
    ) {
      empoyeesQueryParam = "3";
    }

    let prospectRevenue = "";
    if (
      row.original.annualRevenueLowBound == 1000000 &&
      row.original.annualRevenueHighBound == 10000000
    ) {
      prospectRevenue = "1";
    } else if (
      row.original.annualRevenueLowBound == 10000000 &&
      row.original.annualRevenueHighBound == 200000000
    ) {
      prospectRevenue = "2";
    } else if (
      row.original.annualRevenueLowBound == 200000000 &&
      row.original.annualRevenueHighBound == undefined
    ) {
      prospectRevenue = "3";
    }

    // Search params
    const params = new URLSearchParams({
      acv: acvQueryParam,
      vertical: row.original.verticals?.join(",") ?? "",
      employees: empoyeesQueryParam,
      prospectRevenue: prospectRevenue,
    });

    if (nameArray.length == 0) {
      return (
        <div className="space-y-1">
          <Link href={`/}`} className="w-[80px]">
            All
          </Link>
        </div>
      );
    }
    return (
      <div className="w-[380px] space-y-1 text-wrap ">
        <span className="50 w-[380px] text-wrap">{nameArray.join(" • ")}</span>
      </div>
    );
  }
  return (
    <div className="space-y-1">
      <span className="w-[80px]">{row.original.name}</span>
    </div>
  );
}

/*
 {data
          .map((segment) => {
            return (
              <Card className="w-[424px] px-4 py-4">
                <CardContent className="p-0 font-medium">
                  <SegmentDetails
                    row={{
                      original: segment,
                    }}
                  />
                </CardContent>
              </Card>
            );
          })}
*/
