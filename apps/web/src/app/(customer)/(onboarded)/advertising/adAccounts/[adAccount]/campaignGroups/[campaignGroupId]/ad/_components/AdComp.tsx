import React, { useCallback, useMemo, useRef } from "react";
import { useRouter } from "next/navigation";
import { api } from "@/trpc/client";
import { ArrowLeft, ArrowRight } from "lucide-react";

import { Button } from "@kalos/ui/button";

import { SegmentComponent } from "./SegmentComponent";

interface AdCompProps {
  params: {
    adAccount: string;
    campaignGroupId: string;
  };
}

export const AdComp = React.memo(function AdComp({ params }: AdCompProps) {
  // Use a stable reference for the query with consistent options
  const campaignsQuery =
    api.v2.ads.adSegment.getAllAdSegmentsForAdProgram.useQuery(
      {
        adProgramId: params.campaignGroupId,
      },
      {
        refetchOnWindowFocus: false,
        staleTime: 60 * 1000,
        // Prevent refetching unless data actually changes
        structuralSharing: true,
      },
    );

  const router = useRouter();
  const segmentRefs = useRef<Array<{ saveAllAdCopies: () => Promise<void> }>>([]);

  // Memoize navigation URLs to prevent unnecessary recalculations
  const navigationUrls = useMemo(() => ({
    previous: `/advertising/adAccounts/${params.adAccount}/campaignGroups/${params.campaignGroupId}/abTest`,
    next: `/advertising/adAccounts/${params.adAccount}/campaignGroups/${params.campaignGroupId}/assets`,
  }), [params.adAccount, params.campaignGroupId]);

  // Use callbacks for event handlers to avoid recreating functions
  const handlePrevious = useCallback(
    (e: React.MouseEvent) => {
      e.preventDefault();
      router.push(navigationUrls.previous);
    },
    [router, navigationUrls.previous],
  );

  const handleNext = useCallback(
    async (e: React.MouseEvent) => {
      e.preventDefault();

      // Save all ad copies before navigating
      const savePromises = segmentRefs.current
        .filter(ref => ref?.saveAllAdCopies)
        .map(ref => ref.saveAllAdCopies());

      if (savePromises.length > 0) {
        try {
          await Promise.all(savePromises);
        } catch (error) {
          console.error('Error saving ad copies:', error);
          // Continue with navigation even if save fails
        }
      }

      router.push(navigationUrls.next);
    },
    [router, navigationUrls.next],
  );

  // Memoize the segment list to prevent unnecessary re-renders
  const segmentList = useMemo(() => {
    return campaignsQuery.data?.map((segment, index) => (
      <MemoizedSegmentComponent
        key={segment.id}
        segment={segment}
        campaignGroupId={params.campaignGroupId}
        adAccountId={params.adAccount}
        index={index}
        ref={(ref: { saveAllAdCopies: () => Promise<void> } | null) => {
          if (ref) {
            segmentRefs.current[index] = ref;
          }
        }}
      />
    )) || [];
  }, [campaignsQuery.data, params.campaignGroupId, params.adAccount]);

  return (
    <div className="w-full flex-col items-start justify-between">
      <div className="flex h-[calc(100vh-64px)] w-full flex-col items-start justify-start space-y-4 overflow-auto px-4 pb-20 pt-4">
        {segmentList}
      </div>
      <div className="sticky bottom-0 left-0 right-0 z-10 flex h-16 w-full items-center justify-between border-t bg-background p-4">
        <Button
          onClick={handlePrevious}
          className="border-1 border border-blue-200 bg-blue-100 text-primary hover:bg-blue-200"
        >
          <ArrowLeft width="16" className="mr-2" /> Previous
        </Button>
        <Button onClick={handleNext}>
          Next
          <ArrowRight width="16" className="ml-2" />
        </Button>
      </div>
    </div>
  );
});

// Memoized SegmentComponent to prevent unnecessary re-renders
const MemoizedSegmentComponent = React.memo(
  React.forwardRef<
    { saveAllAdCopies: () => Promise<void> },
    React.ComponentProps<typeof SegmentComponent>
  >((props, ref) => <SegmentComponent {...props} ref={ref} />),
  (prevProps, nextProps) => {
    // Custom comparison function to prevent re-renders when data hasn't actually changed
    return (
      prevProps.segment.id === nextProps.segment.id &&
      prevProps.campaignGroupId === nextProps.campaignGroupId &&
      prevProps.adAccountId === nextProps.adAccountId &&
      prevProps.index === nextProps.index
    );
  }
);