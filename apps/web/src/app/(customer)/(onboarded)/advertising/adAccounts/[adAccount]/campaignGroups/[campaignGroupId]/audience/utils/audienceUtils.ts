export enum AudienceModeEnum {
    INCLUDE = "INCLUDE",
    EXCLUDE = "EXCLUDE"
}

export type AudienceMode = AudienceModeEnum | null;

export function getFriendlyFacetName(facetName: string): string {
    const facetNameMap: Record<string, string> = {
        'Locations': 'Countries',
        'Staff Count Ranges': 'Employee Count',
    };

    return facetNameMap[facetName] || facetName;
}

export function camelCaseToWords(camelCase: string) {
    return camelCase
        .replace(/([a-z])([A-Z])/g, "$1 $2")
        .replace(/^./, (str) => str.toUpperCase())
        .replace(/ ([a-z])/g, (match) => match.toUpperCase());
}

export function sortFacetsByFixedOrder(facets: Array<{ facetName: string; adTargetingFacetUrn: string; availableEntityFinders: ("TYPEAHEAD" | "AD_TARGETING_FACET")[] }>) {
    const specialItems = [
        {
            type: 'special' as const,
            facetName: 'retargeting',
            adTargetingFacetUrn: 'special:retargeting',
            availableEntityFinders: [] as ("TYPEAHEAD" | "AD_TARGETING_FACET")[],
            segmentType: 'RETARGETING' as const,
            displayName: 'Retargeting'
        },
        {
            type: 'special' as const,
            facetName: 'audienceSegments',
            adTargetingFacetUrn: 'special:audienceSegments',
            availableEntityFinders: [] as ("TYPEAHEAD" | "AD_TARGETING_FACET")[],
            segmentType: 'BULK' as const,
            displayName: 'Uploaded List'
        },
        {
            type: 'special' as const,
            facetName: 'thirdParty',
            adTargetingFacetUrn: 'special:thirdParty',
            availableEntityFinders: [] as ("TYPEAHEAD" | "AD_TARGETING_FACET")[],
            segmentType: 'MARKET_AUTOMATION' as const,
            displayName: 'Third Party List'
        }
    ];

    const regularItems = facets.map(facet => ({
        type: 'facet' as const,
        ...facet
    }));

    const allItems = [...specialItems, ...regularItems];

    const fixedOrder = [
        'Retargeting',
        'Uploaded List',
        'Third Party List',
        'Seniorities',
        'Titles',
        'Job Functions',
        'Industries',
        'Employee Count',
        'Countries',
        'Skills',
        'Groups'
    ];

    return allItems.sort((a, b) => {
        const friendlyNameA = a.type === 'special'
            ? a.displayName
            : getFriendlyFacetName(camelCaseToWords(a.facetName));
        const friendlyNameB = b.type === 'special'
            ? b.displayName
            : getFriendlyFacetName(camelCaseToWords(b.facetName));

        const indexA = fixedOrder.indexOf(friendlyNameA);
        const indexB = fixedOrder.indexOf(friendlyNameB);

        // If both are in the fixed order, sort by their position
        if (indexA !== -1 && indexB !== -1) {
            return indexA - indexB;
        }

        // If only A is in the fixed order, it comes first
        if (indexA !== -1) return -1;

        // If only B is in the fixed order, it comes first
        if (indexB !== -1) return 1;

        // If neither is in the fixed order, sort alphabetically
        return friendlyNameA.localeCompare(friendlyNameB);
    });
}

export function getBlockedEntities(
    targetingCriteria: any,
    facetUrn: string,
    mode: AudienceModeEnum
): string[] {
    if (!targetingCriteria) return [];

    const blockedEntityUrns: string[] = [];

    if (mode === AudienceModeEnum.INCLUDE) {
        // 1. Block entities that are already in INCLUDE (prevent duplicates)
        if (targetingCriteria.include?.and) {
            for (const andGroup of targetingCriteria.include.and) {
                if (andGroup.or) {
                    const includeFacet = andGroup.or.find(
                        (orGroup: any) => orGroup.facetUrn === facetUrn
                    );
                    if (includeFacet && includeFacet.facetEntites) {
                        blockedEntityUrns.push(
                            ...includeFacet.facetEntites.map((entity: any) => entity.entityUrn)
                        );
                    }
                }
            }
        }

        // 2. Block entities that are in EXCLUDE (prevent conflicts)
        if (targetingCriteria.exclude?.or) {
            const excludeFacet = targetingCriteria.exclude.or.find(
                (orGroup: any) => orGroup.facetUrn === facetUrn
            );
            if (excludeFacet && excludeFacet.facetEntites) {
                blockedEntityUrns.push(
                    ...excludeFacet.facetEntites.map((entity: any) => entity.entityUrn)
                );
            }
        }
    } else if (mode === AudienceModeEnum.EXCLUDE) {
        // 1. Block entities that are already in EXCLUDE (prevent duplicates)
        if (targetingCriteria.exclude?.or) {
            const excludeFacet = targetingCriteria.exclude.or.find(
                (orGroup: any) => orGroup.facetUrn === facetUrn
            );
            if (excludeFacet && excludeFacet.facetEntites) {
                blockedEntityUrns.push(
                    ...excludeFacet.facetEntites.map((entity: any) => entity.entityUrn)
                );
            }
        }

        // 2. Block entities that are in INCLUDE (prevent conflicts)
        if (targetingCriteria.include?.and) {
            for (const andGroup of targetingCriteria.include.and) {
                if (andGroup.or) {
                    const includeFacet = andGroup.or.find(
                        (orGroup: any) => orGroup.facetUrn === facetUrn
                    );
                    if (includeFacet && includeFacet.facetEntites) {
                        blockedEntityUrns.push(
                            ...includeFacet.facetEntites.map((entity: any) => entity.entityUrn)
                        );
                    }
                }
            }
        }
    }

    return blockedEntityUrns;
}


export const LINKEDIN_LOCATIONS_FACET_URN = 'urn:li:adTargetingFacet:locations';


export const unitedStatesLocationCriteria = {
    include: {
        and: [
            {
                or: [
                    {
                        facetUrn: LINKEDIN_LOCATIONS_FACET_URN,
                        facetName: 'Locations',
                        facetEntites: [
                            {
                                facetUrn: LINKEDIN_LOCATIONS_FACET_URN,
                                entityUrn: "urn:li:geo:*********",
                                entityName: "United States"
                            }
                        ]
                    }
                ]
            }
        ]
    },
};

export const defaultLocations = [
    {
        name: 'United States',
        urn: 'urn:li:geo:*********'
    },
    {
        name: 'Canada',
        urn: 'urn:li:geo:*********'
    },
    {
        name: 'United Kingdom',
        urn: 'urn:li:geo:*********'
    },
    {
        name: 'Germany',
        urn: 'urn:li:geo:*********'
    },
    {
        name: 'Australia',
        urn: 'urn:li:geo:*********'
    },
    {
        name: 'Japan',
        urn: 'urn:li:geo:*********'
    },
    {
        name: 'France',
        urn: 'urn:li:geo:*********'
    },
    {
        name: 'North America',
        urn: 'urn:li:geo:*********'
    },
    {
        name: 'Latin America',
        urn: 'urn:li:geo:91000011'
    },
    {
        name: 'Europe',
        urn: 'urn:li:geo:*********'
    },
    {
        name: 'Asia',
        urn: 'urn:li:geo:*********'
    },
    {
        name: 'Africa',
        urn: 'urn:li:geo:*********'
    },
    {
        name: 'Middle East',
        urn: 'urn:li:geo:91000001'
    }
];