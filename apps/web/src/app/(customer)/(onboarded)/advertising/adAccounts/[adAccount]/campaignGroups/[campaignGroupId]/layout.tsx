"use client";

import { useEffect, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { ErrorBanner } from "@/components/campaign/ErrorBanner";
import { useErrorStates, useGlobalActions } from "@/hooks/useCampaignStore";
import { useCampaignCreationStore } from "@/stores/campaignCreationStore";
import { api } from "@/trpc/client";
import { X } from "lucide-react";
import { number } from "zod";

import { Button } from "@kalos/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@kalos/ui/dialog";
import KalosLogo from "@kalos/ui/icons/kalos-logo";
import { cn } from "@kalos/ui/index";
import { Skeleton } from "@kalos/ui/skeleton";

// Component to display errors from the store
function ErrorDisplay() {
  const errorStates = useErrorStates();
  const { setError } = useGlobalActions();

  // Find first error to display
  const currentStep = Object.entries(errorStates).find(
    ([_, error]) => error !== null,
  );

  if (!currentStep) return null;

  const [step, error] = currentStep;

  return (
    <div className="px-4 pt-4">
      <ErrorBanner
        error={error}
        onDismiss={() => setError(step as keyof typeof errorStates, null)}
      />
    </div>
  );
}

export default function CampaignGroupLayout({
  children,
  params,
}: Readonly<{
  children: React.ReactNode;
  params: { adAccount: string; campaignGroupId: string };
}>) {
  const router = useRouter();
  const pathname = usePathname();
  const [isExitModalOpen, setIsExitModalOpen] = useState(false);

  // Initialize zustand store with params
  const { setAdAccount, setCampaignGroupId, resetState } = useGlobalActions();

  // Get organization data
  const userQuery = api.v2.core.user.getUser.useQuery();
  const organizationsQuery = api.v2.core.admin.getAllOrganizations.useQuery();

  const currentOrganization = organizationsQuery.data?.find(
    org => org.organizationId === userQuery.data?.organizationId
  );

  // Create a local loading state object for step loading indicators
  const [loadingStates, setLoadingStates] = useState({
    segments: false,
    audience: false,
    abTest: false,
    ad: false,
    "conversation-ad": false,
    assets: false,
    preview: false,
    edit: false,
  });

  useEffect(() => {
    setAdAccount(params.adAccount);
    setCampaignGroupId(params.campaignGroupId);
  }, [params, setAdAccount, setCampaignGroupId]);

  const campaignGroup = api.v2.ads.linkedInAdProgram.getOne.useQuery({
    id: params.campaignGroupId,
  });

  // Define the steps in the campaign creation flow
  const steps =
    campaignGroup.data?.adFormat.type === "SPONSORED_CONTENT"
      ? [
        {
          number: 1,
          title: "Campaign Setup",
          description: "Goals, budget and basics",
          path: `/advertising/adAccounts/${params.adAccount}/campaignGroups/${params.campaignGroupId}/edit`,
          pathMatch: "edit",
        },
        {
          number: 2,
          title: "Segments",
          description: "Select ICP segments",
          path: `/advertising/adAccounts/${params.adAccount}/campaignGroups/${params.campaignGroupId}/segments`,
          pathMatch: "segments",
        },
        {
          number: 3,
          title: "Audience",
          description: "Give feedback on audience tests",
          path: `/advertising/adAccounts/${params.adAccount}/campaignGroups/${params.campaignGroupId}/audience`,
          pathMatch: "audience",
        },
        {
          number: 4,
          title: "Value Proposition",
          description: "Give feedback on value prop tests",
          path: `/advertising/adAccounts/${params.adAccount}/campaignGroups/${params.campaignGroupId}/abTest`,
          pathMatch: "abTest",
        },
        {
          number: 5,
          title: "Content",
          description: "Give feedback on content for tests",
          path: `/advertising/adAccounts/${params.adAccount}/campaignGroups/${params.campaignGroupId}/ad`,
          pathMatch: "ad",
        },
        {
          number: 6,
          title: "Creative",
          description: "Upload assets and setup creative tests",
          path: `/advertising/adAccounts/${params.adAccount}/campaignGroups/${params.campaignGroupId}/assets`,
          pathMatch: "assets",
        },
        {
          number: 7,
          title: "Preview & Launch",
          description: "Review experimentation plan and launch",
          path: `/advertising/adAccounts/${params.adAccount}/campaignGroups/${params.campaignGroupId}/preview`,
          pathMatch: "preview",
        },
      ]
      : [
        {
          number: 1,
          title: "Campaign Setup",
          description: "Goals, budget and basics",
          path: `/advertising/adAccounts/${params.adAccount}/campaignGroups/${params.campaignGroupId}/edit`,
          pathMatch: "edit",
        },
        {
          number: 2,
          title: "Segments",
          description: "Select ICP segments",
          path: `/advertising/adAccounts/${params.adAccount}/campaignGroups/${params.campaignGroupId}/segments`,
          pathMatch: "segments",
        },
        {
          number: 3,
          title: "Audience",
          description: "Give feedback on audience tests",
          path: `/advertising/adAccounts/${params.adAccount}/campaignGroups/${params.campaignGroupId}/audience`,
          pathMatch: "audience",
        },
        {
          number: 4,
          title: "Value Proposition",
          description: "Give feedback on value prop tests",
          path: `/advertising/adAccounts/${params.adAccount}/campaignGroups/${params.campaignGroupId}/abTest`,
          pathMatch: "abTest",
        },
        {
          number: 5,
          title: "Offer Testing",
          description: "Give feedback on offer tests",
          path: `/advertising/adAccounts/${params.adAccount}/campaignGroups/${params.campaignGroupId}/convo-subject`,
          pathMatch: "convo-subject",
        },
        {
          number: 6,
          title: "Content",
          description: "Give feedback on content for tests",
          path: `/advertising/adAccounts/${params.adAccount}/campaignGroups/${params.campaignGroupId}/conversation-ad`,
          pathMatch: "conversation-ad",
        },
        {
          number: 7,
          title: "Preview & Launch",
          description: "Review experimentation plan and launch",
          path: `/advertising/adAccounts/${params.adAccount}/campaignGroups/${params.campaignGroupId}/preview`,
          pathMatch: "preview",
        },
      ];

  // Find the current step based on the pathname match
  // Improved logic to ensure step 1 is correctly identified
  const currentStep = steps.find((step) => {
    // For edit path, we need to check more specifically since "edit" might be part of other paths
    if (step.pathMatch === "edit") {
      return pathname.endsWith(`/edit`) || pathname.endsWith(`/edit/`);
    }
    // Special case for preview path
    if (step.pathMatch === "preview") {
      return pathname.includes(
        `/campaignGroups/${params.campaignGroupId}/preview`,
      );
    }
    // Special case for assets/creative path
    if (step.pathMatch === "assets") {
      return pathname.includes(
        `/campaignGroups/${params.campaignGroupId}/assets`,
      );
    }
    // For other paths, ensure we match the specific segment in the path
    return (
      pathname.includes(`/${step.pathMatch}/`) ||
      pathname.endsWith(`/${step.pathMatch}`)
    );
  });

  // Ensure we have a currentStep, defaulting to step 1
  const currentStepNumber = currentStep?.number || 1;

  // Track the furthest step the user has visited
  const [furthestVisitedStep, setFurthestVisitedStep] =
    useState(currentStepNumber);

  // Update furthestVisitedStep when the user navigates to a higher step
  useEffect(() => {
    if (currentStepNumber > furthestVisitedStep) {
      setFurthestVisitedStep(currentStepNumber);
    }
  }, [currentStepNumber, furthestVisitedStep]);

  // Filter out any steps that should be skipped based on campaign type
  const filteredSteps = steps;
  // Handle exit from campaign creation
  const handleExit = () => {
    // Reset the store state
    resetState();
    // Close modal
    setIsExitModalOpen(false);
    // Navigate back to campaigns list
    router.push("/advertising/new");
  };

  return (
    <div className="fixed inset-0 z-10 flex flex-col bg-white">
      {/* Header with Logo */}
      <div className="flex h-16 items-center justify-between border-b bg-white px-4">
        <div className="flex items-center gap-3">
          <Link href="/" className="flex items-center">
            <Image
              src="/logo.svg"
              alt="Logo"
              width={32}
              height={32}
              className="h-8 w-auto"
            />
          </Link>
          <div className="h-6 w-px bg-gray-300"></div>
          <div className="flex flex-col justify-center">
            <h2 className="text-sm font-medium text-foreground truncate leading-tight">
              {currentOrganization?.name || "Organization"}
            </h2>
          </div>
        </div>

        <div className="flex-1 text-center">
          <h1 className="text-lg font-medium">
            {currentStepNumber === 1 ? (
              "Create Campaign"
            ) : campaignGroup.data?.title ? (
              campaignGroup.data.title
            ) : (
              <Skeleton className="mx-auto h-6 w-40" />
            )}
          </h1>
        </div>

        <button
          onClick={() => setIsExitModalOpen(true)}
          className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-100 transition-colors hover:bg-gray-200"
          aria-label="Exit campaign creation"
        >
          <X size={16} className="text-gray-700" />
        </button>
      </div>

      {/* Confirmation modal */}
      <Dialog open={isExitModalOpen} onOpenChange={setIsExitModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Exit campaign creation?</DialogTitle>
            <DialogDescription>
              Your progress will be saved as a draft. You can resume from where
              you left off later.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex justify-between sm:justify-between">
            <Button variant="outline" onClick={() => setIsExitModalOpen(false)}>
              Cancel
            </Button>
            <Button variant="default" onClick={handleExit}>
              Exit & Save Draft
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Main content area - removed the pt-16 since we're using flex-col now */}
      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar progress tracker - using Tailwind's scale */}
        <div className="w-64 flex-shrink-0 overflow-y-auto border-r bg-white p-4 sm:w-72 md:p-6 lg:w-80">
          <div className="relative flex flex-col gap-y-5">
            {filteredSteps.map((step, index) => {
              const isCurrent = step.number === currentStepNumber;
              const isCompleted = step.number < currentStepNumber;
              const isLast = index === filteredSteps.length - 1;
              // Allow access to any step up to the furthest visited step
              const isAccessible = step.number <= furthestVisitedStep;

              // Create base content that will be used in both clickable and non-clickable versions
              const contentElement = (
                <>
                  {/* Step number box with improved transition and loading indicator */}
                  <div
                    className={cn(
                      "z-10 flex h-[26px] w-[26px] flex-shrink-0 items-center justify-center rounded-[4px] border-2 text-xs font-medium transition-all duration-300",
                      isCompleted
                        ? "border-blue-600 bg-blue-600 text-white"
                        : isCurrent
                          ? "border-blue-600 bg-white text-blue-600"
                          : "border-gray-300 bg-white text-gray-500",
                      loadingStates[
                      step.pathMatch as keyof typeof loadingStates
                      ] &&
                      isCurrent &&
                      "animate-pulse",
                    )}
                  >
                    {loadingStates[
                      step.pathMatch as keyof typeof loadingStates
                    ] && isCurrent ? (
                      <span className="h-3 w-3 animate-spin rounded-full border-2 border-blue-600 border-t-transparent"></span>
                    ) : (
                      step.number
                    )}
                  </div>

                  {/* Step content with transition */}
                  <div className="flex flex-col gap-y-[2px] pt-[1px]">
                    <span
                      className={cn(
                        "text-[13px] font-medium leading-tight transition-colors duration-200",
                        isCurrent
                          ? "text-blue-600"
                          : isCompleted
                            ? "text-gray-900"
                            : "text-gray-700",
                        isAccessible &&
                        !isCurrent &&
                        "group-hover:text-blue-600",
                        loadingStates[
                        step.pathMatch as keyof typeof loadingStates
                        ] &&
                        isCurrent &&
                        "animate-pulse",
                      )}
                    >
                      {step.title}
                      {loadingStates[
                        step.pathMatch as keyof typeof loadingStates
                      ] &&
                        isCurrent && (
                          <span className="ml-2 text-xs text-blue-500">
                            Loading...
                          </span>
                        )}
                    </span>
                    <span className="text-[11px] leading-tight text-gray-500">
                      {step.description}
                    </span>
                  </div>
                </>
              );

              return (
                <div key={step.number} className="relative">
                  {isAccessible ? (
                    <Link
                      href={step.path}
                      className="group relative z-10 flex items-start gap-x-3 py-1"
                    >
                      {contentElement}
                    </Link>
                  ) : (
                    <div className="relative z-10 flex cursor-not-allowed items-start gap-x-3 py-1 opacity-60">
                      {contentElement}
                    </div>
                  )}

                  {/* Timeline line with transition */}
                  {!isLast && (
                    <div
                      className="absolute left-[13px] h-[35px]"
                      style={{ top: "30px" }}
                    >
                      <div className="relative h-full w-[2px] bg-gray-200">
                        <div
                          className={cn(
                            "absolute left-0 top-0 w-[2px] bg-blue-600 transition-all duration-500 ease-in-out",
                            isCompleted ? "h-full" : "h-0",
                          )}
                        ></div>
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* Main content - single scrollable area */}
        <div className="flex flex-1 flex-col bg-gray-50">
          <ErrorDisplay />
          <div className="flex-1">{children}</div>
        </div>
      </div>
    </div>
  );
}
