import { useEffect, useState } from "react";
import { api } from "@/trpc/client";
import { getBaseUrl } from "@/trpc/provider";

/**
 * Type definition for the ad copy state
 */
type AdCopyState = {
  title: string;
  body: string;
  done: boolean;
  leadGenForm: string | null;
};

/**
 * Custom hook for managing ad copy generation, editing, and saving
 *
 * @param valuePropId - The ID of the value proposition
 * @param adSegmentId - The ID of the ad segment
 * @param adAccountId - The ID of the ad account
 * @returns An object containing state and functions for managing ad copy
 */
export function useAdCopyGeneration(
  valuePropId: string,
  adSegmentId: string,
  adAccountId: string,
) {
  const organizationUser = api.v2.core.user.getUser.useQuery();
  const [generating, setGenerating] = useState(true);
  const [adCopy, setAdCopy] = useState<AdCopyState>({
    title: "",
    body: "",
    done: false,
    leadGenForm: null,
  });
  const [baseCopyRefetchInterval, setBaseCopyRefetchInterval] = useState<
    number | undefined
  >(undefined);
  const [readyToGetAdCopy, setReadyToGetAdCopy] = useState(false);
  const [getCopyRan, setGetCopyRan] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [feedbackMode, setFeedbackMode] = useState<"body" | "title">("body");

  const baseCopyQuery =
    api.v2.ads.socialPostBaseCopy.getAdSegmentSocialPostBaseCopy.useQuery(
      {
        adSegmentId: adSegmentId,
      },
      {
        refetchInterval: baseCopyRefetchInterval,
      },
    );

  const apiUtils = api.useUtils();

  const adProgramQuery = api.v2.ads.linkedInAdProgram.getOne.useQuery({
    id: adSegmentId.split(":")[0] ?? "",
  });

  /**
   * Mutation for updating the lead generation form URN
   */
  const updateLeadGenFormUrn =
    api.v2.ads.socialPostBaseCopy.updateLeadGenFormUrn.useMutation({
      onMutate: async ({ linkedInAdSegmentValuePropId, leadGenFormUrn }) => {
        await apiUtils.v2.ads.socialPostBaseCopy.getAdSegmentSocialPostBaseCopy.cancel(
          { adSegmentId },
        );

        const previousData =
          apiUtils.v2.ads.socialPostBaseCopy.getAdSegmentSocialPostBaseCopy.getData(
            { adSegmentId },
          );


        setAdCopy((prev) => ({
          ...prev,
          leadGenForm: leadGenFormUrn,
        }));

        return { previousData };
      },

      onError: (_err, _newData, context) => {
        if (context?.previousData) {
          apiUtils.v2.ads.socialPostBaseCopy.getAdSegmentSocialPostBaseCopy.setData(
            { adSegmentId },
            context.previousData,
          );
        }

        setAdCopy((prev) => ({
          ...prev,
          leadGenForm: null,
        }));
      },

      onSettled: () => {
        // Only invalidate the specific query
        apiUtils.v2.ads.socialPostBaseCopy.getAdSegmentSocialPostBaseCopy.invalidate(
          { adSegmentId },
        );
      },
    });

  /**
   * Mutation for updating the ad copy (title and body)
   */
  const updateAdCopy =
    api.v2.ads.socialPostBaseCopy.updateSocialPostCopy.useMutation({
      onMutate: async ({ linkedInAdSegmentValuePropId, body, title, copyType, callToActionType }) => {
        await apiUtils.v2.ads.socialPostBaseCopy.getAdSegmentSocialPostBaseCopy.cancel(
          { adSegmentId },
        );

        const previousData =
          apiUtils.v2.ads.socialPostBaseCopy.getAdSegmentSocialPostBaseCopy.getData(
            { adSegmentId },
          );

        // Optimistically update the cache and local state
        setAdCopy((prev) => ({
          ...prev,
          title,
          body,
        }));

        return { previousData, previousAdCopy: { ...adCopy } };
      },

      onSuccess: async (data) => {
        // Only invalidate specific queries that need updates
        await Promise.all([
          apiUtils.v2.ads.socialPostBaseCopy.getAdSegmentSocialPostBaseCopy.invalidate({
            adSegmentId: adSegmentId,
          }),
          apiUtils.v2.ads.adSegmentValueProp.getAdSegmentValuePropsForAdSegment.invalidate({
            adSegmentId: adSegmentId,
            status: "DRAFT",
          }),
        ]);

        setIsEditing(false);
        setIsSaving(false);
      },

      onError: (error, variables, context) => {
        if (context?.previousData) {
          apiUtils.v2.ads.socialPostBaseCopy.getAdSegmentSocialPostBaseCopy.setData(
            { adSegmentId },
            context.previousData,
          );
        }

        if (context?.previousAdCopy) {
          setAdCopy(context.previousAdCopy);
        }

        setIsSaving(false);
      },

      onSettled: () => {
        setIsSaving(false);
      },
    });

  /**
   * Fetches and processes ad copy from the streaming API
   */
  async function getAdCopy() {
    setAdCopy({
      title: "",
      body: "",
      done: false,
      leadGenForm: null,
    });
    setGenerating(true);
    try {
      const response = await fetch(`${getBaseUrl()}/stream`, {
        method: "POST",
        headers: {
          Accept: "application/json, text/plain, */*",
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          valuePropId: valuePropId,
          organizationId: organizationUser.data?.organizationId,
        }),
      });

      if (!response.ok || !response.body) {
        throw response.statusText;
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      const loopRunner = true;

      while (loopRunner) {
        const { value, done } = await reader.read();
        if (done) {
          break;
        }
        const decodedChunk = decoder
          .decode(value, { stream: true })
          .split("!JSON_LINE_END!");
        for (const each of decodedChunk) {
          try {
            const json = JSON.parse(each);
            setAdCopy((prev) => ({
              title: prev.title + json.title,
              body: prev.body + json.body,
              done: prev.done + json.done,
              leadGenForm: json.leadGenFormUrn || prev.leadGenForm,
            }));
            if (json.leadGenFormUrn) {
              console.log("LEAD GEN FORM", json.leadGenFormUrn);
            }
          } catch (e) { }
        }
      }
    } catch (error) {
      console.error("Error generating ad copy:", error);
    } finally {
      setGenerating(false);
    }
  }

  /**
   * Submits feedback to refine the ad copy
   *
   * @param feedback - The feedback to apply to the current copy
   */
  async function submitFeedback(feedback: string) {
    const baseBody = feedbackMode === "body" ? adCopy.body : adCopy.title;

    if (feedbackMode === "body") {
      setAdCopy((prev) => ({ ...prev, body: "" }));
    } else {
      setAdCopy((prev) => ({ ...prev, title: "" }));
    }

    try {
      const response = await fetch(`${getBaseUrl()}/stream-refined`, {
        method: "POST",
        headers: {
          Accept: "application/json, text/plain, */*",
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          baseCopy: baseBody,
          feedback: feedback,
          config: {
            type: "socialPost",
            field: feedbackMode,
          },
        }),
      });

      if (!response.ok || !response.body) {
        throw response.statusText;
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      const loopRunner = true;

      while (loopRunner) {
        const { value, done } = await reader.read();
        if (done) {
          break;
        }
        const decodedChunk = decoder.decode(value, { stream: true });
        for (const each of decodedChunk) {
          try {
            if (feedbackMode === "body") {
              setAdCopy((prev) => ({
                ...prev,
                body: prev.body + each,
              }));
            } else {
              setAdCopy((prev) => ({
                ...prev,
                title: prev.title + each,
              }));
            }
          } catch (e) { }
        }
      }
    } catch (error) {
      console.error("Error submitting feedback:", error);
    }
  }

  /**
   * Effect to set up ad copy generation when data is available
   */
  useEffect(() => {
    if (baseCopyQuery.data && organizationUser.data?.organizationId) {
      setBaseCopyRefetchInterval(undefined);
      setReadyToGetAdCopy(true);
    }
  }, [baseCopyQuery.data, organizationUser.data]);

  /**
   * Effect to trigger ad copy generation when ready
   */
  useEffect(() => {
    if (readyToGetAdCopy && !getCopyRan) {
      getAdCopy();
      setGetCopyRan(true);
    }
  }, [readyToGetAdCopy, getCopyRan]);

  /**
 * Saves the current ad copy
 */
  const saveAdCopy = async (): Promise<void> => {
    return new Promise((resolve, reject) => {
      let completedTasks = 0;
      const totalTasks = adCopy.leadGenForm ? 2 : 1;
      let hasError = false;

      const checkCompletion = () => {
        completedTasks++;
        if (completedTasks === totalTasks && !hasError) {
          resolve();
        }
      };

      const handleError = (error: any) => {
        if (!hasError) {
          hasError = true;
          reject(error);
        }
      };

      // Always save the ad copy (body and title)
      const mutationData = {
        linkedInAdSegmentValuePropId: valuePropId,
        copyType: "standard",
        body: adCopy.body,
        title: adCopy.title,
        callToActionType: "Standard",
      };
      updateAdCopy.mutate(mutationData, {
        onSuccess: () => {
          checkCompletion();
        },
        onError: handleError
      });

      // Also save lead gen form if one is selected
      if (adCopy.leadGenForm) {
        updateLeadGenFormUrn.mutate({
          linkedInAdSegmentValuePropId: valuePropId,
          leadGenFormUrn: adCopy.leadGenForm,
        }, {
          onSuccess: () => {
            checkCompletion();
          },
          onError: handleError
        });
      }
    });
  };

  /**
   * Cancels editing mode without saving changes
   */
  const cancelEditing = () => {
    setIsEditing(false);
  };

  /**
   * Handles saving a lead generation form
   *
   * @param leadGenFormUrn - The URN of the lead generation form
   */
  function handleSaveLeadGenForm(leadGenFormUrn: string) {
    setAdCopy((prev) => ({
      ...prev,
      leadGenForm: leadGenFormUrn,
    }));

    updateLeadGenFormUrn.mutate(
      {
        linkedInAdSegmentValuePropId: valuePropId,
        leadGenFormUrn: leadGenFormUrn,
      },
      {
        onSuccess: () => {
          // Lead gen form updated successfully
        },
        onError: (error) => {
          console.error("Lead gen form update failed:", error);
        },
        onSettled: () => { },
      },
    );
  }

  return {
    adCopy,
    setAdCopy,
    generating,
    isEditing,
    setIsEditing,
    isSaving,
    feedbackMode,
    setFeedbackMode,
    getAdCopy,
    submitFeedback,
    saveAdCopy,
    cancelEditing,
    adProgramQuery,
    handleSaveLeadGenForm,
  };
}
