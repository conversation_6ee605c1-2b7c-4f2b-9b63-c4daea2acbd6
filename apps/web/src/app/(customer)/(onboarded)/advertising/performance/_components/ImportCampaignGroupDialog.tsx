import { useEffect, useState } from "react";
import { api } from "@/trpc/client";
import {
  CheckCircleIcon,
  Loader2Icon,
  PlusIcon,
  XCircleIcon,
} from "lucide-react";

import { Button } from "@kalos/ui/button";
import {
  <PERSON><PERSON>,
  Di<PERSON>Close,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@kalos/ui/dialog";
import { Input } from "@kalos/ui/input";
import { Label } from "@kalos/ui/label";

// Type for campaign group creation response
type CampaignGroupCreationResponse = {
  success: boolean;
  message?: string;
  campaignGroup?: {
    id: string;
    name: string;
    status: string;
    linkedInAdAccountId: string | null;
    createdFromLinkedIn?: boolean;
  };
  error?: string;
};

// Define preview types
type CampaignPreview = {
  id: string;
  name: string;
  status?: string;
};

type AdCreativePreview = {
  id: string;
  name?: string;
  type?: string;
  campaignId: string;
  campaignName?: string;
};

type PreviewData = {
  campaignGroup: {
    id: string;
    name: string;
    status?: string;
  };
  campaignsCount: number;
  campaigns: CampaignPreview[];
  adCreativesCount: number;
  adCreatives: AdCreativePreview[];
  hasData: boolean;
  error?: string;
};

type ImportCampaignGroupDialogProps = {
  selectedAdAccount: string | null;
  onSuccessfulImport: () => void;
};

export function ImportCampaignGroupDialog({
  selectedAdAccount,
  onSuccessfulImport,
}: ImportCampaignGroupDialogProps) {
  const [open, setOpen] = useState(false);
  const [campaignGroupId, setCampaignGroupId] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isPreviewLoading, setIsPreviewLoading] = useState(false);
  const [importStatus, setImportStatus] = useState<
    "idle" | "preview" | "success" | "error"
  >("idle");
  const [errorMessage, setErrorMessage] = useState("");
  const [previewData, setPreviewData] = useState<PreviewData | null>(null);
  const [existingCampaignGroup, setExistingCampaignGroup] =
    useState<CampaignPreview | null>(null);

  // Reset dialog state when closed
  useEffect(() => {
    if (!open) {
      setCampaignGroupId("");
      setImportStatus("idle");
      setErrorMessage("");
      setPreviewData(null);
      setIsSubmitting(false);
      setIsPreviewLoading(false);
    }
  }, [open]);

  const campaignGroupPreviewQuery =
    api.v2.ads.linkedInCampaignGroup.previewCampaignGroup.useQuery(
      {
        campaignGroupId,
        linkedInAdAccountId: selectedAdAccount || "",
      },
      {
        enabled: false, // Don't run automatically
        retry: false,
      },
    );

  const checkCampaignGroupExistsQuery =
    api.v2.ads.linkedInCampaignGroup.checkCampaignGroupExists.useQuery(
      {
        campaignGroupId,
        linkedInAdAccountId: selectedAdAccount || "",
      },
      {
        enabled: false, // Don't run automatically
        retry: false,
      },
    );

  const campaignGroupIngestionMutation =
    api.v2.ads.linkedInCampaignGroup.createCampaignGroup.useMutation();

  // Add state to track if campaign group already exists
  const [campaignGroupExists, setCampaignGroupExists] = useState(false);

  const handlePreview = async () => {
    if (!campaignGroupId) {
      setImportStatus("error");
      setErrorMessage("Please enter a campaign group ID");
      return;
    }

    setIsPreviewLoading(true);
    setImportStatus("idle");
    setErrorMessage("");
    setPreviewData(null);
    setCampaignGroupExists(false);

    try {
      // First check if campaign group already exists using the dedicated endpoint
      const checkExistsResult = await checkCampaignGroupExistsQuery.refetch();

      // If the campaign group exists
      if (checkExistsResult.data && checkExistsResult.data.exists) {
        setCampaignGroupExists(true);
        setExistingCampaignGroup(checkExistsResult.data.campaignGroup);
      } else {
        // Campaign group doesn't exist, get preview data
        const result = await campaignGroupPreviewQuery.refetch();
        if (result.data) {
          setPreviewData(result.data);
          setImportStatus("preview");
        } else {
          setImportStatus("error");
          setErrorMessage("Failed to preview campaign group");
        }
      }
    } catch (error) {
      setImportStatus("error");
      setErrorMessage(
        error instanceof Error ? error.message : "An unknown error occurred",
      );
    } finally {
      setIsPreviewLoading(false);
    }
  };

  // Reset preview when campaign group ID changes
  useEffect(() => {
    // Only reset if we already have preview data and the ID changes
    if (
      (importStatus === "preview" || importStatus === "error") &&
      previewData
    ) {
      // If the ID changes from what was previously previewed
      if (campaignGroupId !== previewData.campaignGroup.id) {
        // Reset to allow previewing the new ID
        setImportStatus("idle");
        setCampaignGroupExists(false);
      }
    }
  }, [campaignGroupId, importStatus, previewData]);

  const handleImport = async () => {
    if (!campaignGroupId || !previewData) {
      setImportStatus("error");
      setErrorMessage("Please enter a campaign group ID and preview first");
      return;
    }

    // Prevent import if campaign group already exists
    if (campaignGroupExists) {
      setImportStatus("error");
      setErrorMessage("This campaign group already exists in the database");
      return;
    }

    setIsSubmitting(true);
    setImportStatus("idle");
    setErrorMessage("");

    try {
      // Use the selected ad account ID from props and data from the preview
      const result = (await campaignGroupIngestionMutation.mutateAsync({
        campaignGroupId,
        linkedInAdAccountId: selectedAdAccount || "",
      })) as CampaignGroupCreationResponse;

      if (
        result &&
        !result.success &&
        result.message === "Campaign group already exists"
      ) {
        setCampaignGroupExists(true);
        setImportStatus("error");
        setErrorMessage("This campaign group already exists in the database");
      } else {
        setCampaignGroupExists(false);
        setImportStatus("success");
        onSuccessfulImport();
      }
    } catch (error) {
      setImportStatus("error");
      setErrorMessage(
        error instanceof Error ? error.message : "An unknown error occurred",
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button size={"sm"} variant={"outline"}>
          <PlusIcon size={10} className="text-muted-foreground" />{" "}
          <span className="text-xs text-muted-foreground">
            {" "}
            Campaign Group{" "}
          </span>
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle>Add LinkedIn Campaign Group</DialogTitle>
          <p className="text-sm text-muted-foreground">
            Enter the LinkedIn Campaign Group ID to import campaigns and ads for
            that group
          </p>
        </DialogHeader>

        <div>
          <Label className="mb-0">Campaign Group ID</Label>
          <Input
            placeholder="Enter Campaign Group ID e.g 729058786"
            className="mt-0"
            value={campaignGroupId}
            onChange={(e) => setCampaignGroupId(e.target.value)}
            onKeyDown={(e) => {
              // Allow pressing Enter to trigger preview
              if (e.key === "Enter" && !isPreviewLoading && campaignGroupId) {
                e.preventDefault();
                handlePreview();
              }
            }}
          />
        </div>

        <div className="mt-4 rounded-md border border-gray-200 p-4">
          <h3 className="mb-4 text-lg font-semibold">Campaign Group Preview</h3>
          <div className="max-h-[50vh] overflow-y-auto pr-1">
            {importStatus === "preview" && previewData && (
              <>
                {/* Campaign Group Section */}
                <div className="mb-4 rounded-md bg-gray-50 p-3">
                  <h4 className="mb-2 border-b pb-1 text-sm font-medium uppercase text-gray-600">
                    Campaign Group
                  </h4>
                  <div className="flex flex-wrap items-center gap-1">
                    <span className="font-medium">
                      {previewData.campaignGroup.name}
                    </span>
                    {previewData.campaignGroup.status && (
                      <span className="rounded-full bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-800">
                        {previewData.campaignGroup.status}
                      </span>
                    )}
                  </div>
                  <div className="mt-1 text-sm text-gray-500">
                    ID: {previewData.campaignGroup.id}
                  </div>
                </div>

                {/* Campaigns Section */}
                <div className="mb-4 rounded-md bg-gray-50 p-3">
                  <div className="mb-2 flex items-center justify-between border-b pb-1">
                    <h4 className="text-sm font-medium uppercase text-gray-600">
                      Campaigns
                    </h4>
                    <span className="rounded-full bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-800">
                      {previewData.campaignsCount}
                    </span>
                  </div>

                  {previewData.campaignsCount === 0 ? (
                    <div className="py-2 text-sm text-amber-600">
                      No campaigns found in this group
                    </div>
                  ) : (
                    <div className="mt-2 max-h-40 overflow-y-auto rounded-md border bg-white p-2">
                      <ul className="space-y-1 text-sm">
                        {previewData.campaigns.map(
                          (campaign: CampaignPreview) => (
                            <li
                              key={campaign.id}
                              className="flex items-center justify-between rounded-sm p-1 hover:bg-gray-50"
                            >
                              <span>{campaign.name}</span>
                              {campaign.status && (
                                <span className="rounded-full bg-gray-100 px-2 py-0.5 text-xs font-medium">
                                  {campaign.status}
                                </span>
                              )}
                            </li>
                          ),
                        )}
                      </ul>
                    </div>
                  )}
                </div>

                {/* Ad Creatives Section */}
                <div className="rounded-md bg-gray-50 p-3">
                  <div className="mb-2 flex items-center justify-between border-b pb-1">
                    <h4 className="text-sm font-medium uppercase text-gray-600">
                      Ad Creatives
                    </h4>
                    <span className="rounded-full bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-800">
                      {previewData.adCreativesCount}
                    </span>
                  </div>

                  {previewData.adCreativesCount === 0 ? (
                    <div className="py-2 text-sm text-amber-600">
                      No ad creatives found in this group
                    </div>
                  ) : (
                    <div className="mt-2 max-h-48 overflow-y-auto rounded-md border bg-white p-2">
                      <ul className="space-y-1 text-sm">
                        {previewData.adCreatives.map(
                          (adCreative: AdCreativePreview) => (
                            <li
                              key={adCreative.id}
                              className="flex flex-col rounded-sm p-1.5 hover:bg-gray-50"
                            >
                              <div className="flex items-center justify-between">
                                <span className="font-medium">
                                  {adCreative.name ||
                                    `Ad ${adCreative.id.substring(0, 8)}`}
                                </span>
                                {adCreative.type && (
                                  <span className="rounded-full bg-gray-100 px-2 py-0.5 text-xs font-medium">
                                    {adCreative.type}
                                  </span>
                                )}
                              </div>
                              {adCreative.campaignName && (
                                <div className="mt-0.5 text-xs text-gray-500">
                                  Campaign: {adCreative.campaignName}
                                </div>
                              )}
                            </li>
                          ),
                        )}
                      </ul>
                    </div>
                  )}
                </div>
              </>
            )}

            {campaignGroupExists && existingCampaignGroup && (
              <div className="mt-4 rounded-md bg-amber-50 p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <XCircleIcon
                      className="h-5 w-5 text-amber-400"
                      aria-hidden="true"
                    />
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-amber-800">
                      Campaign Group "{existingCampaignGroup.name}" Already
                      Exists
                    </h3>
                    <div className="mt-2 text-sm text-amber-700">
                      {/* <p>This campaign group already exists.</p> */}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {importStatus === "success" && (
          <div className="mt-4 flex items-center text-green-600">
            <CheckCircleIcon className="mr-2 h-5 w-5" />
            <span>Campaign group imported successfully!</span>
          </div>
        )}

        {importStatus === "error" && !campaignGroupExists && (
          <div className="mt-4 flex items-center text-red-600">
            <XCircleIcon className="mr-2 h-5 w-5" />
            <span>{errorMessage || "Failed to import campaign group"}</span>
          </div>
        )}

        <DialogFooter className="mt-6">
          <div className="flex w-full justify-between">
            <DialogClose>
              <Button variant="outline">Cancel</Button>
            </DialogClose>

            <div className="flex gap-2">
              {/* Show Preview button when not already previewing */}
              {importStatus !== "preview" && (
                <Button
                  onClick={handlePreview}
                  disabled={isPreviewLoading || !campaignGroupId}
                >
                  {isPreviewLoading ? (
                    <Loader2Icon className="mr-2 h-4 w-4 animate-spin" />
                  ) : null}
                  Preview
                </Button>
              )}

              {/* Show Import button only when preview is successful and group doesn't exist */}
              {importStatus === "preview" &&
                previewData &&
                !campaignGroupExists && (
                  <Button
                    onClick={handleImport}
                    disabled={isSubmitting || campaignGroupExists}
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2Icon className="mr-2 h-4 w-4 animate-spin" />
                        Importing...
                      </>
                    ) : (
                      "Import Campaign Group"
                    )}
                  </Button>
                )}
            </div>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
