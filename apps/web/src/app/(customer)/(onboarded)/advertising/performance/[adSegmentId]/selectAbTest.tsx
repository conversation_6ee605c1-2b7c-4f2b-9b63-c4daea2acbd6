import { useState } from "react";
import { api } from "@/trpc/client";
import { LoaderCircleIcon } from "lucide-react";

import { Button } from "@kalos/ui/button";
import { Card, CardHeader, CardTitle } from "@kalos/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@kalos/ui/dialog";
import { RadioGroup, RadioGroupItem } from "@kalos/ui/radiogroup";

import { AbTestType } from "../../../../../../../../../backend/src/modules/advertising/abTest/internal/domain/abTestType.valueObject";

export default function SelectAbTestModal({
  adFormat,
  adSegmentId,
}: {
  adFormat: "SINGLE_IMAGE" | "DOCUMENT" | "VIDEO" | "SPONSORED_INMAIL";
  adSegmentId: string;
}) {
  const [showModal, setShowModal] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const sponsoredContentAbTests: AbTestType[] = [
    "audience",
    "valueProp",
    "creative",
    "socialPostBodyCopy",
    "socialPostCallToAction",
  ];

  const sponsoredInmailAbTests: AbTestType[] = [
    "audience",
    "valueProp",
    "conversationSubject",
    "conversationMessageCopy",
    "conversationCallToAction",
  ];

  function camelCaseToWords(abTest: string): string {
    return abTest
      .replace(/([a-z])([A-Z])/g, "$1 $2")
      .replace(/^./, (str) => str.toUpperCase())
      .replace(/ ([a-z])/g, (match) => match.toUpperCase());
  }

  const [abTestType, setAbTestType] = useState<AbTestType | "">("");

  const apiUtils = api.useUtils();

  const mutation =
    api.v2.ads.newAbTestController.runDifferentAbTest.useMutation({
      onSuccess: async () => {
        await apiUtils.v2.ads.newAbTestController.invalidate();
        setIsSubmitting(false);
        setShowModal(false);
      },
    });

  return (
    <Dialog open={showModal} onOpenChange={setShowModal}>
      <DialogTrigger asChild>
        <Button variant="outline" className="">
          <span className="text-xs">Select Learning Goal</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="max-h-[80vh] max-w-4xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold"></DialogTitle>
          <DialogDescription>Select Learning goal to run</DialogDescription>
        </DialogHeader>
        <RadioGroup
          value={abTestType}
          onValueChange={setAbTestType as (value: string) => void}
        >
          {adFormat !== "SPONSORED_INMAIL" &&
            sponsoredContentAbTests.map((abTest) => (
              <Card
                key={abTest}
                className="flex items-center justify-between pr-4"
              >
                <CardHeader>
                  <CardTitle>{camelCaseToWords(abTest)}</CardTitle>
                </CardHeader>
                <RadioGroupItem value={abTest}></RadioGroupItem>
              </Card>
            ))}
          {adFormat === "SPONSORED_INMAIL" &&
            sponsoredInmailAbTests.map((abTest) => (
              <Card
                key={abTest}
                className="flex items-center justify-between pr-4"
              >
                <CardHeader>
                  <CardTitle>{camelCaseToWords(abTest)}</CardTitle>
                </CardHeader>
                <RadioGroupItem value={abTest}></RadioGroupItem>
              </Card>
            ))}
        </RadioGroup>
        <div className="flex justify-end gap-2 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => setShowModal(false)}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            onClick={() => {
              if (!abTestType) {
                return;
              }
              if (
                abTestType !== "audience" &&
                abTestType !== "valueProp" &&
                abTestType !== "creative" &&
                abTestType !== "conversationSubject" &&
                abTestType !== "conversationMessageCopy" &&
                abTestType !== "conversationCallToAction" &&
                abTestType !== "socialPostBodyCopy" &&
                abTestType !== "socialPostCallToAction"
              ) {
                alert(`${abTestType} is not a valid ab test type}`);
                return;
              }
              setIsSubmitting(true);
              mutation.mutate({
                adSegmentId: adSegmentId,
                abTestType: abTestType,
              });
            }}
            disabled={isSubmitting}
          >
            {isSubmitting && (
              <LoaderCircleIcon className="mr-2 h-4 w-4 animate-spin" />
            )}
            Submit
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
