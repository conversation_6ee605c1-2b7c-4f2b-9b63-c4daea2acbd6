"use client";

import { useEffect, useState } from "react";
import { api } from "@/trpc/client";

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@kalos/ui/dialog";
import { Skeleton } from "@kalos/ui/skeleton";

interface LinkedInAdPreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  adCreativeUrn: string;
  adTitle: string;
  adFormat?: string;
  segmentId?: string;
  adData?: {
    title?: string;
    description?: string;
    imageUrl?: string;
    companyName?: string;
    destinationUrl?: string;
  };
}

export default function LinkedInAdPreviewModal({
  isOpen,
  onClose,
  adCreativeUrn,
  adTitle,
  adFormat,
  segmentId,
  adData,
}: LinkedInAdPreviewModalProps) {
  const [previews, setPreviews] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const organizationQuery = api.user.organizationUser.get.organization.useQuery();

  const sponsoredInmailQuery = api.advertising.linkedIn.getSponsoredInmailContent.useQuery(
    { adCreativeUrn },
    { 
      enabled: isOpen && adFormat === "SPONSORED_INMAIL" && !!adCreativeUrn,
      refetchOnMount: 'always',
      staleTime: 0,
    }
  );

  const singleImageQuery = api.advertising.linkedIn.getSingleImageContent.useQuery(
    { adCreativeUrn },
    { 
      enabled: isOpen && adFormat === "SINGLE_IMAGE" && !!adCreativeUrn,
      refetchOnMount: 'always',
      staleTime: 0,
    }
  );

  // Fetch ad preview when modal opens
  useEffect(() => {
    if (isOpen && adCreativeUrn && organizationQuery.data?.organizationId) {      
      // Only fetch iframe preview if it's not a custom preview format
      if (adFormat !== "SINGLE_IMAGE" && adFormat !== "SPONSORED_INMAIL") {
        fetchAdPreview();
      }
    }
  }, [isOpen, adCreativeUrn, organizationQuery.data?.organizationId, adFormat, segmentId]);

  const fetchAdPreview = async () => {
    if (!organizationQuery.data?.organizationId) return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch("/api/v1/analytics/ads/preview", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          organizationId: organizationQuery.data?.organizationId,
          adCreativeLinkedInUrn: adCreativeUrn,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to fetch ad preview");
      }
      const data = await response.json();
      setPreviews(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load preview");
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setPreviews({});
    setError(null);
    onClose();
  };

  const getPreviewUrl = () => {
    if (previews.STANDARD) return previews.STANDARD;
    if (previews.MOBILE) return previews.MOBILE;
    if (previews.DESKTOP) return previews.DESKTOP;
    
    // Return the first available preview
    const keys = Object.keys(previews);
    const firstKey = keys[0];
    return keys.length > 0 && firstKey ? previews[firstKey] : null;
  };

  const previewUrl = getPreviewUrl();

  // Custom LinkedIn Sponsored InMail Preview Component
  const SponsoredInMailPreview = () => {
    const sponsoredInmailData = sponsoredInmailQuery.data; 
    
    // If query is still loading, return null to show skeleton
    if (sponsoredInmailQuery.isLoading) {
      return null;
    }
    
         // If query failed or no data, show fallback content
     const subjectLine = sponsoredInmailData?.subjectLine || "subject NA";
     const messageContent = sponsoredInmailData?.messageContent || "message N/A";
     const callToActionButtons = sponsoredInmailData?.callToActionButtons || [
       { content: "CTA N/A", type: "primary" },
     ];

    // Split message content into paragraphs
    const messageParagraphs = messageContent.split('\n\n').filter(p => p.trim() !== '');

    return (
      <div className="w-4/5 max-w-3xl mx-auto bg-white border border-gray-200 rounded-lg shadow-sm">
        {/* Message Content */}
        <div className="p-4" style={{ backgroundColor: '#e8f3ff' }}>
          <div className="space-y-4">
            <div className="flex space-x-3">
              {/* TODO: Uncomment when user logo data is available */}
              {/* <div className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center flex-shrink-0 overflow-hidden">
                <img 
                  src="/no-profile-image.png" 
                  alt="Profile" 
                  className="w-full h-full object-cover"
                />
              </div> */}
              <div className="flex-1 rounded-lg p-3">
                {/* TODO: Uncomment when sender data is available */}
                {/* <h6 className="font-medium text-gray-900 text-sm mb-1">&lt;Sender&gt;</h6> */}
                <div className="text-sm text-gray-700 space-y-2">
                  {messageParagraphs.map((paragraph, index) => (
                    <p key={index}>{paragraph}</p>
                  ))}
                </div>
                <div className="mt-4 space-y-2 flex flex-col items-start">
                  {callToActionButtons.map((button, index) => (
                    <button 
                      key={index}
                      className="px-4 py-2 border border-blue-600 text-blue-600 rounded-lg font-medium text-sm hover:bg-blue-50 transition-colors"
                      style={{ backgroundColor: '#e8f3ff' }}
                    >
                      {button.content}
                    </button>
                  ))}
                  <button 
                    className="px-4 py-2 border border-blue-600 text-blue-600 rounded-lg font-medium text-sm hover:bg-blue-50 transition-colors"
                    style={{ backgroundColor: '#e8f3ff' }}
                  >
                    Not interested
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Custom LinkedIn Ad Preview Component for SINGLE_IMAGE format
  const CustomLinkedInPreview = () => {
    const singleImageData = singleImageQuery.data;
        
    // Use data from query if available, otherwise fallback to adData prop or defaults
    const description = singleImageData?.description || adData?.description || "N/A";
    const ctaText = singleImageData?.ctaText || "Learn more";
    const destinationUrl = singleImageData?.destinationUrl || adData?.destinationUrl || "getkalos.com";
    const imageUrl = singleImageData?.imageUrl || adData?.imageUrl;
    
    // Use the image URL directly if it's already a full URL (presigned URL), otherwise build S3 URL
    const fullImageUrl = imageUrl 
      ? (imageUrl.startsWith('http') ? imageUrl : `https://kalos-ad-creatives.s3.amazonaws.com/${imageUrl}`)
      : null;
    

    // This comes from the ad title or a default
    const footerTitle = adData?.title || adTitle || "Boost Your Site Traffic 🚀 Discover Proven Strategies Today";
        
    return (
      <div className="w-4/5 max-w-3xl mx-auto bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm">
        {/* Header */}
        <div className="flex items-start p-4 space-x-3">
          <div className="w-10 h-10 bg-blue-600 rounded flex items-center justify-center text-white font-bold text-sm">
            K
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex flex-col">
              <h3 className="font-semibold text-gray-900 dark:text-white text-sm">Kalos</h3>
              <span className="text-gray-500 dark:text-gray-400 text-xs">437 followers</span>
              <span className="text-gray-500 dark:text-gray-400 text-xs">Promoted</span>
            </div>
          </div>
        </div>

        {/* Post Content */}
        <div className="px-4 pb-4">
          <p className="text-gray-900 dark:text-white leading-relaxed text-sm">
            {description}
          </p>
        </div>

        {/* Ad Image */}
        <div className="relative w-full">
          {fullImageUrl ? (
            <img 
              src={fullImageUrl}
              alt="Ad Creative" 
              className="w-full h-auto block"
              onError={(e) => {
                console.log('Image failed to load:', fullImageUrl);
                e.currentTarget.style.display = 'none';
                e.currentTarget.nextElementSibling?.classList.remove('hidden');
              }}
            />
          ) : null}
          
          {/* Fallback image when no image URL or image fails to load */}
          <div className={`w-full h-64 bg-gray-100 dark:bg-gray-800 flex items-center justify-center text-gray-500 dark:text-gray-400 ${fullImageUrl ? 'hidden' : ''}`}>
            <div className="text-center p-6">
              <p className="text-sm font-medium">Image N/A</p>
            </div>
          </div>
        </div>

        {/* Footer - This should show the CTA button */}
        <div className="p-4 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <span className="font-semibold text-gray-900 dark:text-white text-sm">
                {ctaText}
              </span>
              <p className="text-gray-500 dark:text-gray-400 text-xs truncate">
                {destinationUrl.replace('https://', '').replace('http://', '')}
              </p>
            </div>
            <button className="ml-3 px-3 py-1.5 bg-transparent border-2 border-blue-600 text-blue-600 dark:text-blue-400 dark:border-blue-400 rounded-full font-medium text-xs hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors whitespace-nowrap">
              Learn more
            </button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-lg font-semibold">
            LinkedIn Ad Preview
          </DialogTitle>
        </DialogHeader>
        
        <div className="flex flex-col space-y-4">
          {/* Show custom preview for SINGLE_IMAGE ads */}
          {adFormat === "SINGLE_IMAGE" && (
            <div className="w-full">
              {singleImageQuery.isLoading ? (
                <div className="w-4/5 max-w-3xl mx-auto bg-white border border-gray-200 rounded-lg shadow-sm p-4">
                  <div className="flex flex-col space-y-4">
                    {/* Header skeleton */}
                    <div className="flex items-center space-x-3">
                      <Skeleton className="h-12 w-12 rounded" />
                      <div className="flex-1 space-y-2">
                        <Skeleton className="h-4 w-32" />
                        <Skeleton className="h-3 w-24" />
                      </div>
                    </div>
                    {/* Content skeleton */}
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-3/4" />
                    </div>
                    {/* Image skeleton */}
                    <Skeleton className="h-64 w-full" />
                    {/* Footer skeleton */}
                    <div className="flex items-center justify-between">
                      <div className="flex-1 space-y-2">
                        <Skeleton className="h-4 w-3/4" />
                        <Skeleton className="h-3 w-1/2" />
                      </div>
                      <Skeleton className="h-8 w-20 rounded" />
                    </div>
                  </div>
                </div>
              ) : singleImageQuery.error ? (
                <div className="w-4/5 max-w-3xl mx-auto bg-red-50 border border-red-200 rounded-lg p-4">
                  <h3 className="text-red-800 font-medium mb-2">Error loading content</h3>
                  <p className="text-red-600 text-sm">
                    {singleImageQuery.error.message}
                  </p>
                  <p className="text-xs text-gray-500 mt-2">
                    URN: {adCreativeUrn}
                  </p>
                  <div className="mt-4">
                    <CustomLinkedInPreview />
                  </div>
                </div>
              ) : (
                <CustomLinkedInPreview />
              )}
              <p className="text-xs text-gray-500 mt-2 text-center">
                This is how your ad will appear on LinkedIn
                {singleImageQuery.data ? "" : " (using fallback content)"}
              </p>
            </div>
          )}

          {/* Show custom preview for SPONSORED_INMAIL ads */}
          {adFormat === "SPONSORED_INMAIL" && (
            <div className="w-full">
              {sponsoredInmailQuery.isLoading ? (
                <div className="w-4/5 max-w-3xl mx-auto bg-white border border-gray-200 rounded-lg shadow-sm p-4">
                  <div className="flex flex-col space-y-4">
                    <Skeleton className="h-4 w-48" />
                    <div className="flex items-center space-x-3">
                      <Skeleton className="h-10 w-10 rounded-full" />
                      <div className="flex-1 space-y-2">
                        <Skeleton className="h-4 w-32" />
                        <Skeleton className="h-3 w-48" />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-3/4" />
                    </div>
                    <div className="space-y-2">
                      <Skeleton className="h-8 w-full rounded-full" />
                      <Skeleton className="h-8 w-full rounded-full" />
                    </div>
                  </div>
                </div>
              ) : sponsoredInmailQuery.error ? (
                <div className="w-4/5 max-w-3xl mx-auto bg-red-50 border border-red-200 rounded-lg p-4">
                  <h3 className="text-red-800 font-medium mb-2">Error loading content</h3>
                  <p className="text-red-600 text-sm">
                    {sponsoredInmailQuery.error.message}
                  </p>
                  <p className="text-xs text-gray-500 mt-2">
                    URN: {adCreativeUrn}
                  </p>
                  <SponsoredInMailPreview />
                </div>
              ) : (
                <SponsoredInMailPreview />
              )}
              <p className="text-xs text-gray-500 mt-2 text-center">
                This is how your sponsored InMail will appear on LinkedIn
                {sponsoredInmailQuery.data ? "" : " (using fallback content)"}
              </p>
            </div>
          )}

          {/* Show iframe preview for other ad formats */}
          {adFormat !== "SINGLE_IMAGE" && adFormat !== "SPONSORED_INMAIL" && (
            <>
              {isLoading && (
                <div className="flex flex-col space-y-2">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-64 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                </div>
              )}

              {error && (
                <div className="flex items-center justify-center p-8">
                  <div className="text-center">
                    <p className="text-red-600 font-medium">Preview Unavailable</p>
                    <p className="text-sm text-gray-500 mt-2">{error}</p>
                  </div>
                </div>
              )}

              {!isLoading && !error && previewUrl && (
                <div className="w-full">
                  <iframe
                    src={previewUrl}
                    className="w-full h-96 border border-gray-200 rounded-lg"
                    title={`LinkedIn Ad Preview - ${adTitle}`}
                    sandbox="allow-same-origin allow-scripts"
                  />
                  <p className="text-xs text-gray-500 mt-2">
                    This is how your ad will appear on LinkedIn
                  </p>
                </div>
              )}

              {!isLoading && !error && !previewUrl && Object.keys(previews).length === 0 && (
                <div className="flex items-center justify-center p-8">
                  <div className="text-center">
                    <p className="text-gray-600 font-medium">No Preview Available</p>
                    <p className="text-sm text-gray-500 mt-2">
                      The ad preview could not be generated at this time
                    </p>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
} 