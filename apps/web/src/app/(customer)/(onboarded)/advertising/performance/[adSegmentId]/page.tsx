"use client";

import { useEffect, useMemo, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { api } from "@/trpc/client";

import AdSegmentDetailPage from "../_components/AdProgram";
// Import custom components
import CampaignGroup from "../_components/CampaignGroup";

export default function PerformanceDetails({
  params,
}: {
  params: { adSegmentId: string };
}) {
  const searchParams = useSearchParams();
  const type = searchParams.get("type");

  if (type === "adProgram") {
    return (
      <AdSegmentDetailPage
        params={{ adSegmentId: params.adSegmentId }}
      ></AdSegmentDetailPage>
    );
  } else {
    const campaignGroupId = searchParams.get("campaignGroupId") || "";
    const objective = searchParams.get("objective") || "";
    return (
      <CampaignGroup
        objective={objective}
        campaignGroupId={campaignGroupId}
      ></CampaignGroup>
    );
  }
}
