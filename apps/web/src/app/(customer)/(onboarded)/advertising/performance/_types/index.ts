import { AdSegmentValueProp } from "../../../../../../../../../backend/src/modules/advertising/domain/entites/adSegmentValueProp";

// AB Test Related Types
export type AbTestRoundDay = {
  id: string;
  abTestRoundId: string;
  dayIndex: number;
  deploymentConfigId: string;
  status: "NOT_STARTED" | "IN_PROGRESS" | "FAILED" | "CANCELLED";
  winner?: "CURRENT_BEST" | "CONTENDER" | null;
  currentBestResult?: number | null;
  contenderResult?: number | null;
};

export type AbTestRound = {
  id: string;
  abTestId: string;
  currentBestId: string;
  contenderId: string;
  roundIndex: number;
  status: "NOT_STARTED" | "IN_PROGRESS" | "FAILED" | "CANCELLED";
  winner?: "CURRENT_BEST" | "CONTENDER" | null;
  roundDays: AbTestRoundDay[];
};

export type AdFormatType =
  | "SINGLE_IMAGE"
  | "VIDEO"
  | "DOCUMENT"
  | "SPONSORED_CONVERSATION"
  | "SPONSORED_INMAIL"
  | "CAROUSEL_IMAGE"
  | "TEXT";

export type AdProgramObjectiveType =
  | "BRAND_AWARENESS"
  | "CREATIVE_ENGAGEMENT"
  | "ENGAGEMENT"
  | "JOB_APPLICANT"
  | "LEAD_GENERATION"
  | "TALENT_LEAD"
  | "VIDEO_VIEW"
  | "WEBSITE_CONVERSION"
  | "WEBSITE_TRAFFIC"
  | "WEBSITE_VISIT";

export interface AdFormat {
  type: "SPONSORED_CONTENT" | "SPONSORED_CONVERSATION" | "SPONSORED_INMAIL";
  format: AdFormatType;
}

export type RawMetrics = {
  sponsoredCreatieUrn?: string;
  clicks?: number;
  impressions?: number;
  landingPageClicks?: number;
  costInUsd?: number;
  oneClickLeads?: number;
  oneClickLeadFormOpens?: number;
  totalEngagements?: number;
  actionClicks?: number;
  leads?: number;
  videoStarts?: number;
  videoCompletions?: number;
  videoFirstQuartileCompletions?: number;
  videoMidpointCompletions?: number;
  videoThirdQuartileCompletions?: number;
  externalWebsiteConversions?: number;
  videoViews?: number;
  sends?: number;
  opens?: number;
};

// Standard metrics format used by components
export type Metrics = {
  impressions?: number;
  clicks?: number;
  ctr?: number;
  cpc?: number;
  cpm?: number;
  engagements?: number;
  landingPageClicks?: number;
  actionClicks?: number;
  totalEngagements?: number;
  oneClickLeadFormOpens?: number;
  videoStarts?: number;
  videoCompletions?: number;
  videoFirstQuartileCompletions?: number;
  videoMidpointCompletions?: number;
  videoThirdQuartileCompletions?: number;
  videoViews?: number;
  costPerLead?: number;
  costPerEngagement?: number;
  clicksToLinkedinPage?: number;
  costInUsd?: number;
  leadFormOpens?: number;
  leadFormCompletionRate?: number;
  leads?: number;
  oneClickLeads?: number;
  sends?: number;
  opens?: number;
  openRate?: number;
  buttonClicks?: number;
  clickToOpenRate?: number;

  spend?: number;
};

export type AdVariant = {
  sponsoredCreativeId?: string;
  adVarients?: Record<string, string>;
  metrics?: Metrics | RawMetrics;
  title?: string;
  description?: string;
  imageUrl?: string;
  id?: string;
  urn?: string;
};

export type AbTestType =
  | "audience"
  | "valueProp"
  | "creative"
  | "conversationSubject"
  | "conversationMessageCopy"
  | "conversationCallToAction"
  | "socialPostBodyCopy"
  | "socialPostCallToAction";

export type StageType =
  | "audienceTest"
  | "valuePropTest"
  | "creativeTest"
  | "conversationSubjectTest"
  | "conversationCallToActionTest";
// Test variant type to represent specific test parameters
export type TestVariant = {
  type: string;
  value: string;
};

export type CurrentRunningAbTest = {
  stageId: string;
  type: AbTestType;
  status: "TO_DO" | "IN_PROGRESS" | "COMPLETED";
  rounds: AbTestRound[];
  ads: AdVariant[];
};

// Segment Related Types
export type SegmentDetail = {
  adProgramId: string;
  adSegmentId: string;
  name: string;
  budget: number;
  startDate: string;
  endDate?: string;
  adProgramType: "EVENT_DRIVEN" | "EVERGREEN";
  status: "ACTIVE" | "PAUSED" | "COMPLETED";
  metrics: {
    totalImpressions?: number;
    totalClicks?: number;
    totalEngagements?: number;
    totalLeads?: number;
    totalSpent?: number;
  };
};

// Learning Goals Types
export type LearningGoal = {
  stageId?: string;
  id: string;
  index: number;
  testTypes: AbTestType;
  format: string;
  title: string;
  valueProp?: string;
  creatives?: {};
  conversationSubjects?: {};
  startingIn?: number;
  dateRange?: string;
  testVariants?: TestVariant[]; // Added to support detailed test parameters
  campaign?: string; // Added to support targeting parameters from ad variants
};

// API Response Types
export type SegmentApiResponse = {
  segment?: {
    name?: string | null;
    status: string;
    id: string;
    // Include other properties that might be in the actual response
    organizationId?: number;
    verticals?: string[];
    annualRevenueLowBound?: number | null;
    jobSeniority?: string;
  };
  metrics?: {
    totalBudget?: number;
    totalImpressions?: number;
    totalClicks?: number;
    totalEngagements?: number;
    totalLeads?: number;
    totalSpent?: number;
    // Include any other metrics from the actual response
  };
  ads?: any[];
  abTests?: any[];
};

export type UpcomingTestApiResponse = {
  stageId: string;
  stageType: string;
  ads?: AdVariant[];
  rounds?: AbTestRound[];
  type?: AbTestType;
  status?: string;
};

// The actual response might have a different structure
export type RawUpcomingTestApiResponse = {
  stageId: string;
  stageType: string;
  valueProps?: AdSegmentValueProp[];
  creatives?: {};
  conversationSubjects?: {};
  ads?: {
    metrics?: {
      sponsoredCreatieUrn?: string;
      clicks?: number;
      impressions?: number;
      costInUsd?: number;
      oneClickLeads?: number;
      externalWebsiteConversions?: number;
      videoViews?: number;
      sends?: number;
      opens?: number;
    };
    id: string;
    urn: string;
    adVarients: Record<string, string>;
  }[];
  type?: AbTestType;
  status?: string;
  rounds?: AbTestRound[];
};
