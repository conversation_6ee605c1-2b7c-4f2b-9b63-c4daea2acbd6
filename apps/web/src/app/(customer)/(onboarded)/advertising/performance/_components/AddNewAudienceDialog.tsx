"use client";

import { useState } from "react";
import {
  AudienceTargetingDialog,
  handleAddCriteria,
  handleAddExcludeCriteria,
} from "@/app/(customer)/(onboarded)/advertising/adAccounts/[adAccount]/campaignGroups/[campaignGroupId]/audience/page";
import { AudienceTargetingCriteria } from "@/app/(customer)/(onboarded)/advertising/adAccounts/[adAccount]/campaignGroups/[campaignGroupId]/audience/types/audience";
import { api } from "@/trpc/client";
import { CheckCircle, Plus, X } from "lucide-react";

import { Alert, AlertDescription, AlertTitle } from "@kalos/ui/alert";
import { Badge } from "@kalos/ui/badge";
import { Button } from "@kalos/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@kalos/ui/dialog";
import { Input } from "@kalos/ui/input";
import { Label } from "@kalos/ui/label";
import { Separator } from "@kalos/ui/seperator";

// Error types for structured error handling
enum ErrorType {
  NO_RUNNING_AB_TEST = "NO_RUNNING_AB_TEST",
  AUDIENCE_NOT_FOUND = "AUDIENCE_NOT_FOUND",
  AUDIENCE_ALREADY_EXISTS = "AUDIENCE_ALREADY_EXISTS",
  VALIDATION_ERROR = "VALIDATION_ERROR",
  PERMISSION_ERROR = "PERMISSION_ERROR",
  QUOTA_LIMIT = "QUOTA_LIMIT",
  INVALID_STAGE = "INVALID_STAGE",
  MISSING_ROUND_ID = "MISSING_ROUND_ID",
  INTERNAL_ERROR = "INTERNAL_ERROR",
}

// Error message mappings
const ERROR_MESSAGES = {
  [ErrorType.NO_RUNNING_AB_TEST]: {
    title: "No Active AB Test",
    message:
      "Your audience was created but there's no active AB test to add it to. Please start an AB test first, then try adding your audience again.",
  },
  [ErrorType.AUDIENCE_NOT_FOUND]: {
    title: "Audience Created but Not Found",
    message:
      "The audience was created successfully, but we couldn't locate it to start the AB test. Please try refreshing the page and manually starting a test.",
  },
  [ErrorType.AUDIENCE_ALREADY_EXISTS]: {
    title: "Audience Already Being Tested",
    message:
      "This audience is already being tested in the current AB test. Please wait for the test to complete or create a different audience.",
  },
  [ErrorType.VALIDATION_ERROR]: {
    title: "Invalid Configuration",
    message:
      "Please check your targeting criteria and audience name, then try again.",
  },
  [ErrorType.PERMISSION_ERROR]: {
    title: "Permission Error",
    message:
      "You don't have permission to perform this action. Please contact your administrator.",
  },
  [ErrorType.QUOTA_LIMIT]: {
    title: "Limit Reached",
    message:
      "You've reached the maximum limit. Please delete unused items and try again.",
  },
  [ErrorType.INVALID_STAGE]: {
    title: "Test Configuration Issue",
    message:
      "There's an issue with the current AB test configuration. Please contact support.",
  },
  [ErrorType.MISSING_ROUND_ID]: {
    title: "Test Setup Error",
    message:
      "The AB test was created but we couldn't get the round ID. Please try refreshing and starting the test manually.",
  },
  [ErrorType.INTERNAL_ERROR]: {
    title: "Something Went Wrong",
    message:
      "An unexpected error occurred. Please try again or contact support if the issue persists.",
  },
};

// Helper function to categorize errors
function categorizeError(error: any): ErrorType {
  const message = error?.message?.toLowerCase() || "";

  if (
    message.includes("no running ab test") ||
    message.includes("no_running_test")
  ) {
    return ErrorType.NO_RUNNING_AB_TEST;
  }
  if (message.includes("audience_already_exists")) {
    return ErrorType.AUDIENCE_ALREADY_EXISTS;
  }
  if (message.includes("validation")) {
    return ErrorType.VALIDATION_ERROR;
  }
  if (message.includes("permission") || message.includes("unauthorized")) {
    return ErrorType.PERMISSION_ERROR;
  }
  if (message.includes("quota") || message.includes("limit")) {
    return ErrorType.QUOTA_LIMIT;
  }
  if (message.includes("invalid_stage")) {
    return ErrorType.INVALID_STAGE;
  }

  return ErrorType.INTERNAL_ERROR;
}

// Helper function to handle errors consistently
function handleError(
  error: any,
  fallbackType: ErrorType = ErrorType.INTERNAL_ERROR,
): { title: string; message: string } {
  const errorType = categorizeError(error);
  console.error(`Error [${errorType}]:`, error);
  return ERROR_MESSAGES[errorType] || ERROR_MESSAGES[fallbackType];
}

interface AddNewAudienceDialogProps {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  adSegmentId: string;
  onSuccess?: () => void;
  /**
   * Notify parent component when a long-running audience creation / deployment
   * process starts or stops so it can, for example, disable the "Add Audience"
   * entry point or surface global status UI.
   */
  onProcessingChange?: (isProcessing: boolean) => void;
}

export function AddNewAudienceDialog({
  isOpen,
  setIsOpen,
  adSegmentId,
  onSuccess,
  onProcessingChange,
}: AddNewAudienceDialogProps) {
  const [audienceName, setAudienceName] = useState("");
  const [targetingCriteria, setTargetingCriteria] =
    useState<AudienceTargetingCriteria | null>({
      include: {
        and: [],
      },
      exclude: {
        or: [],
      },
    });
  const [isTargetingDialogOpen, setIsTargetingDialogOpen] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [notification, setNotification] = useState<{
    type: "success" | "error" | null;
    title: string;
    message: string;
  }>({ type: null, title: "", message: "" });

  const apiUtils = api.useUtils();

  const showNotification = (
    type: "success" | "error",
    title: string,
    message: string,
  ) => {
    setNotification({ type, title, message });

    // Only auto-dismiss success messages, keep error messages until manually dismissed
    if (type === "success") {
      setTimeout(
        () => setNotification({ type: null, title: "", message: "" }),
        5000,
      );
    }
  };

  const dismissNotification = () => {
    setNotification({ type: null, title: "", message: "" });
  };

  // Whenever we flip the internal creating state, let the parent know too.
  const updateCreatingState = (creating: boolean) => {
    setIsCreating(creating);
    onProcessingChange?.(creating);
  };

  // Mutation to create new audience
  const createAudienceMutation = api.v2.ads.adAudience.create.useMutation({
    onSuccess: async () => {
      showNotification(
        "success",
        "Audience Created",
        "New audience created successfully. Finding audience to start AB test...",
      );

      // Refetch audiences to get the new one
      try {
        await apiUtils.v2.ads.adAudience.invalidate();
        const updatedAudiences =
          await apiUtils.v2.ads.adAudience.getAllForAdSegment.fetch({
            adSegmentId,
          });

        console.log("Updated audiences:", updatedAudiences);

        // Find the newest audience (assuming it's the last one or has the most recent creation)
        const newestAudience = updatedAudiences?.[updatedAudiences.length - 1];

        if (!newestAudience?.id) {
          const errorMessage = ERROR_MESSAGES[ErrorType.AUDIENCE_NOT_FOUND];
          showNotification("error", errorMessage.title, errorMessage.message);
          updateCreatingState(false);
          return;
        }

        // Get the current running stage for this ad segment
        const currentAbTestQuery =
          await apiUtils.v2.ads.abTest.getCurrentRunningAbTest.fetch({
            adSegmentId,
            adFormat: "SPONSORED_CONTENT",
          });

        console.log("Current AB test query result:", currentAbTestQuery);

        if (!currentAbTestQuery?.stageId) {
          console.error(
            "No running AB test found. Query result:",
            currentAbTestQuery,
          );
          const errorMessage = ERROR_MESSAGES[ErrorType.NO_RUNNING_AB_TEST];
          showNotification("error", errorMessage.title, errorMessage.message);
          updateCreatingState(false);
          return;
        }

        console.log("Using stage ID:", currentAbTestQuery.stageId);

        // Now trigger the AB test with the new audience
        await addAudienceToTestMutation.mutateAsync({
          stageId: currentAbTestQuery.stageId,
          abTestType: "audience" as const,
          newAudienceId: newestAudience.id,
        });
      } catch (error) {
        const errorMessage = handleError(error);
        showNotification("error", errorMessage.title, errorMessage.message);
        updateCreatingState(false);
      }
    },
    onError: (error: any) => {
      const errorMessage = handleError(error);
      showNotification("error", errorMessage.title, errorMessage.message);
      updateCreatingState(false);
    },
  });

  // Mutation to add audience to running test
  const addAudienceToTestMutation =
    api.v2.ads.abTest.addAudienceToRunningTest.useMutation({
      onSuccess: async (result) => {
        const isQueued = (result as any).wasQueued;

        if (isQueued) {
          showNotification(
            "success",
            "Audience Queued Successfully!",
            "New audience added to test queue. It will be tested when the current round completes.",
          );

          // Reset form and close dialog for queued audiences
          setAudienceName("");
          setTargetingCriteria({
            include: { and: [] },
            exclude: { or: [] },
          });
          updateCreatingState(false);
          setIsOpen(false);

          // Refresh data
          apiUtils.v2.ads.adAudience.invalidate();
          apiUtils.v2.ads.abTest.invalidate();

          if (onSuccess) {
            onSuccess();
          }
        } else {
          // Round was started immediately and already provisioned by the backend
          showNotification(
            "success",
            "Test Deployed Successfully!",
            "New audience is now live on LinkedIn and actively being tested.",
          );

          // Reset form and close dialog
          setAudienceName("");
          setTargetingCriteria({
            include: { and: [] },
            exclude: { or: [] },
          });
          updateCreatingState(false);
          setIsOpen(false);

          // Refresh data
          apiUtils.v2.ads.adAudience.invalidate();
          apiUtils.v2.ads.abTest.invalidate();

          if (onSuccess) {
            onSuccess();
          }
        }
      },
      onError: (error: any) => {
        const errorMessage = handleError(error);
        showNotification("error", errorMessage.title, errorMessage.message);
        updateCreatingState(false);
      },
    });

  const handleCreateAudience = async () => {
    if (!audienceName.trim()) {
      showNotification(
        "error",
        "Name Required",
        "Please enter a name for your audience.",
      );
      return;
    }

    if (!targetingCriteria || targetingCriteria.include.and.length === 0) {
      showNotification(
        "error",
        "Targeting Required",
        "Please add at least one targeting criteria.",
      );
      return;
    }

    updateCreatingState(true);

    try {
      // Only include exclude if it has criteria
      const audienceTargetCriteria = {
        include: targetingCriteria.include,
        ...(targetingCriteria.exclude?.or &&
          targetingCriteria.exclude.or.length > 0 && {
            exclude: targetingCriteria.exclude,
          }),
      };

      await createAudienceMutation.mutateAsync({
        adSegmentId,
        audienceTargetCriteria: audienceTargetCriteria,
      });
    } catch (error) {
      // Error handling is done in mutation onError
    }
  };

  const handleClose = () => {
    // Always allow the dialog to be closed so the user is not blocked on the
    // UI while the backend work continues. We only reset the local form state
    // once the async work is complete to avoid losing the context while the
    // request is still in-flight.
    setIsOpen(false);

    if (!isCreating) {
      setAudienceName("");
      setTargetingCriteria({
        include: { and: [] },
        exclude: { or: [] },
      });
      // Clear any notifications when closing the modal
      dismissNotification();
    }
  };

  const hasTargeting = targetingCriteria?.include.and.length
    ? targetingCriteria.include.and.length > 0
    : false;
  const audienceCount =
    targetingCriteria?.include.and.reduce((total, andGroup) => {
      return (
        total +
        andGroup.or.reduce((orTotal, orGroup) => {
          return orTotal + orGroup.facetEntites.length;
        }, 0)
      );
    }, 0) || 0;

  // Only render dialog when it should be open to prevent floating elements
  if (!isOpen) {
    return (
      <>
        {/* Audience Targeting Dialog - only render when main dialog is closed */}
        {isTargetingDialogOpen && (
          <AudienceTargetingDialog
            audienceId="new-audience" // Temporary ID for new audience
            isOpen={isTargetingDialogOpen}
            setIsOpen={setIsTargetingDialogOpen}
            onAddIncludeSubmit={(data) => {
              const currentTargetCriteria = structuredClone(targetingCriteria);
              handleAddCriteria(
                data,
                currentTargetCriteria,
                setTargetingCriteria,
              );
            }}
            onAddExcludeSubmit={(data) => {
              const currentTargetCriteria = structuredClone(targetingCriteria);
              handleAddExcludeCriteria(
                data,
                currentTargetCriteria,
                setTargetingCriteria,
              );
            }}
            disabled={isCreating}
          />
        )}
      </>
    );
  }

  return (
    <>
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Add New Audience for Testing</DialogTitle>
          </DialogHeader>

          <div className="space-y-6">
            {/* Notification Alert */}
            {notification.type && (
              <Alert
                variant={
                  notification.type === "error" ? "destructive" : "default"
                }
                className="relative"
              >
                <AlertTitle>{notification.title}</AlertTitle>
                <AlertDescription>{notification.message}</AlertDescription>
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute right-2 top-2 h-6 w-6 p-0 hover:bg-transparent"
                  onClick={dismissNotification}
                >
                  <X className="h-4 w-4" />
                  <span className="sr-only">Close notification</span>
                </Button>
              </Alert>
            )}

            <div className="space-y-2">
              <Label htmlFor="audience-name">Audience Name</Label>
              <Input
                id="audience-name"
                value={audienceName}
                onChange={(e) => setAudienceName(e.target.value)}
                placeholder="e.g., Tech Directors - Large Companies"
                disabled={isCreating}
              />
            </div>

            <Separator />

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium">Targeting Criteria</h3>
                  <p className="text-sm text-muted-foreground">
                    Define who should see your ads
                  </p>
                </div>
                <Button
                  onClick={() => setIsTargetingDialogOpen(true)}
                  variant="outline"
                  size="sm"
                  className="border-blue-200 bg-blue-50 font-normal text-primary hover:bg-blue-100"
                  disabled={isCreating}
                >
                  <Plus className="mr-1 h-4 w-4" />
                  Add criteria
                </Button>
              </div>

              {hasTargeting && targetingCriteria ? (
                <div className="space-y-3">
                  {/* Include Criteria */}
                  {targetingCriteria.include.and.map((andGroup, andIndex) => (
                    <div key={andIndex} className="rounded-lg border p-4">
                      <h4 className="mb-2 text-sm font-medium">Include</h4>
                      <div className="space-y-2">
                        {andGroup.or.map((orGroup, orIndex) => (
                          <div key={orIndex} className="space-y-1">
                            <p className="text-sm font-medium text-muted-foreground">
                              {orGroup.facetName}
                            </p>
                            <div className="flex flex-wrap gap-1">
                              {orGroup.facetEntites.map(
                                (entity, entityIndex) => (
                                  <Badge key={entityIndex} variant="secondary">
                                    {entity.entityName}
                                  </Badge>
                                ),
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}

                  {/* Exclude Criteria */}
                  {targetingCriteria.exclude &&
                    targetingCriteria.exclude.or.length > 0 && (
                      <div className="rounded-lg border border-red-200 p-4">
                        <h4 className="mb-2 text-sm font-medium text-red-700">
                          Exclude
                        </h4>
                        <div className="space-y-2">
                          {targetingCriteria.exclude.or.map(
                            (orGroup, orIndex) => (
                              <div key={orIndex} className="space-y-1">
                                <p className="text-sm font-medium text-muted-foreground">
                                  {orGroup.facetName}
                                </p>
                                <div className="flex flex-wrap gap-1">
                                  {orGroup.facetEntites.map(
                                    (entity, entityIndex) => (
                                      <Badge
                                        key={entityIndex}
                                        variant="destructive"
                                      >
                                        {entity.entityName}
                                      </Badge>
                                    ),
                                  )}
                                </div>
                              </div>
                            ),
                          )}
                        </div>
                      </div>
                    )}

                  <div className="text-sm text-muted-foreground">
                    {audienceCount} targeting criteria added
                  </div>
                </div>
              ) : (
                <div className="rounded-lg border-2 border-dashed border-gray-200 p-8 text-center">
                  <p className="text-sm text-muted-foreground">
                    No targeting criteria added yet. Click "Add criteria" to
                    define your audience.
                  </p>
                </div>
              )}
            </div>

            <Separator />

            <div className="rounded-lg bg-blue-50 p-4">
              <div className="flex items-start gap-3">
                <CheckCircle className="mt-0.5 h-5 w-5 text-blue-600" />
                <div className="space-y-1">
                  <p className="text-sm font-medium text-blue-900">
                    This will start a new AB test round and deploy to LinkedIn
                  </p>
                  <p className="text-sm text-blue-700">
                    Your new audience will be created, tested against the
                    current best performing audience, and automatically deployed
                    to LinkedIn. The test will run day-by-day and determine the
                    winner based on performance metrics.
                  </p>
                </div>
              </div>
            </div>

            <div className="flex justify-end gap-3">
              <Button variant="outline" onClick={handleClose}>
                Cancel
              </Button>
              <Button
                onClick={handleCreateAudience}
                disabled={isCreating || !hasTargeting || !audienceName.trim()}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {isCreating
                  ? "Creating & Deploying..."
                  : "Create Audience & Deploy Test"}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Audience Targeting Dialog - only render when main dialog is open */}
      {isTargetingDialogOpen && (
        <AudienceTargetingDialog
          audienceId="new-audience" // Temporary ID for new audience
          isOpen={isTargetingDialogOpen}
          setIsOpen={setIsTargetingDialogOpen}
          onAddIncludeSubmit={(data) => {
            const currentTargetCriteria = structuredClone(targetingCriteria);
            handleAddCriteria(
              data,
              currentTargetCriteria,
              setTargetingCriteria,
            );
          }}
          onAddExcludeSubmit={(data) => {
            const currentTargetCriteria = structuredClone(targetingCriteria);
            handleAddExcludeCriteria(
              data,
              currentTargetCriteria,
              setTargetingCriteria,
            );
          }}
          disabled={isCreating}
        />
      )}
    </>
  );
}
