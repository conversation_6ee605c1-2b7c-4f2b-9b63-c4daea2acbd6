import React from "react";
import { api } from "@/trpc/client";
import { Calendar } from "lucide-react";
import { z } from "zod";

import { Badge } from "@kalos/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@kalos/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@kalos/ui/table/table";

import LinkedInAdPreviewModal from "../_components/LinkedInAdPreviewModal";
import { AbTestType } from "../../../../../../../../../backend/src/modules/advertising/abTest/internal/domain/abTestType.valueObject";
import { AbTestDataDto } from "../../../../../../../../../backend/src/modules/advertising/abTest/internal/dtos/abTestData.dto";
import { AbTestRoundsDetailsModal } from "./abTestRoundsDetailsModal";

const metricsKeySchema = z.object({
  impressions: z.number().nullable(),
  clicks: z.number().nullable(),
  conversions: z.number().nullable(),
  leads: z.number().nullable(),
  videoViews: z.number().nullable(),
  sends: z.number().nullable(),
  opens: z.number().nullable(),
  cost: z.number().nullable(),
  actionClicks: z.number().nullable(),
  totalEngagements: z.number().nullable(),
  oneClickLeadFormOpens: z.number().nullable(),
  landingPageClicks: z.number().nullable(),
  videoCompletions: z.number().nullable(),
  videoFirstQuartileCompletions: z.number().nullable(),
  videoMidpointCompletions: z.number().nullable(),
  videoThirdQuartileCompletions: z.number().nullable(),
  videoStarts: z.number().nullable(),
  externalWebsiteConversions: z.number().nullable(),
});

type Metrics = z.infer<typeof metricsKeySchema>;

function getMetrics(
  data: Metrics,
  format: "SINGLE_IMAGE" | "DOCUMENT" | "VIDEO" | "SPONSORED_INMAIL",
  objectiveType: "BRAND_AWARENESS" | "LEAD_GENERATION",
): (number | null)[] {
  if (format === "SINGLE_IMAGE") {
    if (objectiveType === "BRAND_AWARENESS") {
      return singleImageAwarenessMetrics(data);
    } else {
      return singleImageAwarenessMetrics(data);
    }
  }
  return singleImageAwarenessMetrics(data);
}

function getHeaders(
  format: "SINGLE_IMAGE" | "DOCUMENT" | "VIDEO" | "SPONSORED_INMAIL",
  objectiveType: "BRAND_AWARENESS" | "LEAD_GENERATION",
) {
  if (format === "SINGLE_IMAGE") {
    if (objectiveType === "BRAND_AWARENESS") {
      return singleImageAwarenessHeaders;
    } else {
      return singleImageAwarenessHeaders;
    }
  }
  return [];
}

function getCpm(impressions: number | null, cost: number | null) {
  if (!impressions || !cost) return null;
  return (cost / impressions) * 1000;
}

function getCtr(clicks: number | null, impressions: number | null) {
  if (!clicks || !impressions || impressions === 0) return null;
  return (clicks / impressions) * 100;
}

function getCpc(clicks: number | null, cost: number | null) {
  if (!clicks || !cost) return null;
  return cost / clicks;
}

const singleImageAwarenessHeaders = [
  "Impressions",
  "Clicks",
  "CPM",
  "CTR",
  "CPC",
  "Total Engagements",
];
function singleImageAwarenessMetrics(data: Metrics): (number | null)[] {
  const cpm = getCpm(data.impressions, data.cost);
  const ctr = getCtr(data.clicks, data.impressions);
  const cpc = getCpc(data.clicks, data.cost);
  return [data.impressions, data.clicks, cpm, ctr, cpc, data.totalEngagements];
}

// Component for individual upcoming ad variant
function UpcomingAdVariantRow({
  variant,
  adFormat,
  objectiveType,
  abTestType,
  adSegmentId,
}: {
  variant: {
    variantId: string;
    name: string;
  };
  adFormat: "SINGLE_IMAGE" | "DOCUMENT" | "VIDEO" | "SPONSORED_INMAIL";
  objectiveType: "BRAND_AWARENESS" | "LEAD_GENERATION";
  abTestType: AbTestType;
  adSegmentId: string;
}) {
  // State for the LinkedIn Ad Preview Modal
  const [previewModal, setPreviewModal] = React.useState<{
    isOpen: boolean;
    adCreativeUrn: string;
    adTitle: string;
    adData: {
      title: string;
      description: string;
      imageUrl?: string;
      destinationUrl: string;
      companyName: string;
    };
  }>({
    isOpen: false,
    adCreativeUrn: "",
    adTitle: "",
    adData: {
      title: "",
      description: "",
      destinationUrl: "",
      companyName: "",
    },
  });

  const handleAdClick = (variantId: string, adName: string) => {
    setPreviewModal({
      isOpen: true,
      adCreativeUrn: variantId,
      adTitle: adName,
      adData: {
        title: adName,
        description:
          "This ad is scheduled to be created. Preview will be available once the ad goes live.",
        destinationUrl: "getkalos.com",
        companyName: "Your Company",
      },
    });
  };

  const closePreviewModal = () => {
    setPreviewModal({
      isOpen: false,
      adCreativeUrn: "",
      adTitle: "",
      adData: {
        title: "",
        description: "",
        destinationUrl: "",
        companyName: "",
      },
    });
  };

  return (
    <>
      <TableRow className="hover:bg-gray-50">
        <TableCell className="font-medium">
          <div className="flex items-center space-x-2">
            <button
              className="cursor-pointer text-blue-600 hover:text-blue-800 hover:underline"
              onClick={() => handleAdClick(variant.variantId, variant.name)}
            >
              {variant.name}
            </button>
          </div>
        </TableCell>
      </TableRow>

      <LinkedInAdPreviewModal
        isOpen={previewModal.isOpen}
        onClose={closePreviewModal}
        adCreativeUrn={previewModal.adCreativeUrn}
        adTitle={previewModal.adTitle}
        adFormat={adFormat}
        adData={previewModal.adData}
        segmentId={adSegmentId}
      />
    </>
  );
}

export function UpcomingAbTests({
  abTestId,
  adFormat,
  objectiveType,
  abTestType,
  adSegmentId,
}: {
  abTestId: string;
  abTestType: AbTestType;
  adSegmentId: string;
  adFormat: "SINGLE_IMAGE" | "DOCUMENT" | "VIDEO" | "SPONSORED_INMAIL";
  objectiveType: "BRAND_AWARENESS" | "LEAD_GENERATION";
}) {
  const presumedVariantsQuery =
    api.v2.ads.newAbTestController.getPresumedVariantsForUpcomingAbTest.useQuery(
      {
        abTestId: abTestId,
        type: abTestType,
      },
    );

  function camelCaseToWords(camelCase: string): string {
    // Add a space before each uppercase letter that follows a lowercase letter
    const withSpaces = camelCase.replace(/([a-z])([A-Z])/g, "$1 $2");

    // Capitalize the first letter of the string
    return withSpaces.charAt(0).toUpperCase() + withSpaces.slice(1);
  }

  if (presumedVariantsQuery.isLoading) {
    return <div>Loading upcoming AB test data...</div>;
  }

  if (presumedVariantsQuery.isError) {
    return <div>Error loading upcoming AB test data</div>;
  }

  if (!presumedVariantsQuery.data || presumedVariantsQuery.data.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">
            Upcoming Test: {camelCaseToWords(abTestType)}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="py-8 text-center text-gray-500">
            {abTestType == "valueProp"
              ? " Creative test must finished before we can determine which value props to test."
              : "No upcoming variants found for this test."}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg">
              Learning Goal: {camelCaseToWords(abTestType)} Test
            </CardTitle>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[200px]">Ad Title</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {presumedVariantsQuery.data.map((variant) => (
                <UpcomingAdVariantRow
                  key={variant.variantId}
                  variant={variant}
                  adFormat={adFormat}
                  objectiveType={objectiveType}
                  abTestType={abTestType}
                  adSegmentId={adSegmentId}
                />
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
}
