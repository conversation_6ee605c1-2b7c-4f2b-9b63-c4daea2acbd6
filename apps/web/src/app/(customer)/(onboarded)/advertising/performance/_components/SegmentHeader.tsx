import { useEffect, useMemo, useRef, useState } from "react";
import { AppRouterInstance } from "next/dist/shared/lib/app-router-context.shared-runtime";
import { Audience } from "@/app/(customer)/(onboarded)/advertising/adAccounts/[adAccount]/campaignGroups/[campaignGroupId]/audience/_components/Audience";
import { api } from "@/trpc/client";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@radix-ui/react-dropdown-menu";
import {
  ArrowLeft,
  ChevronDown,
  DollarSign,
  Edit,
  Info,
  LoaderCircleIcon,
  Pause,
  Play,
  Plus,
} from "lucide-react";

import { Badge } from "@kalos/ui/badge";
import { Button } from "@kalos/ui/button";
import { Checkbox } from "@kalos/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@kalos/ui/dialog";
import { Skeleton } from "@kalos/ui/skeleton";

import { SegmentDetail } from "../_types";
import { AddNewAudienceDialog } from "./AddNewAudienceDialog";
import EditBudgetModal from "./EditBudgetModal";

interface SegmentHeaderProps {
  segmentDetail: SegmentDetail;
  router: AppRouterInstance;
  adProgramId?: string;
}

/**
 * Segment Header Component
 * Displays segment title, status, and metrics
 */
export default function SegmentHeader({
  segmentDetail,
  router,
}: SegmentHeaderProps) {
  const [isEditAudienceOpen, setIsEditAudienceOpen] = useState(false);
  const [isEditBudgetOpen, setIsEditBudgetOpen] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isAddNewAudienceOpen, setIsAddNewAudienceOpen] = useState(false);
  const audienceProcessingKey = `audience-adding-${segmentDetail.adSegmentId}`;

  // Utils and audience data
  const apiUtils = api.useUtils();

  // Prefetch all audiences for the segment (declared early so hooks below can use it)
  const audiencesQuery = api.v2.ads.adAudience.getAllForAdSegment.useQuery({
    adSegmentId: segmentDetail.adSegmentId,
  });

  // We remember how many audiences we had when the add-audience flow started so
  // we know when a new one has arrived.
  const initialAudienceCountRef = useRef<number | null>(null);

  // Initialise from localStorage so the state survives a page refresh.
  const [isAddingAudience, setIsAddingAudience] = useState<boolean>(() => {
    if (typeof window === "undefined") return false;
    return localStorage.getItem(audienceProcessingKey) === "true";
  });

  // Keep isAddingAudience in-sync across browser tabs / windows.
  useEffect(() => {
    const handler = (e: StorageEvent) => {
      if (e.key === audienceProcessingKey) {
        setIsAddingAudience(e.newValue === "true");
      }
    };
    window.addEventListener("storage", handler);
    return () => window.removeEventListener("storage", handler);
  }, [audienceProcessingKey]);

  // When we enter the "adding" state record the audience count so we can tell
  // when something has changed. Also start a lightweight polling loop that
  // refetches the audiences every 5 s. When we leave the state, clean up.
  useEffect(() => {
    if (!isAddingAudience) return;

    // Save baseline count once.
    if (initialAudienceCountRef.current == null && audiencesQuery.data) {
      initialAudienceCountRef.current = audiencesQuery.data.length;
    }

    const intervalId = setInterval(() => {
      audiencesQuery.refetch();
    }, 5000);

    // Safety timeout: clear the flag after 2 minutes even if nothing changed.
    const timeoutId = setTimeout(() => {
      setIsAddingAudience(false);
      if (typeof window !== "undefined") {
        localStorage.removeItem(audienceProcessingKey);
      }
    }, 120_000);

    return () => {
      clearInterval(intervalId);
      clearTimeout(timeoutId);
    };
  }, [isAddingAudience, audiencesQuery, audienceProcessingKey]);

  // Detect when a new audience arrives compared with baseline count.
  useEffect(() => {
    if (!isAddingAudience) return;
    if (
      initialAudienceCountRef.current != null &&
      audiencesQuery.data &&
      audiencesQuery.data.length > initialAudienceCountRef.current
    ) {
      setIsAddingAudience(false);
      initialAudienceCountRef.current = null;
      if (typeof window !== "undefined") {
        localStorage.removeItem(audienceProcessingKey);
      }
    }
  }, [audiencesQuery.data, isAddingAudience, audienceProcessingKey]);

  // Refetch audiences when modal opens to ensure fresh data
  useEffect(() => {
    if (isEditAudienceOpen) {
      audiencesQuery.refetch();
    }
  }, [isEditAudienceOpen, audiencesQuery]);

  // Check for paused audience stage
  const stageStatusQuery = api.v2.ads.stage.getStageStatus.useQuery(
    { adSegmentId: segmentDetail.adSegmentId },
    {
      enabled: !!segmentDetail.adSegmentId,
      refetchInterval: 5000,
    },
  );

  const isPausedForUserInput = stageStatusQuery.data?.isPaused;
  console.log("isPausedForUserInput", isPausedForUserInput);

  const resumeStageMutation = api.v2.ads.stage.resumeStage.useMutation({
    onSuccess: () => {
      stageStatusQuery.refetch();
    },
  });

  // Allow adding audiences only when we're in an active audience test stage and
  // no audience is currently being processed. This prevents users from
  // triggering the flow again while the previous audience is still being
  // created/deployed.
  const canAddAudience =
    !isAddingAudience &&
    stageStatusQuery.data?.stageType === "audienceTest" &&
    ["RUNNING", "PAUSED"].includes(stageStatusQuery.data?.status ?? "");
  const [isClient, setIsClient] = useState(false);

  // Fix hydration by ensuring client-side rendering for date-dependent content
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Memoize date calculations to prevent hydration mismatches
  const monthDateRange = useMemo(() => {
    const now = new Date();
    return {
      fromDate: new Date(now.getFullYear(), now.getMonth(), 1, 0, 0, 0, 0),
      toDate: new Date(now.getFullYear(), now.getMonth() + 1, 1, 0, 0, 0, 0),
    };
  }, []);

  const handleAudienceClick = () => {
    setIsEditAudienceOpen(true);
    setIsDropdownOpen(false);
  };

  const handleBudgetClick = () => {
    console.log("[DEBUG] Budget button clicked");
    setIsEditBudgetOpen(true);
    setIsDropdownOpen(false);
  };

  const handleDialogClose = (open: boolean) => {
    setIsEditAudienceOpen(open);
    setIsDropdownOpen(false);
  };

  const getCampaignGroupMonthAnalytics =
    api.v2.ads.linkedInCampaignGroup.getAnalyticsForOneCampaignGroup.useQuery(
      {
        linkedInAdSegment: segmentDetail.adSegmentId,
        fromDate: monthDateRange.fromDate,
        toDate: monthDateRange.toDate,
      },
      {
        enabled:
          !!segmentDetail.adSegmentId && !segmentDetail.endDate && isClient,
      },
    );

  // Prefetch facets that will be needed for the dialog - only when audience modal is open
  const facetsQuery =
    api.v2.ads.linkedInApi.adTargeting.getAdTargetingFacets.useQuery(
      undefined,
      {
        enabled: isEditAudienceOpen, // Only fetch when audience modal is open
      },
    );

  // If we have audiences, prefetch their details and counts
  useEffect(() => {
    if (audiencesQuery.data) {
      audiencesQuery.data.forEach((audience) => {
        // Prefetch audience details
        apiUtils.v2.ads.adAudience.getOne.prefetch({ id: audience.id });
        // Skip audience count prefetch due to missing targetingCriteria
      });
    }
  }, [audiencesQuery.data, apiUtils]);
  // Format currency with proper client-side rendering
  const formatCurrency = (amount: number) => {
    if (!isClient) return `$${amount.toFixed(2)}`;
    return amount.toLocaleString(undefined, {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
  };

  // Safe format for numbers that might be objects
  const safeFormatCurrency = (value: number | any) => {
    const numValue = typeof value === "number" ? value : 0;
    return formatCurrency(numValue);
  };

  return (
    <>
      <div className="mb-6 flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <div className="flex items-start gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.back()}
              className="mr-2"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>

            <div className="flex flex-col">
              <div className="flex items-center gap-2">
                <h1 className="text-2xl font-bold tracking-tight">
                  {segmentDetail.name}
                </h1>
                <Badge variant="outline" className="bg-blue-50 text-blue-700">
                  {segmentDetail.status}
                </Badge>
                {isPausedForUserInput && (
                  <Badge
                    variant="outline"
                    className="border-orange-200 bg-orange-50 text-orange-700"
                  >
                    <Pause className="mr-1 h-3 w-3" />
                    Audience Test Complete
                  </Badge>
                )}
              </div>

              <p className="text-sm text-muted-foreground">
                {isClient ? (
                  <>
                    {new Date(segmentDetail.startDate).toLocaleDateString()} to{" "}
                    {segmentDetail.endDate
                      ? new Date(segmentDetail.endDate).toLocaleDateString()
                      : "Evergreen"}
                  </>
                ) : (
                  "Loading dates..."
                )}
              </p>
            </div>
          </div>
        </div>
        <div className="flex items-center gap-4">
          <div className="space-y-0.5 text-right">
            <div>
              <span className="text-sm text-muted-foreground">Total:</span>{" "}
              <span className="text-base font-semibold">
                {safeFormatCurrency(segmentDetail.metrics.totalSpent)}
              </span>
            </div>
            {!segmentDetail.endDate && (
              <div>
                <span className="text-sm text-muted-foreground">
                  This Month:
                </span>{" "}
                <span className="text-base font-semibold">
                  {getCampaignGroupMonthAnalytics.isLoading ? (
                    <LoaderCircleIcon className="mb-1 inline-block h-4 w-4 animate-spin align-middle" />
                  ) : getCampaignGroupMonthAnalytics.isError ? (
                    <span className="text-red-500">Error</span>
                  ) : (
                    safeFormatCurrency(
                      getCampaignGroupMonthAnalytics.data?.costInUsd,
                    )
                  )}
                </span>
              </div>
            )}

            <div>
              <span className="text-sm text-muted-foreground">Budget:</span>{" "}
              <span className="text-base font-semibold">
                {safeFormatCurrency(segmentDetail.budget)}
              </span>
            </div>
          </div>

          <DropdownMenu open={isDropdownOpen} onOpenChange={setIsDropdownOpen}>
            <DropdownMenuTrigger asChild>
              <Button
                className="flex items-center gap-2"
                disabled={isPausedForUserInput}
              >
                <Edit className="h-4 w-4" />
                Edit
                <ChevronDown className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              align="end"
              className="w-[200px] space-y-1 rounded-md border bg-white p-2 shadow-md"
            >
              <DropdownMenuItem className="flex cursor-pointer items-center rounded-sm px-3 py-2 text-sm hover:bg-gray-100 focus:bg-gray-100 focus:outline-none">
                <div
                  role="button"
                  onClick={handleAudienceClick}
                  className="flex w-full cursor-pointer items-center"
                >
                  <Edit className="mr-2 h-4 w-4" />
                  Audience
                </div>
              </DropdownMenuItem>
              <DropdownMenuItem className="flex cursor-pointer items-center rounded-sm px-3 py-2 text-sm hover:bg-gray-100 focus:bg-gray-100 focus:outline-none">
                <div
                  role="button"
                  onClick={handleBudgetClick}
                  className="flex w-full cursor-pointer items-center"
                >
                  <DollarSign className="mr-2 h-4 w-4" />
                  Edit Budget
                </div>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Action row shown only when the stage is paused */}
      {isPausedForUserInput && stageStatusQuery.data?.stageId && (
        <div className="mb-4 flex items-center justify-end gap-2">
          <Button
            onClick={() =>
              resumeStageMutation.mutate({
                stageId: stageStatusQuery.data!.stageId!,
                action: "RESUME_CURRENT_STAGE",
              })
            }
            variant="outline"
            className="flex items-center gap-2 border-orange-200 bg-orange-50 text-orange-700 hover:bg-orange-100"
            disabled={resumeStageMutation.isPending}
          >
            <Play className="h-4 w-4" />
            Add More Audiences
          </Button>

          <Button
            onClick={() =>
              resumeStageMutation.mutate({
                stageId: stageStatusQuery.data!.stageId!,
                action: "CONTINUE_TO_NEXT_STAGE",
              })
            }
            className="flex items-center gap-2 bg-orange-500 hover:bg-orange-600"
            disabled={resumeStageMutation.isPending}
          >
            <Play className="h-4 w-4" />
            {resumeStageMutation.isPending
              ? "Processing..."
              : "Move to Next Stage"}
          </Button>
        </div>
      )}

      <Dialog open={isEditAudienceOpen} onOpenChange={handleDialogClose}>
        <DialogContent className="min-w-[900px]">
          <DialogHeader>
            <DialogTitle>Edit Audience</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <p className="flex items-center gap-2">
                <Info></Info> Changes will apply to this campaign and will be
                default settings for future campaigns
              </p>
            </div>

            {/* Add New Audience Button */}
            <div className="flex items-center justify-between border-b pb-4">
              <div>
                <h3 className="text-lg font-medium">Current Audiences</h3>
                <p className="text-sm text-muted-foreground">
                  Edit existing audiences or add a new one. New audiences will
                  be tested immediately or queued for the next round.
                </p>
              </div>
              <Button
                onClick={() => canAddAudience && setIsAddNewAudienceOpen(true)}
                variant="outline"
                className="flex items-center gap-2 border-blue-200 bg-blue-50 text-blue-700 hover:bg-blue-100 disabled:cursor-not-allowed"
                disabled={isAddingAudience || !canAddAudience}
              >
                {isAddingAudience ? (
                  <>
                    <LoaderCircleIcon className="h-4 w-4 animate-spin" />
                    Adding Audience...
                  </>
                ) : (
                  <>
                    <Plus className="h-4 w-4" />
                    Add New Audience
                  </>
                )}
              </Button>
            </div>

            {isEditAudienceOpen && (
              <div className="max-h-[600px] overflow-y-auto">
                <Audience
                  adSegmentId={segmentDetail.adSegmentId}
                  adAccount={segmentDetail.adProgramId.split("/")[1] || ""}
                  adProgramId={segmentDetail.adProgramId}
                  prefetchedAudiences={undefined}
                  prefetchedFacets={facetsQuery.data}
                />
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Add New Audience Dialog */}
      <AddNewAudienceDialog
        isOpen={isAddNewAudienceOpen}
        setIsOpen={setIsAddNewAudienceOpen}
        adSegmentId={segmentDetail.adSegmentId}
        onSuccess={() => {
          setIsAddNewAudienceOpen(false);
          setIsAddingAudience(false);
          // Clear persistence – audience has finished adding.
          if (typeof window !== "undefined") {
            localStorage.removeItem(audienceProcessingKey);
          }
          // Refresh audiences list
          audiencesQuery.refetch();
        }}
        onProcessingChange={(processing) => {
          setIsAddingAudience(processing);
          // Persist flag so it survives reloads.
          if (typeof window !== "undefined") {
            if (processing) {
              localStorage.setItem(audienceProcessingKey, "true");
              initialAudienceCountRef.current =
                audiencesQuery.data?.length ?? null;
            } else {
              localStorage.removeItem(audienceProcessingKey);
              initialAudienceCountRef.current = null;
            }
          }
          // Close the edit audience modal when processing starts so the user
          // is returned to the segment page immediately.
          if (processing) {
            setIsEditAudienceOpen(false);
            setIsAddNewAudienceOpen(false);
          }
        }}
      />
      {/* Edit Budget Modal - Only render when client-side and data is available */}
      {isClient && segmentDetail.adProgramId && (
        <EditBudgetModal
          segmentDetail={segmentDetail}
          isOpen={isEditBudgetOpen}
          setIsOpen={setIsEditBudgetOpen}
        />
      )}
    </>
  );
}
