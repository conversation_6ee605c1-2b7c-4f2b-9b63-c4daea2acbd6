import { z } from "zod";

const metricsKeySchema = z.object({
  impressions: z.number().nullable(),
  clicks: z.number().nullable(),
  conversions: z.number().nullable(),
  leads: z.number().nullable(),
  videoViews: z.number().nullable(),
  sends: z.number().nullable(),
  opens: z.number().nullable(),
  cost: z.number().nullable(),
  actionClicks: z.number().nullable(),
  totalEngagements: z.number().nullable(),
  oneClickLeadFormOpens: z.number().nullable(),
  landingPageClicks: z.number().nullable(),
  videoCompletions: z.number().nullable(),
  videoFirstQuartileCompletions: z.number().nullable(),
  videoMidpointCompletions: z.number().nullable(),
  videoThirdQuartileCompletions: z.number().nullable(),
  videoStarts: z.number().nullable(),
  externalWebsiteConversions: z.number().nullable(),
});

type Metrics = z.infer<typeof metricsKeySchema>;
export function getMetrics(
  data: Metrics,
  format: "SINGLE_IMAGE" | "DOCUMENT" | "VIDEO" | "SPONSORED_INMAIL",
  objectiveType: "BRAND_AWARENESS" | "LEAD_GENERATION" | "VIDEO_VIEW",
): (number | null | string)[] {
  if (format === "SINGLE_IMAGE") {
    if (objectiveType === "BRAND_AWARENESS") {
      return singleImageAwarenessMetrics(data);
    } else {
      return singleImageLeadgenMetrics(data);
    }
  }
  if (format === "SPONSORED_INMAIL") {
    if (objectiveType === "LEAD_GENERATION") {
      return conversationLeadgenMetrics(data);
    }
    if (objectiveType === "BRAND_AWARENESS") {
      return conversationAwarenessMetrics(data);
    }
  }
  if (format === "VIDEO") {
    if (objectiveType === "BRAND_AWARENESS") {
      return videoAwarenessMetrics(data);
    }
    if (objectiveType === "VIDEO_VIEW") {
      return videoViewsMetrics(data);
    }
  }
  if (format === "DOCUMENT") {
    if (objectiveType === "LEAD_GENERATION") {
      return documentLeadgenMetrics(data);
    }
  }
  return singleImageAwarenessMetrics(data);
}

export function getHeaders(
  format: "SINGLE_IMAGE" | "DOCUMENT" | "VIDEO" | "SPONSORED_INMAIL",
  objectiveType: "BRAND_AWARENESS" | "LEAD_GENERATION" | "VIDEO_VIEW",
) {
  if (format === "SINGLE_IMAGE") {
    if (objectiveType === "BRAND_AWARENESS") {
      return singleImageAwarenessHeaders;
    } else {
      return singleImageLeadgenHeaders;
    }
  }
  if (format === "SPONSORED_INMAIL") {
    if (objectiveType === "LEAD_GENERATION") {
      return conversationLeadgenHeaders;
    }
    if (objectiveType === "BRAND_AWARENESS") {
      return conversationAwarenessHeaders;
    }
  }
  if (format === "VIDEO") {
    if (objectiveType === "BRAND_AWARENESS") {
      return videoAwarenessHeaders;
    }
    if (objectiveType === "VIDEO_VIEW") {
      return videoViewsHeaders;
    }
  }
  if (format === "DOCUMENT") {
    if (objectiveType === "LEAD_GENERATION") {
      return documentLeadgenHeaders;
    }
  }
  return [];
}

function getCpm(impressions: number | null, cost: number | null) {
  if (!impressions || !cost) return null;
  return (cost / impressions) * 1000;
}

function getCtr(clicks: number | null, impressions: number | null) {
  if (!clicks || !impressions || impressions === 0) return null;
  return ((clicks / impressions) * 100).toFixed(2);
}

function getCpc(clicks: number | null, cost: number | null) {
  if (!clicks || !cost) return null;
  return cost / clicks;
}

const singleImageAwarenessHeaders = [
  "Spend",
  "Impressions",
  "Clicks",
  "CTR",
  "CPC",
  "Total Engagements",
];
function singleImageAwarenessMetrics(
  data: Metrics,
): (number | null | string)[] {
  const ctr = getCtr(data.clicks, data.impressions);
  const cpc = getCpc(data.clicks, data.cost);
  return [
    `$${data.cost?.toFixed(2)}`,
    data.impressions,
    data.clicks,
    ctr,
    cpc,
    data.totalEngagements,
  ];
}

const singleImageLeadgenHeaders = [
  "Spend",
  "Impressions",
  "Clicks",
  "CTR",
  "CPC",
  "Total Engagements",
  "Lead Form Opens",
  "Lead form completion rate",
  "Leads",
];

function singleImageLeadgenMetrics(data: Metrics): (number | null | string)[] {
  const ctr = getCtr(data.clicks, data.impressions);
  const cpc = getCpc(data.clicks, data.cost);
  const leadFormCompletionRate =
    data.oneClickLeadFormOpens && data.leads
      ? data.leads / data.oneClickLeadFormOpens
      : null;
  return [
    `$${data.cost?.toFixed(2)}`,
    data.impressions,
    data.clicks,
    ctr,
    cpc,
    data.totalEngagements,
    data.oneClickLeadFormOpens,
    leadFormCompletionRate,
    data.leads,
  ];
}

const conversationLeadgenHeaders = [
  "Spend",
  "Sends",
  "Opens",
  "Open Rate",
  "Button Clicks",
  "Click to Open Rate",
  "Lead Form Opens",
  "Lead Form Completion Rate",
  "Leads",
];

function conversationLeadgenMetrics(data: Metrics): (number | null | string)[] {
  const openRate = data.opens && data.sends ? data.opens / data.sends : null;
  const leadFormCompletionRate =
    data.oneClickLeadFormOpens && data.leads
      ? data.leads / data.oneClickLeadFormOpens
      : null;
  const clickToOpenRate =
    data.opens && data.actionClicks ? data.actionClicks / data.opens : null;
  return [
    `$${data.cost?.toFixed(2)}`,
    data.sends,
    data.opens,
    openRate,
    data.actionClicks,
    clickToOpenRate,
    data.oneClickLeadFormOpens,
    leadFormCompletionRate,
    data.leads,
  ];
}

const conversationAwarenessHeaders = [
  "Sends",
  "Opens",
  "Open Rate",
  "Button Clicks",
  "Click to Open Rate",
];

function conversationAwarenessMetrics(
  data: Metrics,
): (number | null | string)[] {
  const openRate = data.opens && data.sends ? data.opens / data.sends : null;
  const clickToOpenRate =
    data.opens && data.actionClicks ? data.actionClicks / data.opens : null;
  return [data.sends, data.opens, openRate, data.actionClicks, clickToOpenRate];
}

const videoAwarenessHeaders = [
  "Spend",
  "Impressions",
  "Videos @ 25%",
  "Videos @ 50%",
  "Videos @ 75%",
  "Video Completion Rate",
  "Total Engagements",
];

function videoAwarenessMetrics(data: Metrics): (number | null | string)[] {
  const cpm = getCpm(data.impressions, data.cost);
  const videoCompletionRate =
    data.impressions && data.videoCompletions
      ? data.videoCompletions / data.impressions
      : null;
  return [
    `$${data.cost?.toFixed(2)}`,
    data.impressions,
    data.videoFirstQuartileCompletions,
    data.videoMidpointCompletions,
    data.videoThirdQuartileCompletions,
    videoCompletionRate,
    data.totalEngagements,
  ];
}

const documentLeadgenHeaders = [
  "Spend",
  "Impressions",
  "Clicks",
  "CTR",
  "Lead Form Opens",
  "Lead Form Completion Rate",
  "Leads",
];

function documentLeadgenMetrics(data: Metrics): (number | null | string)[] {
  const cpm = getCpm(data.impressions, data.cost);
  const ctr = getCtr(data.clicks, data.impressions);
  const leadFormCompletionRate =
    data.oneClickLeadFormOpens && data.leads
      ? data.leads / data.oneClickLeadFormOpens
      : null;
  return [
    `$${data.cost?.toFixed(2)}`,
    data.impressions,
    data.clicks,
    ctr,
    data.oneClickLeadFormOpens,
    leadFormCompletionRate,
    data.leads,
  ];
}

const videoViewsHeaders = [
  "Spend",
  "Impressions",
  "Views @ 25%",
  "Views @ 50%",
  "Views @ 75%",
  "Completion rate",
  "Engagements",
  "Cost per Engagement",
  "Engagement rate",
];

function videoViewsMetrics(data: Metrics): (number | null | string)[] {
  const videoCompletionRate =
    data.impressions && data.videoCompletions
      ? (() => {
        const value = (data.videoCompletions / data.impressions) * 100;
        return value > 0 ? `${value.toFixed(2)}%` : 0;
      })()
      : null;
  const costPerEngagement =
    data.totalEngagements && data.cost
      ? `$${(data.cost / data.totalEngagements).toFixed(2)}`
      : null;
  const engagementRate =
    data.impressions && data.totalEngagements
      ? (() => {
        const value = (data.totalEngagements / data.impressions) * 100;
        return value > 0 ? `${value.toFixed(2)}%` : 0;
      })()
      : null;

  return [
    `$${data.cost?.toFixed(2)}`,
    data.impressions,
    data.videoFirstQuartileCompletions,
    data.videoMidpointCompletions,
    data.videoThirdQuartileCompletions,
    videoCompletionRate,
    data.totalEngagements,
    costPerEngagement,
    engagementRate,
  ];
}
