"use client";

import type {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
} from "@tanstack/react-table";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useCampaignCreationStore } from "@/stores/campaignCreationStore";
import { api } from "@/trpc/client";
import {
  ReloadIcon,
  TrashIcon,
  UploadIcon,
} from "@radix-ui/react-icons";
import {
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { format, set } from "date-fns";
import {
  ArrowLeft,
  ArrowRight,
  ChevronDownIcon,
  FilesIcon,
  Upload,
} from "lucide-react";
import { NavigationFooter } from "@/components/ui/navigation-footer";

import { Button } from "@kalos/ui/button";
import { Checkbox } from "@kalos/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  Di<PERSON>Title,
  DialogTrigger,
} from "@kalos/ui/dialog";
import { Input } from "@kalos/ui/input";
import { Popover, PopoverContent, PopoverTrigger } from "@kalos/ui/popover";
import { DataTable } from "@kalos/ui/table/datatable";

export function AdCreativeTable({
  adAccountId,
  campaignGroupId,
}: {
  adAccountId: string;
  campaignGroupId: string;
}) {
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const adCreative =
    api.v2.ads.adCreative.getAdCreativesForOrganization.useQuery();
  const router = useRouter();

  const nowDate = new Date(Date.now());
  nowDate.setMonth(nowDate.getMonth() - 12);
  const [dateRange, setDateRange] = useState<{
    startDate?: Date;
    endDate?: Date;
  }>({ startDate: nowDate, endDate: undefined });
  // Use Zustand store for assets state

  const campaignGroupAdCreativesQuery =
    api.v2.ads.linkedInAdProgramCreative.getForAdProgram.useQuery({
      linkedInAdProgramId: campaignGroupId,
    });

  const [isSubmitting, setIsSubmitting] = useState(false);

  const deleteAssetMutation =
    api.v2.ads.adCreative.deleteAdCreative.useMutation({
      onSuccess: async () => {
        await apiUtils.v2.ads.invalidate();
      },
    });

  const apiUtils = api.useUtils();

  function onSubmit() {
    if (
      !campaignGroupAdCreativesQuery.data ||
      campaignGroupAdCreativesQuery.data.length === 0
    ) {
      return;
    }
    setIsSubmitting(true);
    router.push(
      `/advertising/adAccounts/${adAccountId}/campaignGroups/${campaignGroupId}/preview`,
    );
  }

  const columns: ColumnDef<{
    organizationId: number;
    id: string;
    fileName: string;
    s3BucketKey: string;
    beingUsed: boolean;
    fileType: "VIDEO" | "DOCUMENT" | "IMAGE";
  }>[] = [
      {
        accessorKey: "fileName",
        header: () => {
          return <span className="pl-8">File Name</span>;
        },
        cell: ({ row }) => {
          const metadata = api.v2.ads.adCreative.getAdCreative.useQuery({
            id: row.original.id,
          });
          const adProgramCreative = campaignGroupAdCreativesQuery.data?.find(
            (c) => c.adCreativeId === row.original.id,
          );

          const createAdProgramCreativeMutation =
            api.v2.ads.linkedInAdProgramCreative.createOne.useMutation({
              onSuccess: async () => {
                await apiUtils.v2.ads.invalidate();
                setIsLoading(false);
              },
            });

          const deleteAdProgramCreativeMutation =
            api.v2.ads.linkedInAdProgramCreative.deleteForAdProgram.useMutation({
              onSuccess: async () => {
                await apiUtils.v2.ads.invalidate();
                setIsLoading(false);
              },
            });
          const [isLoading, setIsLoading] = useState(false);
          return (
            <div className="flex items-center justify-start space-x-4">
              {campaignGroupAdCreativesQuery.data && (
                <>
                  {!isLoading ? (
                    <Checkbox
                      disabled={isSubmitting}
                      checked={adProgramCreative !== undefined ? true : false}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setIsLoading(true);
                          createAdProgramCreativeMutation.mutate({
                            adCreativeId: row.original.id,
                            adProgramId: campaignGroupId,
                          });
                        } else {
                          setIsLoading(true);
                          deleteAdProgramCreativeMutation.mutate({
                            adProgramCreativeId: adProgramCreative!.id,
                          });
                        }
                      }}
                    />
                  ) : (
                    <ReloadIcon className="h-4 w-4 animate-spin" />
                  )}
                </>
              )}
              <Dialog>
                <DialogTrigger asChild>
                  <Button variant="ghost" className="h-16 w-16 p-0">
                    {row.original.fileType == "IMAGE" && (
                      <img
                        width="64"
                        height="64"
                        src={metadata.data?.presignedUrl}
                      />
                    )}
                    {row.original.fileType == "DOCUMENT" && <FilesIcon />}
                    {row.original.fileType == "VIDEO" && (
                      <video
                        width="64"
                        height="64"
                        src={metadata.data?.presignedUrl}
                        preload="metadata"
                        className="object-cover"
                        disablePictureInPicture
                        controlsList="nodownload"
                        muted
                      />
                    )}
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  {row.original.fileType == "IMAGE" && (
                    <img src={metadata.data?.presignedUrl} className="p-2" />
                  )}
                  {row.original.fileType == "DOCUMENT" && (
                    <FilesIcon className="p-2" />
                  )}
                  {row.original.fileType == "VIDEO" && (
                    <video src={metadata.data?.presignedUrl} className="p-2" />
                  )}
                </DialogContent>
              </Dialog>
              <Button
                onClick={() => {
                  const adProgramCreative =
                    campaignGroupAdCreativesQuery.data?.find(
                      (c) => c.adCreativeId === row.original.id,
                    );
                  if (adProgramCreative !== undefined) {
                    return;
                  } else {
                    deleteAssetMutation.mutate({
                      id: row.original.id,
                    });
                  }
                }}
                variant="ghost"
                className="items-start justify-start p-0 text-left text-sm font-medium hover:bg-transparent"
                disabled={
                  campaignGroupAdCreativesQuery.data?.find(
                    (c) => c.adCreativeId === row.original.id,
                  ) !== undefined
                }
              >
                <div className="flex flex-col items-start justify-start space-y-1">
                  <p>{row.original.fileName}</p>
                  {metadata.data !== undefined &&
                    metadata.data?.metadata.size && (
                      <p className="text-xs font-normal text-muted-foreground">
                        {Math.round(
                          (metadata.data.metadata.size / 1000000) * 100,
                        ) / 100}{" "}
                        MB
                      </p>
                    )}
                </div>
              </Button>
            </div>
          );
        },
      },
      {
        accessorKey: "id",
        header: "Value Props",
        cell: ({ row }) => {
          const adSegmentsQuery =
            api.v2.ads.adSegment.getAllAdSegmentsForAdProgram.useQuery({
              adProgramId: campaignGroupId,
            });
          const adProgramCreative = campaignGroupAdCreativesQuery.data?.find(
            (c) => c.adCreativeId === row.original.id,
          );
          const adSegmentValuePropCreativesQuery =
            api.v2.ads.adSegmentValuePropCreative.getForAdProgramCreative.useQuery(
              {
                adProgramCreativeId: adProgramCreative?.id ?? "",
              },
              {
                enabled: adProgramCreative !== undefined,
              },
            );
          const adProgramValueProps =
            api.v2.ads.adSegmentValueProp.getAdSegmentValuePropsForAdProgram.useQuery(
              {
                adProgramId: campaignGroupId,
                status: "DRAFT",
              },
            );

          return (
            <>
              {adProgramCreative !== undefined && (
                <Popover>
                  <PopoverTrigger asChild>
                    {adSegmentValuePropCreativesQuery.data && (
                      <Button
                        variant="ghost"
                        className="flex h-8 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-white px-3 py-1.5 text-sm shadow-sm ring-offset-background focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 data-[placeholder]:text-muted-foreground [&>span]:line-clamp-1"
                      >
                        {adProgramValueProps.data?.length ==
                          adSegmentValuePropCreativesQuery.data?.length
                          ? "All"
                          : `${adSegmentValuePropCreativesQuery.data?.length} Value Prop${adSegmentValuePropCreativesQuery.data?.length == 1 ? "" : "s"}`}
                        <ChevronDownIcon className="h-4 w-4" color="black" />
                      </Button>
                    )}
                  </PopoverTrigger>
                  <PopoverContent className="flex flex-col gap-2">
                    {adSegmentsQuery.data?.map((adSegment) => (
                      <ValuePropsSelectForAdSegment
                        key={adSegment.id}
                        adSegmentId={adSegment.id}
                        segmentId={adSegment.segmentId}
                        adProgramCreativeId={adProgramCreative.id}
                      />
                    ))}
                  </PopoverContent>
                </Popover>
              )}
            </>
          );
        },
      },
      {
        accessorKey: "id",
        header: "Date Uploaded",
        cell: ({ row }) => {
          const metadata = api.v2.ads.adCreative.getAdCreative.useQuery({
            id: row.original.id,
          });
          return (
            <>
              {metadata.data?.metadata.lastModified && (
                <p>
                  {format(metadata.data?.metadata.lastModified, "MMM d, yyyy")}
                </p>
              )}
            </>
          );
        },
      },
      {
        accessorKey: "s3BucketKey",
        header: () => { },
        cell: ({ row }) => {
          return (
            <DeleteFile id={row.original.id} disabled={row.original.beingUsed} />
          );
        },
      },
    ];

  const [sorting, setSorting] = useState<SortingState>([]);

  const table = useReactTable({
    data: adCreative.data ?? [],
    columns,
    enableRowSelection: false,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    onSortingChange: setSorting,
    state: {
      columnFilters,
      sorting,
    },
  });

  return (
    <div>
      <div className="flex h-[calc(100vh-64px)] w-full flex-col bg-background px-4 pt-4">
        <div className="mb-4 flex items-center justify-between">
          <div className="flex flex-col items-start justify-start space-y-0.5">
            <h1 className="text-xl font-medium">Creative</h1>
            <p className="text-sm text-secondary-foreground">
              Upload and select creative assets we should use. Select multiple if
              you'd like to use for creative testing.
            </p>
          </div>
          <UploadFile />
        </div>
        <div className="min-h-0 flex-1 overflow-auto">
          <DataTable
            columns={columns}
            table={table}
            rowHeight="16"
            noResultsMessage="No files uploaded"
            noHover={true}
          />
        </div>
      </div>
      <NavigationFooter
        onPrevious={() =>
          router.push(
            `/advertising/adAccounts/${adAccountId}/campaignGroups/${campaignGroupId}/ad`,
          )
        }
        onNext={onSubmit}
        nextDisabled={campaignGroupAdCreativesQuery.data?.length === 0 || isSubmitting}
        nextLoading={isSubmitting}
      />
    </div>
  );
}

function UploadFile() {
  const apiUtils = api.useUtils();
  const [file, setFile] = useState<File | undefined>(undefined);
  const [error, setError] = useState<
    | "imageFileTooLarge"
    | "documentFileTooLarge"
    | "videoFileTooLarge"
    | "invalidTypeImage"
    | "invalidTypeDocument"
    | "invalidTypeGeneral"
    | undefined
  >(undefined);
  const uploadFileQuery = api.v2.ads.adCreative.create.useMutation({
    onSuccess: async (data) => {
      if (file !== undefined) {
        await fetch(data.preSignedUrl, {
          method: "PUT",
          headers: new Headers({
            "Content-Type": file.type,
            "Access-Control-Allow-Origin": "*",
          }),
          body: file,
        });
      }
      await apiUtils.v2.ads.invalidate();
      setIsUpLoading(false);
      setIsOpen(false);
    },
  });

  const [isOpen, setIsOpen] = useState(false);
  const [upLoading, setIsUpLoading] = useState(false);
  async function onSubmit() {
    if (file !== undefined) {
      const fileTypeGourp = file.type.split("/")[0];
      const fileTypeExtension = file.type.split("/")[1];
      if (!fileTypeGourp || !fileTypeExtension) {
        throw "No file group of extension";
      }
      if (fileTypeGourp == "image") {
        if (
          fileTypeExtension != "jpeg" &&
          fileTypeExtension != "png" &&
          fileTypeExtension != "gif"
        ) {
          setError("invalidTypeImage");
          return;
        }
        if (file.size > 5000000) {
          setError("imageFileTooLarge");
          return;
        }
      } else if (fileTypeGourp == "application") {
        if (
          fileTypeExtension != "pdf" &&
          fileTypeExtension != "application/vnd.ms-powerpoint" &&
          fileTypeExtension !=
          "application/vnd.openxmlformats-officedocument.presentationml.presentation" &&
          fileTypeExtension != "application/msword" &&
          fileTypeExtension !=
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        ) {
          setError("invalidTypeDocument");
          return;
        }
        if (file.size > 100000000) {
          setError("documentFileTooLarge");
          return;
        }
      } else if (fileTypeGourp == "video") {
        if (file.size > 200000000) {
          setError("videoFileTooLarge");
          return;
        }
      } else {
        setError("invalidTypeGeneral");
        return;
      }
      setIsUpLoading(true);
      uploadFileQuery.mutate({ fileName: file.name });
    }
  }
  useEffect(() => {
    if (!isOpen) {
      setFile(undefined);
    }
  }, [isOpen]);
  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button className="bg-blue-100 text-primary">
          <UploadIcon className="mr-2" />
          Upload your Creative
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Upload your Creative</DialogTitle>
        </DialogHeader>
        <div className="flex w-full flex-col items-start justify-start space-y-4">
          <Input
            id="picture"
            type="file"
            onChange={async (event) => {
              setFile(event.target.files?.[0]);
            }}
            className="hover:bg-secondary"
          />
          {error && (
            <p className="text-xs text-destructive">
              {error === "imageFileTooLarge" &&
                "Image file is too large. Max size is 5MB"}
              {error === "documentFileTooLarge" &&
                "Document file is too large. Max size is 100MB"}
              {error === "videoFileTooLarge" &&
                "Video file is too large. Max size is 200MB"}
              {error === "invalidTypeImage" &&
                "Invalid image file type. Only JPEG, PNG, and GIF files are allowed"}
              {error === "invalidTypeDocument" &&
                "Invalid document file type. Only PDF, PPT, PPTX, DOC, and DOCX files are allowed"}
              {error === "invalidTypeGeneral" &&
                "Invalid file type. Only image and document files are allowed"}
            </p>
          )}
          <div className="flex w-full items-center justify-end">
            <Button onClick={onSubmit} disabled={upLoading || !file}>
              {upLoading && (
                <ReloadIcon className="mr-2 h-4 w-4 animate-spin" />
              )}
              Upload
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

function DeleteFile({ id, disabled }: { id: string; disabled?: boolean }) {
  const { removeAsset } = useCampaignCreationStore();
  const [isDeleteing, setIsDeleteing] = useState(false);
  const apiUtils = api.useUtils();
  const deleteFileMutation = api.v2.ads.adCreative.deleteAdCreative.useMutation(
    {
      onSuccess: async (data) => {
        removeAsset(id);
        await apiUtils.v2.ads.invalidate();
        setIsDeleteing(false);
      },
    },
  );

  async function deleteFile() {
    setIsDeleteing(true);
    deleteFileMutation.mutate({ id: id });
  }
  return (
    <Button
      variant="outline"
      disabled={isDeleteing || disabled}
      onClick={deleteFile}
    >
      {!isDeleteing && <TrashIcon color="blue" />}
      {isDeleteing && <ReloadIcon className="mr-2 h-4 w-4 animate-spin" />}
    </Button>
  );
}

function ValuePropsSelectForAdSegment({
  adSegmentId,
  segmentId,
  adProgramCreativeId,
}: {
  adSegmentId: string;
  segmentId: string;
  adProgramCreativeId: string;
}) {
  const segmentQuery = api.v2.core.segment.getSegment.useQuery({
    segmentId: segmentId,
  });

  const adSegmentValueProps =
    api.v2.ads.adSegmentValueProp.getAdSegmentValuePropsForAdSegment.useQuery({
      adSegmentId: adSegmentId,
      status: "DRAFT",
    });

  return (
    <div className="flex flex-col gap-2">
      <span className="text-xs font-medium text-muted-foreground">
        {segmentQuery.data?.name}
      </span>
      <div className="flex flex-col gap-2 text-sm">
        {adSegmentValueProps.data?.map((adSegmentValueProp) => (
          <ValuePropCheckbox
            key={adSegmentValueProp.id}
            adSegmentValueProp={adSegmentValueProp}
            adProgramCreativeId={adProgramCreativeId}
          />
        ))}
      </div>
    </div>
  );
}

function ValuePropCheckbox({
  adSegmentValueProp,
  adProgramCreativeId,
}: {
  adSegmentValueProp: any;
  adProgramCreativeId: string;
}) {
  const [isLoading, setIsLoading] = useState(false);
  const apiUtils = api.useUtils();

  const adProgramValuePropCreativesQuery =
    api.v2.ads.adSegmentValuePropCreative.getForAdProgramCreative.useQuery({
      adProgramCreativeId: adProgramCreativeId,
    });

  const createAdSegmentValuePropCreativeMutation =
    api.v2.ads.adSegmentValuePropCreative.createOne.useMutation({
      onSuccess: async () => {
        await apiUtils.v2.ads.invalidate();
        setIsLoading(false);
      },
    });

  const deleteAdSegmentValuePropCreativeMutation =
    api.v2.ads.adSegmentValuePropCreative.deleteOne.useMutation({
      onSuccess: async () => {
        await apiUtils.v2.ads.invalidate();
        setIsLoading(false);
      },
    });

  return (
    <div className="flex items-center gap-2">
      {!isLoading ? (
        <Checkbox
          disabled={
            adProgramValuePropCreativesQuery.data?.find(
              (c) => c.adSegmentValuePropId === adSegmentValueProp.id,
            )?.id !== undefined &&
            adProgramValuePropCreativesQuery.data.length == 1
          }
          checked={
            adProgramValuePropCreativesQuery.data?.find(
              (c) => c.adSegmentValuePropId === adSegmentValueProp.id,
            ) !== undefined
          }
          onCheckedChange={(checked) => {
            if (checked) {
              setIsLoading(true);
              createAdSegmentValuePropCreativeMutation.mutate({
                adProgramCreativeId: adProgramCreativeId,
                adSegmentValuePropId: adSegmentValueProp.id,
              });
            } else {
              if (
                adProgramValuePropCreativesQuery.data?.find(
                  (c) => c.adSegmentValuePropId === adSegmentValueProp.id,
                )?.id !== undefined
              ) {
                if (adProgramValuePropCreativesQuery.data.length == 1) {
                  return;
                }
                setIsLoading(true);
                deleteAdSegmentValuePropCreativeMutation.mutate({
                  adSegmentValuePropCreativeId:
                    adProgramValuePropCreativesQuery.data?.find(
                      (c) => c.adSegmentValuePropId === adSegmentValueProp.id,
                    )?.id ?? "",
                });
              }
            }
          }}
        />
      ) : (
        <ReloadIcon className="h-4 w-4 animate-spin" />
      )}
      <p>{adSegmentValueProp.valueProp}</p>
    </div>
  );
}
