"use client";

import type { ColumnDef } from "@tanstack/react-table";
import React, { useEffect, useState } from "react";
import { api } from "@/trpc/client";
import { getCoreRowModel, useReactTable } from "@tanstack/react-table";
import { ChevronDown, NotebookIcon, RefreshCw } from "lucide-react";

import { Button } from "@kalos/ui/button";
import { Checkbox } from "@kalos/ui/checkbox";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@kalos/ui/dialog";
import { cn } from "@kalos/ui/index";
import { DataTable } from "@kalos/ui/table/datatable";

interface AdFormData {
  name: string;
  id: string;
  language: string;
  created: number;
  lifecycleState: string;
  reviewStatus: string;
}

const LeadGenFormsDialog = ({
  selectedFormId,
  adAccountId,
  setSelectedFormId,
  disabled,
}: {
  selectedFormId: string | null;
  adAccountId: string;
  setSelectedFormId: (id: string | null) => void;
  disabled: boolean;
}) => {
  useEffect(() => {
    if (selectedFormId) {
      setSelectedFormUrn(selectedFormId);
    }
  }, [selectedFormId]);

  const fetchFormsQuery =
    api.v2.ads.linkedInApi.leadGenForms.getForAccount.useQuery({
      adAccountId,
    });
  const adAccountQuery = api.v2.ads.adAccounts.getForOrganization.useQuery();
  const organizationQuery =
    api.user.organizationUser.get.organization.useQuery();
  const [forms, setForms] = useState<AdFormData[]>([]);
  const [selectedFormUrn, setSelectedFormUrn] = useState<string | null>(null);
  const [open, setOpen] = useState(false);

  const findExistingAdFormQuery = api.advertising.adForm.findOne.useQuery(
    { linkedInUrn: parseInt(selectedFormUrn!) },
    { enabled: !!selectedFormUrn },
  );

  const createAdFormMutation = api.advertising.adForm.create.useMutation({
    onSuccess: (data) => {
      console.log("adForm created", data);
    },
  });

  const handleFormSubmit = () => {
    if (!organizationQuery.data?.organizationId) return;

    setSelectedFormId(selectedFormUrn);

    setOpen(false);
  };

  // Debug prints
  useEffect(() => {
    if (fetchFormsQuery.data) {
      console.log("forms", fetchFormsQuery.data);
      setForms(
        fetchFormsQuery.data
          .map((form) => ({
            name: form.name,
            id: form.leadGenFormUrn,
            language: form.language,
            created: form.created,
            reviewStatus: form.reviewStatus,
            lifecycleState: form.lifecycleState,
          }))
          .filter((form) => form.lifecycleState === "PUBLISHED"),
      );
    }
  }, [fetchFormsQuery.data]);

  const columns: ColumnDef<AdFormData>[] = [
    {
      id: "select",
      header: "",
      cell: ({ row }) => (
        <Checkbox
          checked={selectedFormUrn === row.original.id}
          onCheckedChange={() =>
            setSelectedFormUrn(
              selectedFormUrn === row.original.id ? null : row.original.id,
            )
          }
        />
      ),
    },
    {
      accessorKey: "name",
      header: "Form name",
      cell: ({ row }) => (
        <div>
          <div className="font-medium text-gray-900">{row.original.name}</div>
          <div className="text-sm text-gray-500">ID: {row.original.id}</div>
        </div>
      ),
    },
    {
      accessorKey: "language",
      header: "Language",
    },
    {
      accessorKey: "reviewStatus",
      header: "Status",
      cell: ({ row }) => (
        <div>{row.original.reviewStatus.split("_").join(" ")}</div>
      ),
    },
    {
      accessorKey: "created",
      header: "Date created",
      cell: ({ row }) => (
        <div>{new Date(row.original.created).toLocaleDateString()}</div>
      ),
    },
  ];

  const table = useReactTable({
    data: forms,
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger
        className={cn(
          "flex h-[35px] w-full items-center justify-between rounded border px-1 py-2",
          selectedFormUrn
            ? "border-2 border-primary bg-accent/20"
            : "border-gray-300",
          disabled
            ? "cursor-not-allowed bg-gray-100 text-gray-400"
            : "bg-white text-gray-900 hover:bg-gray-50",
        )}
        disabled={disabled}
      >
        <span
          className={cn(
            "flex items-center gap-2 text-sm text-muted-foreground",
            selectedFormUrn && forms.length > 0 ? "text-black" : "",
          )}
        >
          {selectedFormUrn && forms.length > 0 ? (
            `Selected: ${forms.find((form) => form.id === selectedFormUrn)?.name}`
          ) : (
            <>
              <NotebookIcon className="h-4 w-4" />
              <span className="text-sm">Lead generation form to collect prospect contact information</span>
            </>
          )}
        </span>
        {/* <ChevronDown className="ml-4 h-4 w-4 opacity-50" /> */}
      </DialogTrigger>

      <DialogContent className="flex h-[600px] flex-col sm:max-w-[1200px]">
        <DialogHeader className="flex-shrink-0">
          <div className="flex items-center">
            <DialogTitle>Lead Generation Form</DialogTitle>

            <div className="ml-4">
              <Button
                onClick={() => fetchFormsQuery.refetch()}
                variant="outline"
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                Refresh
              </Button>
            </div>
            <div className="ml-4">
              <Button
                onClick={() =>
                  window.open(
                    `https://www.linkedin.com/campaignmanager/accounts/${adAccountQuery.data
                      ?.find((adAccount) => adAccount.id === adAccountId)
                      ?.linkedInAdAccountUrn.split(
                        "urn:li:sponsoredAccount:",
                      )[1]
                    }/leadgen-forms/new`,
                    "_blank",
                  )
                }
              >
                Create New Form
              </Button>
            </div>
          </div>
        </DialogHeader>
        <div className="flex-1 overflow-y-auto">
          {fetchFormsQuery.isLoading && (
            <div className="space-y-4 p-6">
              <div className="h-12 w-full animate-pulse rounded-md bg-gray-100"></div>
              <div className="h-12 w-full animate-pulse rounded-md bg-gray-100"></div>
              <div className="h-12 w-full animate-pulse rounded-md bg-gray-100"></div>
            </div>
          )}

          {fetchFormsQuery.isError && (
            <div className="flex flex-col items-center justify-center p-8 text-center">
              <p className="mb-4 text-destructive">Error loading forms.</p>
              <Button
                variant="outline"
                onClick={() => fetchFormsQuery.refetch()}
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                Try again
              </Button>
            </div>
          )}

          {!fetchFormsQuery.isLoading &&
            !fetchFormsQuery.isError &&
            forms.length === 0 && (
              <div className="flex flex-col items-center justify-center p-8 text-center">
                <p className="mb-4 text-muted-foreground">
                  No forms available.
                </p>
                <p className="text-sm text-muted-foreground">
                  Use the <span className="font-semibold">Create New Form</span>{" "}
                  button above to create a LinkedIn form.
                </p>
              </div>
            )}

          {!fetchFormsQuery.isLoading &&
            !fetchFormsQuery.isError &&
            forms.length > 0 && (
              <DataTable columns={columns} table={table} noHover={true} />
            )}
        </div>
        <DialogFooter className="flex-shrink-0">
          <DialogClose asChild>
            <Button
              type="submit"
              className="mt-4"
              onClick={handleFormSubmit}
            // disabled={!selectedFormUrn}
            >
              Submit
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default LeadGenFormsDialog;
