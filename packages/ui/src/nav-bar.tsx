import * as React from "react";
import Image from "next/image";
import Link from "next/link";
import { ChevronDownIcon } from "@radix-ui/react-icons";
import { Slot } from "@radix-ui/react-slot";
import { cva, VariantProps } from "class-variance-authority";

import Kalos<PERSON>ogo from "./icons/kalos-logo";
import { cn, hexColors } from "./index";

const NavBarShell = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      "flex w-60 flex-col justify-start self-stretch border bg-muted",
      className,
    )}
    {...props}
  />
));

function KalosLogoSections({ clientName }: { clientName: string }) {
  return (
    <div className="flex flex-col space-y-3 px-4 py-4 border-b border-border/50">
      <div className="flex h-12 items-center justify-start">
        <Link href="/" className="flex items-center">
          <Image
            src="/logo.svg"
            alt="Logo"
            width={32}
            height={32}
            className="h-8 w-auto"
          />
        </Link>
      </div>
      <div className="flex flex-col">
        <h2 className="text-sm font-medium text-foreground truncate">
          {clientName}
        </h2>
      </div>
    </div>
  );
}

const NavBarItems = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      "flex h-full flex-col items-center justify-start gap-y-5 self-stretch px-4 py-4",
      className,
    )}
    {...props}
  />
));

const NavBarItemsGroup = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      "flex flex-col items-center justify-start space-y-3 self-stretch",
      className,
    )}
    {...props}
  />
));

const NavBarItemsGroupHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      "flex items-center justify-start space-x-1.5 self-stretch ",
      className,
    )}
    {...props}
  />
));

const NavBarItemsGroupTitle = ({ title }: { title: string }) => {
  return (
    <h1 className="text-xs font-medium text-secondary-foreground">{title}</h1>
  );
};

const navBarItemVariants = cva(
  "flex items-center justify-start space-x-2 self-stretch rounded-md text-sm font-medium text-foreground",
  {
    variants: {
      isActivePage: {
        isActive: " text-foreground",
        isNotActive: "",
      },
    },
    defaultVariants: {
      isActivePage: "isNotActive",
    },
  },
);

const NavBarItemTitle = ({ title }: { title: string }) => {
  return <h1 className="text-sm font-medium text-foreground">{title}</h1>;
};

export interface NavBarItemProps
  extends React.HTMLAttributes<HTMLDivElement>,
  VariantProps<typeof navBarItemVariants> {
  asChild?: boolean;
}

const NavBarItem = React.forwardRef<HTMLDivElement, NavBarItemProps>(
  ({ className, isActivePage, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "div";
    return (
      <Comp
        className={cn(navBarItemVariants({ isActivePage, className }))}
        ref={ref}
        {...props}
      />
    );
  },
);

export const NavBar = {
  Shell: NavBarShell,
  Items: NavBarItems,
  ItemsGroup: NavBarItemsGroup,
  Item: NavBarItem,
  ClientInfoSection: KalosLogoSections,
  ItemsGroupHeader: NavBarItemsGroupHeader,
  ItemsGroupHeaderTitle: NavBarItemsGroupTitle,
  ItemTitle: NavBarItemTitle,
};
