import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { eq, and } from "drizzle-orm";
import { GetObjectCommand, S3Client } from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";

import { linkedInAdAccountHandlers } from "@kalos/database/handlers/linkedInAdAccount";
import { linkedInOAuthHandlers } from "@kalos/database/handlers/linkedInOAuth";
import { getLinkedInApiClientFromOrganizationId } from "@kalos/linkedin-api";

// Database imports
import { db } from "../../../../../backend/src/database/db";
import { linkedInSponsoredCreativeTable } from "../../../../../backend/src/database/schemas/advertising/linkedInSponsoredCreative.table";
import { conversationCallToActionCopyTable } from "../../../../../backend/src/database/schemas/advertising/conversationCallToActionCopy.table";
import { conversationMessageCopyTable } from "../../../../../backend/src/database/schemas/advertising/conversationMessageCopy.table";
import { conversationSubjectCopyTable } from "../../../../../backend/src/database/schemas/advertising/conversationSubjectCopy.table";
import { linkedInPostTable } from "../../../../../backend/src/database/schemas/advertising/linkedInPost.table";
import { linkedInSingleImagePostTable } from "../../../../../backend/src/database/schemas/advertising/linkedInSingleImagePost.table";
import { linkedInAdSegmentValuePropTable } from "../../../../../backend/src/database/schemas/advertising/linkedInAdSegmentValueProp.table";
import { linkedInAdSegmentTable } from "../../../../../backend/src/database/schemas/advertising/linkedInAdSegment.table";
import { socialPostCopyTable } from "../../../../../backend/src/database/schemas/advertising/socialPostCopy.table";
import { socialPostCallToActionCopyTable } from "../../../../../backend/src/database/schemas/advertising/socialPostCallToActionCopy.table";
import { linkedInAdProgramCreativeTable } from "../../../../../backend/src/database/schemas/advertising/linkedInAdProgramCreative.table";
import { adCreativeTable } from "../../../../../backend/src/database/schemas/advertising/adCreative.table";
import { linkedInCampaignTable } from "../../../../../backend/src/database/schemas/advertising/linkedInCampaign.table";
import { linkedInAudienceTable } from "../../../../../backend/src/database/schemas/advertising/linkedInAudience.table";

import { organizationRoute } from "../../trpc";

export const linkedInRouter = {
  getOauth: organizationRoute.query(async ({ ctx }) => {
    const linkedInAuth = linkedInOAuthHandlers.select.one.byUserId(ctx.userId);
    return linkedInAuth;
  }),
  getAdTargetingFacets: organizationRoute.query(async ({ ctx }) => {
    const linkedInClient = await getLinkedInApiClientFromOrganizationId(
      ctx.organizationId,
    );
    if (!linkedInClient) {
      throw new Error("LinkedIn client not found");
    }
    const facets = await linkedInClient.getFacets();
    return facets;
  }),
  searchTypeahead: organizationRoute
    .input(
      z.object({
        facetUrn: z.string(),
        query: z.string(),
      }),
    )
    .query(async ({ ctx, input }) => {
      if (input.query.length == 0) {
        return [];
      }
      const linkedInClient = await getLinkedInApiClientFromOrganizationId(
        ctx.organizationId,
      );
      if (!linkedInClient) {
        throw new Error("LinkedIn client not found");
      }
      const results = await linkedInClient.getEntitesViaTypeahead(
        input.facetUrn,
        input.query,
      );
      return results;
    }),
  searchEntity: organizationRoute
    .input(z.object({ urn: z.string() }))
    .query(async ({ ctx, input }) => {
      const linkedInClient = await getLinkedInApiClientFromOrganizationId(
        ctx.organizationId,
      );
      if (!linkedInClient) {
        throw new Error("LinkedIn client not found");
      }
      const result = await linkedInClient.getEntitesViaAdTargetingFacet(
        input.urn,
      );
      return result;
    }),

  getAdAccountsFromLinkedIn: organizationRoute.query(async ({ ctx }) => {
    const client = await getLinkedInApiClientFromOrganizationId(
      ctx.organizationId,
    );
    if (!client) {
      throw new Error("LinkedIn client not found");
    }

    const res = await client.getAdAccountsForUser();
    const accounts: {
      name: string;
      id: number;
      organizationOrPersonId: number;
    }[] = [];
    for (const each of res) {
      const acc = await client.getAdAccount(
        each.account.split(":")[each.account.split(":").length - 1] ?? "",
      );
      const isOrganizationOrPerson = acc.reference.includes("organization")
        ? "organization"
        : "person";
      if (isOrganizationOrPerson == "person") {
        continue;
      }
      console.log(acc);
      accounts.push({
        name: acc.name,
        id: acc.id,
        organizationOrPersonId: parseInt(
          acc.reference.split(":")[acc.reference.split(":").length - 1] ?? "0",
        ),
      });
    }
    return accounts;
  }),
  getAdAccount: organizationRoute.query(async ({ ctx }) => {
    const adAccount =
      await linkedInAdAccountHandlers.select.one.byOrganizationId(
        ctx.organizationId,
      );
    if (!adAccount[0]) {
      return null;
    }
    return adAccount[0];
  }),
  setAdAccountForOrganization: organizationRoute
    .input(z.object({ name: z.string(), urn: z.number() }))
    .mutation(async ({ ctx, input }) => {
      await linkedInAdAccountHandlers.setForOrganization(
        ctx.organizationId,
        input.urn,
        input.name,
      );
    }),
  getLeadGenForms: organizationRoute.query(async ({ ctx }) => {
    const linkedInClient = await getLinkedInApiClientFromOrganizationId(
      ctx.organizationId,
    );
    console.log("orgId", ctx.organizationId);
    const adAccount =
      await linkedInAdAccountHandlers.select.one.byOrganizationId(
        ctx.organizationId,
      );
    if (!adAccount[0]) {
      return [];
    }
    if (!linkedInClient) {
      throw new Error("LinkedIn client not found");
    }
    const forms = await linkedInClient.getLeadForms(adAccount[0].linkedInUrn);
    return forms;
  }),
  getCampaignAnalytics: organizationRoute
    .input(
      z.object({
        campaignIds: z.array(z.string()),
        timeGranularity: z
          .enum(["DAILY", "ALL", "MONTHLY", "YEARLY"])
          .optional()
          .default("DAILY"),
      }),
    )
    .query(async ({ ctx, input }) => {
      const linkedInClient = await getLinkedInApiClientFromOrganizationId(
        ctx.organizationId,
      );
      return linkedInClient?.getCampaignAnalytics(
        input.campaignIds,
        new Date("2024-01-01"),
        undefined,
        input.timeGranularity,
      );
    }),
  getCampaigns: organizationRoute
    .input(z.object({ campaignLinkedInUrns: z.array(z.string()) }))
    .query(async ({ ctx, input }) => {
      const adAccount =
        await linkedInAdAccountHandlers.select.one.byOrganizationId(
          ctx.organizationId,
        );
      if (!adAccount[0]) {
        return [];
      }
      const linkedInClient = await getLinkedInApiClientFromOrganizationId(
        ctx.organizationId,
      );
      if (!linkedInClient) {
        throw new Error("LinkedIn client not found");
      }
      return linkedInClient.getCampaigns(
        adAccount[0].linkedInUrn,
        input.campaignLinkedInUrns.map((urn) => parseInt(urn)),
      );
    }),
  updateCampaignDailyBudget: organizationRoute
    .input(z.object({ campaignUrn: z.string(), dailyBudget: z.number() }))
    .mutation(async ({ ctx, input }) => {
      const adAccount =
        await linkedInAdAccountHandlers.select.one.byOrganizationId(
          ctx.organizationId,
        );
      if (!adAccount[0]) {
        throw new Error("Ad account not found");
      }
      const linkedInClient = await getLinkedInApiClientFromOrganizationId(
        ctx.organizationId,
      );
      if (!linkedInClient) {
        throw new Error("LinkedIn client not found");
      }
      await linkedInClient.updateCampaignDailyBudget(
        adAccount[0].linkedInUrn.toString(),
        input.campaignUrn,
        input.dailyBudget,
      );
    }),
  setCampaignUnitCost: organizationRoute
    .input(z.object({ campaignUrn: z.string(), unitCost: z.number() }))
    .mutation(async ({ ctx, input }) => {
      const adAccount =
        await linkedInAdAccountHandlers.select.one.byOrganizationId(
          ctx.organizationId,
        );
      if (!adAccount[0]) {
        throw new Error("Ad account not found");
      }
      const linkedInClient = await getLinkedInApiClientFromOrganizationId(
        ctx.organizationId,
      );
      if (!linkedInClient) {
        throw new Error("LinkedIn client not found");
      }
      await linkedInClient.setCampaignUnitCost(
        adAccount[0].linkedInUrn,
        input.campaignUrn,
        input.unitCost,
      );
    }),
  getMatchedAudiences: organizationRoute.query(async ({ ctx, input }) => {
    const linkedInClient = await getLinkedInApiClientFromOrganizationId(
      ctx.organizationId,
    );
    if (!linkedInClient) {
      throw new Error("LinkedIn client not found");
    }
    const adAccount =
      await linkedInAdAccountHandlers.select.one.byOrganizationId(
        ctx.organizationId,
      );
    if (!adAccount[0]) {
      return [];
    }
    return linkedInClient.getAdSegments(adAccount[0].linkedInUrn);
  }),
  getAdSegments: organizationRoute
    .input(
      z
        .object({
          types: z
            .enum(["BULK", "RETARGETING", "MARKET_AUTOMATION"])
            .optional(),
          start: z.number().optional(),
          count: z.number().optional(),
        })
        .optional(),
    )
    .query(async ({ ctx, input }) => {
      const linkedInClient = await getLinkedInApiClientFromOrganizationId(
        ctx.organizationId,
      );
      if (!linkedInClient) {
        throw new Error("LinkedIn client not found");
      }
      const adAccount =
        await linkedInAdAccountHandlers.select.one.byOrganizationId(
          ctx.organizationId,
        );
      if (!adAccount[0]) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Ad account not found",
        });
      }
      return linkedInClient.getSegments(adAccount[0].linkedInUrn.toString(), {
        types: input?.types,
        start: input?.start,
        count: input?.count,
      });
    }),
  getSponsoredInmailContent: organizationRoute
    .input(z.object({ adCreativeUrn: z.string() }))
    .query(async ({ ctx, input }) => {
      try {
        // Check if input looks like a URN or a UUID
        const isUrn = input.adCreativeUrn.startsWith('urn:li:sponsoredCreative:');
        const searchValue = input.adCreativeUrn;
        const conversationData = await db
          .select({
            subject: conversationSubjectCopyTable.content,
            message: conversationMessageCopyTable.content,
            callToAction: conversationCallToActionCopyTable.content,
            callToActionType: conversationCallToActionCopyTable.type,
            creativeType: linkedInSponsoredCreativeTable.type,
            messageId: conversationMessageCopyTable.id,
          })
          .from(linkedInSponsoredCreativeTable)
          .innerJoin(
            conversationCallToActionCopyTable,
            eq(linkedInSponsoredCreativeTable.conversationCallToActionId, conversationCallToActionCopyTable.id)
          )
          .innerJoin(
            conversationMessageCopyTable,
            eq(conversationCallToActionCopyTable.conversationMessageCopyId, conversationMessageCopyTable.id)
          )
          .innerJoin(
            conversationSubjectCopyTable,
            eq(conversationMessageCopyTable.conversationSubjectCopyId, conversationSubjectCopyTable.id)
          )
          .where(
            isUrn 
              ? eq(linkedInSponsoredCreativeTable.linkedInSponsoredCreativeUrn, searchValue)
              : eq(linkedInSponsoredCreativeTable.id, searchValue)
          )
          .limit(1);
                
        if (!conversationData[0]) {
          const alternativeSearchValue = isUrn ? searchValue : searchValue;
          const alternativeWhere = isUrn 
            ? eq(linkedInSponsoredCreativeTable.id, searchValue)
            : eq(linkedInSponsoredCreativeTable.linkedInSponsoredCreativeUrn, searchValue);
          
          const creativeExists = await db
            .select({ 
              id: linkedInSponsoredCreativeTable.id,
              urn: linkedInSponsoredCreativeTable.linkedInSponsoredCreativeUrn,
              type: linkedInSponsoredCreativeTable.type,
              conversationCallToActionId: linkedInSponsoredCreativeTable.conversationCallToActionId
            })
            .from(linkedInSponsoredCreativeTable)
            .where(
              isUrn 
                ? eq(linkedInSponsoredCreativeTable.linkedInSponsoredCreativeUrn, searchValue)
                : eq(linkedInSponsoredCreativeTable.id, searchValue)
            )
            .limit(1);
          return null;
        }

        const mainData = conversationData[0];

        // Only return data for INMAIL type creatives
        if (mainData.creativeType !== "INMAIL") {
          console.log(`Creative is not INMAIL type: ${mainData.creativeType}`);
          return null;
        }

        // Get all call to action copies for this message (for multiple buttons)
        const allCallToActionCopies = await db
          .select({
            id: conversationCallToActionCopyTable.id,
            content: conversationCallToActionCopyTable.content,
            type: conversationCallToActionCopyTable.type,
          })
          .from(conversationCallToActionCopyTable)
          .where(eq(conversationCallToActionCopyTable.conversationMessageCopyId, mainData.messageId));

        const result = {
          subjectLine: mainData.subject,
          messageContent: mainData.message,
          callToActionButtons: allCallToActionCopies.map(cta => ({
            content: cta.content,
            type: cta.type,
          })),
        };
        return result;
      } catch (error) {
        console.error("Error fetching sponsored inmail content:", error);
        // Fallback to mock data in case of error
        return {
          subjectLine: "N/A",
          messageContent: "N/A",
          callToActionButtons: [
            {
              content: "CTA N/A",
              type: "primary",
            }
          ],
        };
      }
    }),
  getSingleImageContent: organizationRoute
    .input(z.object({ adCreativeUrn: z.string() }))
    .query(async ({ ctx, input }) => {
      try {
        // Check if input looks like a URN or a UUID
        const isUrn = input.adCreativeUrn.startsWith('urn:li:sponsoredCreative:');
        const searchValue = input.adCreativeUrn;

        // Use the query provided by the user
        const singleImageData = await db
          .select({
            // Basic creative info
            linkedInSponsoredCreativeUrn: linkedInSponsoredCreativeTable.linkedInSponsoredCreativeUrn,
            status: linkedInSponsoredCreativeTable.status,
            type: linkedInSponsoredCreativeTable.type,
            
            // Post copy info
            description: socialPostCopyTable.body,
            
            // Call to action
            ctaText: socialPostCallToActionCopyTable.callToAction,
            
            // Image info
            imageUrl: adCreativeTable.s3BucketKey,
            imageName: adCreativeTable.fileName,
            fileType: adCreativeTable.fileType,
            
            // Destination URL
            destinationUrl: linkedInAdSegmentTable.destinationUrl,
            
            // Additional info
            leadGenFormUrn: socialPostCopyTable.leadGenFormUrn,
            linkedInPostType: linkedInPostTable.linkedInPostType,
            linkedInAdSegmentValueProp: linkedInSingleImagePostTable.linkedInAdSegmentValueProp,
            linkedInAdProgramId: linkedInAdProgramCreativeTable.linkedInAdProgramId,
          })
          .from(linkedInSponsoredCreativeTable)
          .leftJoin(
            linkedInPostTable,
            eq(linkedInSponsoredCreativeTable.linkedInPostId, linkedInPostTable.id)
          )
          .leftJoin(
            linkedInSingleImagePostTable,
            eq(linkedInPostTable.singleImagePostId, linkedInSingleImagePostTable.id)
          )
          .leftJoin(
            linkedInAdSegmentValuePropTable,
            eq(linkedInSingleImagePostTable.linkedInAdSegmentValueProp, linkedInAdSegmentValuePropTable.id)
          )
          .leftJoin(
            linkedInAdSegmentTable,
            eq(linkedInAdSegmentValuePropTable.linkedInAdSegmentId, linkedInAdSegmentTable.id)
          )
          .leftJoin(
            socialPostCopyTable,
            and(
              eq(linkedInSingleImagePostTable.linkedInAdSegmentValueProp, socialPostCopyTable.linkedInAdSegmentValuePropId),
              eq(linkedInSingleImagePostTable.socialPostCopyType, socialPostCopyTable.socialPostCopyType)
            )
          )
          .leftJoin(
            socialPostCallToActionCopyTable,
            and(
              eq(linkedInSingleImagePostTable.linkedInAdSegmentValueProp, socialPostCallToActionCopyTable.adSegmentValuePropId),
              eq(linkedInSingleImagePostTable.callToActionCopyType, socialPostCallToActionCopyTable.type)
            )
          )
          .leftJoin(
            linkedInAdProgramCreativeTable,
            eq(linkedInSingleImagePostTable.adProgramCreativeId, linkedInAdProgramCreativeTable.id)
          )
          .leftJoin(
            adCreativeTable,
            eq(linkedInAdProgramCreativeTable.adCreativeId, adCreativeTable.id)
          )
          .where(
            and(
              isUrn 
                ? eq(linkedInSponsoredCreativeTable.linkedInSponsoredCreativeUrn, searchValue)
                : eq(linkedInSponsoredCreativeTable.id, searchValue),
              eq(linkedInPostTable.linkedInPostType, 'SINGLE_IMAGE')
            )
          )
          .limit(1);
                
        if (!singleImageData[0]) {

          // Return more realistic fallback data for debugging
          return {
            description: "description N/A",
            ctaText: "CTA N/A",
            imageUrl: "",
            imageName: "",
            destinationUrl: "destinationUrl N/A",
            leadGenFormUrn: "",
            fileType: "IMAGE",
          };
        }

        const mainData = singleImageData[0];

        // Only return data for POST type creatives with SINGLE_IMAGE
        if (mainData.type !== "POST" || mainData.linkedInPostType !== "SINGLE_IMAGE") {
          console.log(`Creative is not SINGLE_IMAGE POST type: ${mainData.type}/${mainData.linkedInPostType}`);
          return null;
        }
        // Generate presigned URL for the image if s3BucketKey exists
        let presignedImageUrl = "";
        if (mainData.imageUrl) {
          try {
            const bucket = process.env.AD_ASSET_S3;
            if (bucket) {
              const s3 = new S3Client({});
              const command = new GetObjectCommand({
                Key: mainData.imageUrl,
                Bucket: bucket,
              });
              presignedImageUrl = await getSignedUrl(s3, command);
            }
          } catch (s3Error) {
            console.error(`[getSingleImageContent] Error generating presigned URL:`, s3Error);
          }
        }

        const result = {
          description: mainData.description || "",
          ctaText: mainData.ctaText || "Learn more",
          imageUrl: presignedImageUrl || "",
          imageName: mainData.imageName || "",
          destinationUrl: mainData.destinationUrl || "",
          leadGenFormUrn: mainData.leadGenFormUrn || "",
          fileType: mainData.fileType || "",
        };
        return result;
      } catch (error) {
        console.error("Error fetching single image content:", error);
        // Fallback to mock data in case of error
        return {
          description: "description N/A",
          ctaText: "CTA N/A",
          imageUrl: "",
          imageName: "",
          destinationUrl: "destinationUrl N/A",
          leadGenFormUrn: "",
          fileType: "IMAGE",
        };
      }
    }),
  getAdDataByCreativeId: organizationRoute
    .input(z.object({ 
      sponsoredCreativeId: z.string(),
      adFormat: z.enum(["SINGLE_IMAGE", "SPONSORED_INMAIL", "SPONSORED_CONVERSATION", "VIDEO", "DOCUMENT"])
    }))
    .query(async ({ ctx, input }) => {
      try {
        console.log(`[getAdDataByCreativeId] Fetching ad data for creative ID: ${input.sponsoredCreativeId}, format: ${input.adFormat}`);

        // Base query to get sponsored creative and related data
        const baseAdData = await db
          .select({
            creativeId: linkedInSponsoredCreativeTable.id,
            creativeUrn: linkedInSponsoredCreativeTable.linkedInSponsoredCreativeUrn,
            creativeType: linkedInSponsoredCreativeTable.type,
            creativeStatus: linkedInSponsoredCreativeTable.status,
            segmentId: linkedInAdSegmentTable.id,
            campaignId: linkedInCampaignTable.linkedInAudienceId,
            audienceId: linkedInAudienceTable.id,
            valuePropId: linkedInAdSegmentValuePropTable.id,
            valueProp: linkedInAdSegmentValuePropTable.valueProp,
          })
          .from(linkedInSponsoredCreativeTable)
          .innerJoin(
            linkedInCampaignTable,
            eq(linkedInSponsoredCreativeTable.linkedInCampaignId, linkedInCampaignTable.linkedInAudienceId)
          )
          .innerJoin(
            linkedInAudienceTable,
            eq(linkedInCampaignTable.linkedInAudienceId, linkedInAudienceTable.id)
          )
          .innerJoin(
            linkedInAdSegmentTable,
            eq(linkedInAudienceTable.linkedInAdSegmentId, linkedInAdSegmentTable.id)
          )
          .innerJoin(
            linkedInAdSegmentValuePropTable,
            eq(linkedInAdSegmentTable.id, linkedInAdSegmentValuePropTable.linkedInAdSegmentId)
          )
          .where(eq(linkedInSponsoredCreativeTable.id, input.sponsoredCreativeId))
          .limit(1);

        if (!baseAdData[0]) {
          console.log(`[getAdDataByCreativeId] No base data found for creative ID: ${input.sponsoredCreativeId}`);
          return null;
        }

        const baseData = baseAdData[0];
        console.log(`[getAdDataByCreativeId] Found base data:`, {
          creativeId: baseData.creativeId,
          creativeUrn: baseData.creativeUrn,
          creativeType: baseData.creativeType,
          creativeStatus: baseData.creativeStatus
        });

        // Handle different ad formats
        if (input.adFormat === "SPONSORED_INMAIL" && baseData.creativeType === "INMAIL") {
          // Get conversation data for SPONSORED_INMAIL
          const conversationData = await db
            .select({
              subjectId: conversationSubjectCopyTable.id,
              subjectContent: conversationSubjectCopyTable.content,
              subjectType: conversationSubjectCopyTable.type,
              messageId: conversationMessageCopyTable.id,
              messageContent: conversationMessageCopyTable.content,
              messageType: conversationMessageCopyTable.type,
              callToActionId: conversationCallToActionCopyTable.id,
              callToActionContent: conversationCallToActionCopyTable.content,
              callToActionType: conversationCallToActionCopyTable.type,
            })
            .from(linkedInSponsoredCreativeTable)
            .innerJoin(
              conversationCallToActionCopyTable,
              eq(linkedInSponsoredCreativeTable.conversationCallToActionId, conversationCallToActionCopyTable.id)
            )
            .innerJoin(
              conversationMessageCopyTable,
              eq(conversationCallToActionCopyTable.conversationMessageCopyId, conversationMessageCopyTable.id)
            )
            .innerJoin(
              conversationSubjectCopyTable,
              eq(conversationMessageCopyTable.conversationSubjectCopyId, conversationSubjectCopyTable.id)
            )
            .where(eq(linkedInSponsoredCreativeTable.id, input.sponsoredCreativeId))
            .limit(1);

          if (conversationData[0]) {
            const convoData = conversationData[0];
            return {
              adFormat: input.adFormat,
              creativeId: baseData.creativeId,
              creativeUrn: baseData.creativeUrn,
              segmentId: baseData.segmentId,
              valueProp: baseData.valueProp,
              title: `${convoData.subjectContent}`,
              description: convoData.messageContent,
              callToAction: convoData.callToActionContent,
              adSpecificData: {
                subjectLine: convoData.subjectContent,
                messageContent: convoData.messageContent,
                callToActionText: convoData.callToActionContent,
                subjectType: convoData.subjectType,
                messageType: convoData.messageType,
                callToActionType: convoData.callToActionType,
              }
            };
          }
        } 
        else if (input.adFormat === "SINGLE_IMAGE" && baseData.creativeType === "POST") {
          // Get social post data for SINGLE_IMAGE
          const socialPostData = await db
            .select({
              postId: linkedInPostTable.id,
              socialPostId: socialPostCopyTable.id,
              socialPostTitle: socialPostCopyTable.title,
              socialPostBody: socialPostCopyTable.body,
              socialPostType: socialPostCopyTable.socialPostCopyType,
              ctaId: socialPostCallToActionCopyTable.id,
              ctaText: socialPostCallToActionCopyTable.callToAction,
              ctaType: socialPostCallToActionCopyTable.type,
              creativeFileName: adCreativeTable.fileName,
              creativeS3Key: adCreativeTable.s3BucketKey,
              creativeFileType: adCreativeTable.fileType,
            })
            .from(linkedInSponsoredCreativeTable)
            .innerJoin(
              linkedInPostTable,
              eq(linkedInSponsoredCreativeTable.linkedInPostId, linkedInPostTable.id)
            )
            .innerJoin(
              linkedInSingleImagePostTable,
              eq(linkedInPostTable.singleImagePostId, linkedInSingleImagePostTable.id)
            )
            .innerJoin(
              socialPostCopyTable,
              and(
                eq(linkedInSingleImagePostTable.linkedInAdSegmentValueProp, socialPostCopyTable.linkedInAdSegmentValuePropId),
                eq(linkedInSingleImagePostTable.socialPostCopyType, socialPostCopyTable.socialPostCopyType)
              )
            )
            .leftJoin(
              socialPostCallToActionCopyTable,
              and(
                eq(linkedInSingleImagePostTable.linkedInAdSegmentValueProp, socialPostCallToActionCopyTable.adSegmentValuePropId),
                eq(linkedInSingleImagePostTable.callToActionCopyType, socialPostCallToActionCopyTable.type)
              )
            )
            .innerJoin(
              linkedInAdProgramCreativeTable,
              eq(linkedInSingleImagePostTable.adProgramCreativeId, linkedInAdProgramCreativeTable.id)
            )
            .innerJoin(
              adCreativeTable,
              eq(linkedInAdProgramCreativeTable.adCreativeId, adCreativeTable.id)
            )
            .where(eq(linkedInSponsoredCreativeTable.id, input.sponsoredCreativeId))
            .limit(1);

          if (socialPostData[0]) {
            const postData = socialPostData[0];
            return {
              adFormat: input.adFormat,
              creativeId: baseData.creativeId,
              creativeUrn: baseData.creativeUrn,
              segmentId: baseData.segmentId,
              valueProp: baseData.valueProp,
              title: postData.socialPostTitle,
              description: postData.socialPostBody,
              callToAction: postData.ctaText || "Learn More",
              imageUrl: postData.creativeS3Key ? `https://your-s3-bucket.amazonaws.com/${postData.creativeS3Key}` : undefined,
              adSpecificData: {
                socialPostTitle: postData.socialPostTitle,
                socialPostBody: postData.socialPostBody,
                socialPostType: postData.socialPostType,
                callToActionText: postData.ctaText,
                callToActionType: postData.ctaType,
                creativeFileName: postData.creativeFileName,
                creativeFileType: postData.creativeFileType,
              }
            };
          }
        }

        // Fallback: return basic data if specific format data not found
        console.log(`[getAdDataByCreativeId] No specific format data found, returning basic data for creative: ${baseData.creativeId}`);
        return {
          adFormat: input.adFormat,
          creativeId: baseData.creativeId,
          creativeUrn: baseData.creativeUrn,
          segmentId: baseData.segmentId,
          valueProp: baseData.valueProp,
          title: `${baseData.valueProp} (${baseData.creativeType})`,
          description: "Ad content not available",
          callToAction: "Learn More",
          adSpecificData: {
            creativeType: baseData.creativeType,
            creativeStatus: baseData.creativeStatus,
          }
        };

      } catch (error) {
        console.error(`[getAdDataByCreativeId] Error fetching ad data:`, error);
        return null;
      }
    }),
};