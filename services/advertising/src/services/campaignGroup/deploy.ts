import type { Readable } from "stream";

import { segmentService } from "@kalos/core/services/segments";
import { adCopyHandlers } from "@kalos/database/handlers/adCopy";
import { adCreativeHandlers } from "@kalos/database/handlers/adCreative";
import { adFormHandlers } from "@kalos/database/handlers/adForm";
import { campaignHandlers } from "@kalos/database/handlers/campaign";
import { campaignGroupHandlers } from "@kalos/database/handlers/campaignGroup";
import { campaignGroupAdFormHandlers } from "@kalos/database/handlers/campaignGroupAdForm";
import { campaignGroupSegmentHandlers } from "@kalos/database/handlers/campaignGroupSegment";
import { campaignLinkedInAudienceMatchingSegmentHandlers } from "@kalos/database/handlers/campaignLinkedInAudienceMatchingSegment";
import { linkedInAdHanders } from "@kalos/database/handlers/linkedInAd";
import { linkedInAdDeploymentHandlers } from "@kalos/database/handlers/linkedInAdDeployment";
import { getLinkedInApiClientFromOrganizationId } from "@kalos/linkedin-api";

import { inngest } from "../../inngest/client";
import { getAdAccount } from "../adAccount/getAdAccount";
import { getAdCreativeFile } from "../adAsset/getAdCreative";
import {
  getAudienceTargets,
  getCampaignsForCampaignGroupSegment,
} from "../campaign/getCampaigns";
import { getCampaignGroup } from "./getCampaignGroup";

export async function deploy(
  adAccountId: string,
  campaignGroupId: string,
  destinationUrls: { campaignGroupSegmentId: string; url: string }[],
  organizationId: number,
  campaignGroupBudget: number,
  campaignGroupSegmentBudgets: Record<string, number>,
  manualBidding = true,
  unitCost = 2, // LinkedIn minimum unit cost for manual bidding
) {
  const client = await getLinkedInApiClientFromOrganizationId(organizationId);
  if (!client) {
    throw new Error("No client found");
  }
  const adAccount = await getAdAccount(adAccountId);
  if (!adAccount) {
    throw new Error("Ad account not found");
  }
  const campaignGroup = await getCampaignGroup(campaignGroupId);
  if (!campaignGroup) {
    throw new Error("Campaign group not found");
  }

  for (const destinationUrl of destinationUrls) {
    await campaignGroupSegmentHandlers.update.destinationUrlForCampaignGroupSegment(
      destinationUrl.campaignGroupSegmentId,
      destinationUrl.url,
    );
  }

  let adFormUrn: string | undefined;
  if (campaignGroup.objectiveType === "LEAD_GENERATION") {
    const campaignGroupAdForm =
      await campaignGroupAdFormHandlers.select.one.byCampaignGroupId(
        campaignGroupId,
      );
    if (!campaignGroupAdForm) {
      throw new Error("Campaign group ad form not found");
    }
    const adForm = await adFormHandlers.select.one.byId(
      campaignGroupAdForm.adFormId,
    );
    if (!adForm?.linkedInUrn) {
      throw new Error("Ad form not found");
    }
    adFormUrn = adForm.linkedInUrn.toString();
  }

  const startDate =
    campaignGroup.startDate < new Date(Date.now())
      ? new Date(Date.now())
      : campaignGroup.startDate;

  const endDate =
    new Date(startDate.getTime() + 24 * 60 * 60 * 1000) > campaignGroup.endDate
      ? new Date(startDate.getTime() + 24 * 60 * 60 * 1000)
      : campaignGroup.endDate;

  const linkedInCampaignGroup = await client.createCampaignGroup({
    adAccount: adAccount.linkedInUrn.toString(),
    name: campaignGroup.title,
    status: "ACTIVE",
    startDate: startDate,
    endDate: endDate,
    // Objective type applies to all campaigns in the campaign group
    objectiveType: campaignGroup.objectiveType as
      | "BRAND_AWARENESS"
      | "ENGAGEMENT"
      | "LEAD_GENERATION"
      | "WEBSITE_CONVERSIONS"
      | "WEBSITE_VISIT"
      | "VIDEO_VIEWS"
      | null,
  });

  await campaignGroupHandlers.update.one(campaignGroupId, {
    adAccountId: adAccountId,
    organizationId: campaignGroup.organizationId,
    title: campaignGroup.title,
    startDate: startDate,
    endDate: endDate,
    budget: campaignGroupBudget,
    adFormat: campaignGroup.adFormat,
    linkedInUrn: linkedInCampaignGroup,
    linkedInStatus: "ACTIVE",
  });

  for (const [id, budget] of Object.entries(campaignGroupSegmentBudgets)) {
    await campaignGroupSegmentHandlers.update.budgetForCampaignGroupSegment(
      id,
      budget,
    );
  }

  const adCreative =
    await adCreativeHandlers.select.one.byCampaignGroupId(campaignGroupId);

  if (!adCreative) {
    console.log(campaignGroupId);
    throw new Error("Ad creative not found");
  }

  const file = await getAdCreativeFile(adCreative.adCreativeId);
  const fileBody = file.Body;

  const imageUrn = await client.uploadImage({
    linkedInOrganizationId: adAccount.linkedInOrganizationUrn.toString(),
    body: fileBody as Readable,
    type: "image",
    fileSizeBytes: 1,
  });

  const campaignGroupSegments =
    await campaignGroupSegmentHandlers.select.many.byCampaignGroupId(
      campaignGroupId,
    );
  if (campaignGroupSegments.length === 0) {
    throw new Error("No campaign group segments found");
  }

  for (const campaignGroupSegment of campaignGroupSegments) {
    const destinationUrl = destinationUrls.find(
      (url) => url.campaignGroupSegmentId === campaignGroupSegment.id,
    );
    if (campaignGroup.objectiveType === "WEBSITE_VISIT" && !destinationUrl) {
      throw new Error("Destination URL not found");
    }
    const segment = await segmentService.getSegmentById(
      campaignGroupSegment.segmentId,
    );
    if (!segment) {
      throw new Error("Segment not found");
    }
    const campaignsInSegment = (
      await getCampaignsForCampaignGroupSegment(campaignGroupSegment.id)
    ).filter((campaign) => campaign.enabled);
    const valuePropsInSegment =
      await linkedInAdHanders.select.many.byCampaignGroupSegmentId(
        campaignGroupSegment.id,
      );
    for (const campaignInSegment of campaignsInSegment) {
      const targetsFromDb = await getAudienceTargets(campaignInSegment.id);
      console.log(targetsFromDb);
      const targetsByGroup = targetsFromDb.reduce<
        Record<
          string,
          { audienceTargetId: string; entityUrn: string; facetUrn: string }[]
        >
      >((agg, target) => {
        let curr = agg[target.campaignAudienceTargetGroupId];
        if (!curr) {
          curr = [];
        }
        curr.push({
          audienceTargetId: target.audienceTargetId,
          entityUrn: target.entityUrn,
          facetUrn: target.facetUrn,
        });
        agg[target.campaignAudienceTargetGroupId] = curr;
        return agg;
      }, {});
      const targetGroups = Object.entries(targetsByGroup).map(
        ([groupId, targets]) => targets,
      );
      const targetsFinal: {
        and: {
          or: Record<string, string[]>;
        }[];
      } = {
        and: [],
      };
      targetsFinal.and.push({
        or: {
          "urn:li:adTargetingFacet:interfaceLocales": ["urn:li:locale:en_US"],
        },
      });
      for (const targets of targetGroups) {
        const curr = targets.reduce<Record<string, string[]>>((agg, target) => {
          const curr = agg[target.facetUrn] ?? [];
          curr.push(target.entityUrn);
          agg[target.facetUrn] = curr;
          return agg;
        }, {});
        targetsFinal.and.push({
          or: curr,
        });
      }
      // If there's retargeting audiences, add them to the targets

      const matchedAudienceSegments =
        await campaignLinkedInAudienceMatchingSegmentHandlers.select.many.byCampaignId(
          campaignInSegment.id,
        );
      const dynamicTargets = matchedAudienceSegments.filter(
        (segment) => segment.adSegmentType === "RETARGETING",
      );
      if (dynamicTargets.length > 0) {
        targetsFinal.and.push({
          or: {
            "urn:li:adTargetingFacet:dynamicSegments": dynamicTargets.map(
              (segment) => `urn:li:adSegment:${segment.linkedInAdSegmentUrn}`,
            ),
          },
        });
      }
      const matchingSegments = matchedAudienceSegments.filter((segment) =>
        ["MARKET_AUTOMATION", "BULK"].includes(segment.adSegmentType),
      );
      if (matchingSegments.length > 0) {
        targetsFinal.and.push({
          or: {
            "urn:li:adTargetingFacet:matchingSegments": matchingSegments.map(
              (segment) => `urn:li:adSegment:${segment.linkedInAdSegmentUrn}`,
            ),
          },
        });
      }
      console.log(JSON.stringify(targetsFinal, null, 2));

      let suggestedMinBid: number | undefined;
      if (manualBidding) {
        // Get the linkedin bidding recommendation
        const biddingInfo = await client.getSuggestedBidding({
          adAccountUrn: adAccount.linkedInUrn.toString(),
          bidType: "CPC",
          campaignType: "SPONSORED_UPDATES",
          targetingCriteria: {
            include: targetsFinal,
          },
          objectiveType: campaignGroup.objectiveType,
        });
        console.log("biddingInfo", JSON.stringify(biddingInfo, null, 2));
        if (biddingInfo.elements[0]?.suggestedBid.min.amount) {
          suggestedMinBid = Math.max(
            parseFloat(biddingInfo.elements[0]?.suggestedBid.min.amount) - 5,
            parseFloat(biddingInfo.elements[0]?.bidLimits.min.amount ?? "2"),
          );
        }
        console.log("suggestedMinBid", suggestedMinBid);
      }

      const currCampaign = await client.createCampaign({
        adAccount: adAccount.linkedInUrn.toString(),
        campaignGroupUrn: linkedInCampaignGroup,
        budgetType: "TOTAL",
        budget: campaignGroupSegment.budget,
        name: `${segment.name} - ${campaignInSegment.description.toLowerCase().includes("function") ? "Function" : campaignInSegment.description.toLowerCase().includes("title") ? "Title" : campaignInSegment.description}`,
        startDate: startDate,
        endDate: endDate,
        audienceTargets: {
          include: targetsFinal,
        },
        manualBidding: manualBidding,
        unitCost: suggestedMinBid ?? unitCost,
      });
      await campaignHandlers.update.one(campaignInSegment.id, {
        description: campaignInSegment.description,
        campaignGroupSegmentId: campaignInSegment.campaignGroupSegmentId,
        linkedInStatus: "PAUSED",
        linkedInUrn: currCampaign,
      });

      for (const valueProp of valuePropsInSegment) {
        const adCopy = await adCopyHandlers.select.one.firstByValuePropId(
          valueProp.id,
        );
        if (!adCopy) {
          throw new Error("Ad copy not found");
        }
        const currAd = await client.createInlineCreative({
          adAccount: adAccount.linkedInUrn.toString(),
          campaign: currCampaign,
          imageUrn,
          commentary: adCopy.body,
          headline: adCopy.title,
          linkedInOrgId: adAccount.linkedInOrganizationUrn.toString(),
          ...(destinationUrl && {
            destinationUrl: destinationUrl.url,
          }),
          ...(adFormUrn && {
            adFormUrn: adFormUrn,
          }),
        });
        await linkedInAdDeploymentHandlers.insert.one({
          linkedInAdCopyId: adCopy.id,
          linkedInUrn: currAd.value.creative,
          campaignId: campaignInSegment.id,
          linkedInStatus: "PAUSED",
          adCreativeId: adCreative.adCreativeId,
        });
      }
    }
  }
  await inngest.send({
    name: "linkedin/poll-ad-status",
    data: {
      campaignGroupId: campaignGroupId,
    },
  });
}
