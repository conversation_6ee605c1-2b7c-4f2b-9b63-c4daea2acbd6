CREATE TYPE "public"."linkedin_objective_type_enum" AS ENUM('BRAND_AWARENESS', 'ENGAGEMENT', 'JOB_APPLICANTS', 'LEAD_GENERATION', 'WEBSITE_CONVERSIONS', 'WEBSITE_VISITS', 'VIDEO_VIEWS');--> statement-breakpoint
/* 
    Unfortunately in current drizzle-kit version we can't automatically get name for primary key.
    We are working on making it available!

    Meanwhile you can:
        1. Check pk name in your database, by running
            SELECT constraint_name FROM information_schema.table_constraints
            WHERE table_schema = 'advertising'
                AND table_name = 'linkedin_campaign_group'
                AND constraint_type = 'PRIMARY KEY';
        2. Uncomment code below and paste pk name manually
        
    Hope to release this update as soon as possible
*/

ALTER TABLE "advertising"."linkedin_campaign_group" DROP CONSTRAINT "linkedin_campaign_group_pkey";--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_campaign_group" ALTER COLUMN "linkedin_ad_segment_id" DROP NOT NULL;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_campaign_group" ADD COLUMN "id" varchar(36) PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_campaign_group" ADD COLUMN "linked_in_ad_account_id" varchar(36);--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_campaign_group" ADD COLUMN "created_from_linkedin" boolean DEFAULT false NOT NULL;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_campaign_group" ADD COLUMN "objective_type" "linkedin_objective_type_enum";--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_campaign_group" ADD CONSTRAINT "linkedin_campaign_group_linked_in_ad_account_id_linkedin_ad_account_id_fk" FOREIGN KEY ("linked_in_ad_account_id") REFERENCES "advertising"."linkedin_ad_account"("id") ON DELETE no action ON UPDATE no action;