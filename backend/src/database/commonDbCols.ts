import { sql } from "drizzle-orm";
import { bigint, serial, timestamp, varchar } from "drizzle-orm/pg-core";

export const idCol = serial("id").primaryKey();

export const uuIdCol = varchar("id", { length: 36 }).primaryKey();
export const uuIdColDefault = varchar("id", { length: 36 })
  .default(sql`gen_random_uuid()`)
  .primaryKey();
export const uuIdFk = (colName: string) => varchar(colName, { length: 36 });
export const bigIntFk = (colName: string) =>
  bigint(colName, { mode: "number" });

export const userIdFk = (colName: string) => varchar(colName, { length: 33 });

export const createdAtCol = timestamp("created_at").notNull().defaultNow();
export const updatedAtCol = timestamp("updated_at").$onUpdateFn(
  () => sql`now()`,
);

export const linkedInUrnCol = (name: string) => varchar(name, { length: 255 });
