import {
  boolean,
  integer,
  pgEnum,
  timestamp,
  varchar,
} from "drizzle-orm/pg-core";

import { linkedInUrnCol, uuIdColDefault, uuIdFk } from "../../commonDbCols";
import { adSchema } from "../schemas";
import { linkedInAdAccountTable } from "./linkedInAdAccount.table";
import { linkedInAdSegmentTable } from "./linkedInAdSegment.table";

export const linkedInCampaignGroupStatusEnum = pgEnum(
  "linkedin_campaign_group_status_enum",
  [
    "ACTIVE",
    "PAUSED",
    "ARCHIVED",
    "CANCELLED",
    "DRAFT",
    "PENDING_DELETION",
    "REMOVED",
  ],
);

export const linkedInObjectiveTypeEnum = pgEnum(
  "linkedin_objective_type_enum",
  [
    "BRAND_AWARENESS",
    "ENGAGEMENT",
    "JOB_APPLICANTS",
    "LEAD_GENERATION",
    "WEBSITE_CONVERSIONS",
    "WEBSITE_VISITS",
    "VIDEO_VIEWS",
  ],
);

export const linkedInCampaignGroupTable = adSchema.table(
  "linkedin_campaign_group",
  {
    id: uuIdColDefault,
    linkedInAdSegmentId: uuIdFk("linkedin_ad_segment_id").references(
      () => linkedInAdSegmentTable.id,
    ),
    linkedInAdAccountId: uuIdFk("linked_in_ad_account_id").references(
      () => linkedInAdAccountTable.id,
    ),
    totalBudget: integer("total_budget").notNull(),
    linkedInCampaignGroupUrn: linkedInUrnCol(
      "linkedin_campaign_group_urn",
    ).notNull(),
    name: varchar("name").notNull(),
    startDatetime: timestamp("start_datetime"),
    status: linkedInCampaignGroupStatusEnum("status").notNull(),
    createdFromLinkedIn: boolean("created_from_linkedin")
      .default(false)
      .notNull(),
    objectiveType: linkedInObjectiveTypeEnum("objective_type"),
  },
);
