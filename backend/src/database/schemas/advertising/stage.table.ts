import { integer, pgEnum, varchar } from "drizzle-orm/pg-core";

import { uuIdCol, uuIdFk } from "../../commonDbCols";
import { adSchema } from "../schemas";
import { linkedInAdSegmentTable } from "./linkedInAdSegment.table";

export const stageStatusEnum = pgEnum("stage_status_enum", [
  "NOT_STATED",
  "PROVISIONING",
  "RUNNING",
  "INTERRUPTED",
  "FATAL_PROBLEM",
  "FINISHED",
  "PAUSED",
]);

export const stageTable = adSchema.table("stage", {
  id: uuIdCol,
  linkedInAdSegmentid: uuIdFk("ad_segment_id")
    .notNull()
    .references(() => linkedInAdSegmentTable.id),
  index: integer("index").notNull(),
  stageType: varchar("stage_type", { length: 255 }).notNull(),
  status: stageStatusEnum("stage_status").notNull(),
});
