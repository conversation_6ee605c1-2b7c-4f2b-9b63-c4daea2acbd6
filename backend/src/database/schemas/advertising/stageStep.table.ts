import { pgEnum, pgTable, varchar } from "drizzle-orm/pg-core";

import { uuIdCol, uuIdFk } from "../../commonDbCols";
import { adSchema } from "../schemas";
import { stageTable } from "./stage.table";

export const statusEnum = pgEnum("stage_step_status_enum", [
  "NOT_STATED",
  "PROVISIONING",
  "RUNNING",
  "INTERRUPTED",
  "FATAL_PROBLEM",
  "FINISHING",
  "FINISHED",
  "PAUSED",
]);

export const onStartOutputTypeEnum = pgEnum(
  "stage_step_on_start_output_type_enum",
  ["CALL_STEP", "CALL_END_WORKFLOW", "CALL_END_STEP", "CALL_CONFIG"],
);

export const onEndOutputTypeEnum = pgEnum(
  "stage_step_on_end_output_type_enum",
  ["CALL_STEP", "CALL_END_WORKFLOW", "CALL_END_STEP", "CALL_CONFIG"],
);

export const stageStepTable = adSchema.table("stage_step", {
  id: uuIdCol,
  stageId: uuIdFk("stage_id")
    .notNull()
    .references(() => stageTable.id),
  stepName: varchar("step_name", { length: 255 }).notNull(),
  status: statusEnum("status").notNull(),
  onStartOutputType: onStartOutputTypeEnum("on_start_output_type"),
  onEndOutputType: onEndOutputTypeEnum("on_end_output_type"),
});
