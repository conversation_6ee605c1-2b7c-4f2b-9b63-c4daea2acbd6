import { db, Transaction } from "../../../../database/db";
import { ITransactionManagerService } from "../../application/interfaces/infrastructure/services/transactionManager.service.interface";

export class TransactionManagerService implements ITransactionManagerService {
  public async startTransaction<T>(
    clb: (tx: Transaction) => Promise<T>,
    parent?: Transaction,
  ): Promise<T> {
    const invoker = parent ?? db;
    const result: T = await invoker.transaction(async (tx1) => {
      const res = await clb(tx1);
      return res;
    });

    return result;
  }
}
