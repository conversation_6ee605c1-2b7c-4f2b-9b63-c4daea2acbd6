import { TRPCError } from "@trpc/server";

import { Transaction } from "../../../../../database/dbTransactionType";
import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { AddVariantToRunningTestCommandHandler } from "../../../abTest/internal/commands/addVariantToRunningTest/addVariantToRunningTest.command.handler";
import { AbTestRepository as InternalAbTestRepository } from "../../../abTest/internal/repositories/abTest.repository";
import { AbTestRepository } from "../../../infrastructure/repositories/abTest.repository";
import { AbTestRoundRepository } from "../../../abTest/internal/repositories/abTestRound.repository";
import { VariantValidationService } from "../../../abTest/internal/services/variantValidation.service";
import { VariantAdditionRoundService } from "../../../abTest/internal/services/variantAdditionRound.service";
import { AbTestService } from "../../../domain/services/abTest/abTest.service";
import { LinkedInAdAudienceRepository } from "../../../infrastructure/repositories/linkedInAdAudience.repository";
import { AddAudienceToRunningTestDto } from "../../dtos/controllerDtos/abTest/addAudienceToRunningTest.dto";
import { IAbTestRepository } from "../../interfaces/infrastructure/repositories/abTest.repository.interface";
import { IStageRepository } from "../../interfaces/infrastructure/repositories/stage.repository.interface";
import { ILinkedInAdAudienceRepository } from "../../interfaces/infrastructure/repositories/linkedInAdAudience.repository.interface";
import { ILinkedInCampaignRepositoryInterface } from "../../interfaces/infrastructure/repositories/linkedInCampaign.repository.interface";
import { ILinkedInAdSegmentRepository } from "../../interfaces/infrastructure/repositories/linkedInAdSegment.repository.interface";
import { ILinkedInAdAccountRepository } from "../../interfaces/infrastructure/repositories/linkedInAdAccount.repository.interface";
import { ILinkedInCampaignGroupRepository } from "../../interfaces/infrastructure/repositories/linkedInCampaignGroup.repository.interface";
import { ILinkedInService } from "../../interfaces/infrastructure/services/thirdPartyApis/linkedInApi/linkedIn.service.interface";
import { ILinkedInSponsoredCreativeRepository } from "../../interfaces/infrastructure/repositories/linkedInSponsoredCreative.repository.interface";
import { ILinkedInPostRepositoryInterface } from "../../interfaces/infrastructure/repositories/linkedInPost.repository.interface";
import { AdCopyingService } from "../../../domain/services/adCopying.service";
import { AdCreativeService } from "../../../domain/services/adCreative.service";
import { LinkedInAdProgramCreativeRepository } from "../../../infrastructure/repositories/linkedInAdProgramCreative.repository";
import { SocialPostCopyRepository } from "../../../infrastructure/repositories/socialPostCopy.repository";
import { SocialPostCallToActionCopyRepository } from "../../../infrastructure/repositories/socialPostCallToActionCopy.repository";
import { AdSegmentValuePropRepository } from "../../../infrastructure/repositories/adSegmentValueProp.repository";
import { ConversationCopyRepository } from "../../../infrastructure/repositories/conversationCopy.repository";
import { ConversationCallToActionCopyRepository } from "../../../infrastructure/repositories/conversationCallToActionCopy.repository";
import { ConversationMessageCopyRepository } from "../../../infrastructure/repositories/conversationMessageCopy.repository";
import { ConversationSubjectCopyRepository } from "../../../infrastructure/repositories/conversationSubjectCopy.repository";
import { LinkedInAdProgramRepositoryInterface } from "../../interfaces/infrastructure/repositories/linkedInAdProgram.repository.interface";
import { InngestJobTriggerPublisher } from "../../../../shared/inngestJobTriggerPublisher";
import { advertisingInngestClient } from "../../../utils/advertisingInngestClient";
import { TransactionManagerService } from "../../../../core/infrastructure/services/transcationManager.service";
import { Stage } from "../../../domain/entites/stage";
import { LinkedInSponsoredCreativeService } from "../../../domain/services/linkedInSponsoredCreative.service";

// State tracking for rollback operations
interface ProcessState {
  linkedInCampaignUrn?: string;
  linkedInCampaignId?: string;
  copiedSponsoredCreativeUrns?: string[];
  createdDbRecords?: {
    campaignId?: string;
    sponsoredCreativeIds?: string[];
  };
}

export class AddAudienceToRunningTestUseCase {
  constructor(
    private readonly ctx: {
      stageRepository: IStageRepository;
      adAudienceRepository: ILinkedInAdAudienceRepository;
      linkedInCampaignRepository: ILinkedInCampaignRepositoryInterface;
      linkedInSponsoredCreativeService: LinkedInSponsoredCreativeService;
      linkedInAdSegmentRepository: ILinkedInAdSegmentRepository;
      linkedInAdAccountRepository: ILinkedInAdAccountRepository;
      linkedInCampaignGroupRepository: ILinkedInCampaignGroupRepository;
      linkedInService: ILinkedInService;
      linkedInSponsoredCreativeRepository: ILinkedInSponsoredCreativeRepository;
      linkedInPostRepository: ILinkedInPostRepositoryInterface;
      adCreativeService: AdCreativeService;
      linkedInAdProgramRepository: LinkedInAdProgramRepositoryInterface;
    },
  ) {}

  async execute(
    dto: AddAudienceToRunningTestDto,
    organizationId: number,
  ) {
    const transactionManager = new TransactionManagerService();
    const processState: ProcessState = {};
    
    try {
      // Step 1: Database validations and setup (in a focused transaction)
      const validationResults = await transactionManager.startTransaction(async (tx: ITransaction) => {
        return await this.performDatabaseValidations(dto, organizationId, tx);
      });
      
      const { adAudience, currentBestId, stage } = validationResults;
      
      // Step 2: LinkedIn infrastructure setup (outside of database transactions)
      let newCampaignUrn: string | null = null;
      let needsAdApproval = false;
      
      try {
        newCampaignUrn = await this.ensureLinkedInInfrastructureWithoutTransaction(
          adAudience, 
          organizationId, 
          currentBestId,
          processState
        );
        
        // Step 3: Copy ads from best performer (also outside main transaction)
        if (newCampaignUrn && currentBestId) {
          console.log(`Copying ads from best performer ${currentBestId} to new campaign ${newCampaignUrn}`);
          const copyResult = await this.copyAdsFromBestPerformerWithoutTransaction(
            currentBestId,
            newCampaignUrn,
            organizationId,
            processState
          );
          
          needsAdApproval = copyResult.needsPolling;
        }
      } catch (linkedInError) {
        console.error("LinkedIn infrastructure setup failed:", linkedInError);
        
        // Attempt rollback of LinkedIn resources
        await this.rollbackLinkedInResources(processState, organizationId, dto.newAudienceId);
        
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to set up LinkedIn infrastructure. All resources have been cleaned up.",
          cause: linkedInError,
        });
      }

      // Step 4: If ads were copied, wait for approval before creating AB test rounds
      if (needsAdApproval) {
        console.log("Waiting for ad approval before creating AB test rounds...");
        try {
          await this.waitForAdApprovalForSpecificAudience(dto.newAudienceId, organizationId);
        } catch (approvalError) {
          console.error("Ad approval failed or timed out:", approvalError);
          
          // Rollback LinkedIn resources since they won't be used
          await this.rollbackLinkedInResources(processState, organizationId, dto.newAudienceId);
          
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Ad approval failed or timed out. LinkedIn resources have been cleaned up.",
            cause: approvalError,
          });
        }
      }

      // Step 5: Create the AB test round (in another focused transaction)
      try {
        const result = await transactionManager.startTransaction(async (tx: ITransaction) => {
          const variantValidationService = new VariantValidationService({
            adAudienceRepository: new LinkedInAdAudienceRepository(),
          });
          const variantAdditionRoundService = new VariantAdditionRoundService({
            abTestRoundRepository: new AbTestRoundRepository(),
            jobTriggerPublisher: InngestJobTriggerPublisher(advertisingInngestClient),
          });

          const commandHandler = new AddVariantToRunningTestCommandHandler(
            new InternalAbTestRepository(),
            new AbTestRoundRepository(),
            this.ctx.stageRepository,
            variantValidationService,
            variantAdditionRoundService,
            new AbTestService({ abTestRepository: new AbTestRepository() as IAbTestRepository }),
          );

          return await commandHandler.execute({
            abTestId: dto.stageId,
            abTestType: dto.abTestType,
            newVariantId: dto.newAudienceId,
            organizationId,
            tx,
          });
        });

        if (result.isErr()) {
          const errorMessages = {
            AB_TEST_NOT_FOUND: "AB test not found for this stage",
            AB_TEST_NOT_IN_DESIRED_STATE: "AB test is not in progress or cancelled or completed",
            VARIANT_ALREADY_EXISTS: "This audience is already part of the test",
            VARIANT_NOT_FOUND: "Audience not found",
            VARIANT_NOT_AUTHORIZED: "Audience does not belong to organization",
            VALIDATION_FAILED: "Failed to validate audience",
            FAILED_TO_ADD_VARIANT: "Failed to add audience to test",
            FAILED_TO_CREATE_NEW_TEST: "Failed to create new AB test",
          };

          throw new TRPCError({
            code: "BAD_REQUEST",
            message: errorMessages[result.error.type] || "Unknown error occurred",
          });
        }

        return {
          success: true,
          roundId: result.value.newRoundId,  // Frontend expects 'roundId' not 'newRoundId'
          newRoundId: result.value.newRoundId,  // Keep for backward compatibility
          currentLeaderId: result.value.currentLeaderId,
          wasQueued: result.value.wasQueued,
          isNewTest: result.value.isNewTest,
          message: needsAdApproval
            ? (result.value.isNewTest 
                ? "New AB test created for campaign with audience. Ads approved and test rounds ready."
                : "Audience successfully added to test. Ads approved and test rounds ready.")
            : (result.value.isNewTest 
                ? "New AB test created for campaign with audience"
                : "Audience successfully added to test"),
        };
      } catch (abTestError) {
        console.error("AB test creation failed:", abTestError);
        
        // Rollback LinkedIn resources since AB test creation failed
        await this.rollbackLinkedInResources(processState, organizationId, dto.newAudienceId);
        
        throw abTestError; // Re-throw the original error
      }
    } catch (error) {
      // Final safety net - ensure all LinkedIn resources are cleaned up
      await this.rollbackLinkedInResources(processState, organizationId, dto.newAudienceId);
      throw error;
    }
  }

  /**
   * Rollback LinkedIn resources created during the process
   */
  private async rollbackLinkedInResources(
    processState: ProcessState,
    organizationId: number,
    audienceIdToCleanup?: string,
  ): Promise<void> {
    console.log("Starting LinkedIn resources rollback...", processState);
    
    try {
      // Get ad account for LinkedIn operations
      const adAccounts = await this.ctx.linkedInAdAccountRepository.getForOrganization(organizationId);
      if (!adAccounts || adAccounts.length === 0) {
        console.warn("No ad account found for rollback operations");
        return;
      }
      const adAccount = adAccounts[0]!;

      // 1. Clean up sponsored creatives first (they depend on campaigns)
      if (processState.copiedSponsoredCreativeUrns && processState.copiedSponsoredCreativeUrns.length > 0) {
        console.log(`Rolling back ${processState.copiedSponsoredCreativeUrns.length} sponsored creatives...`);
        
        for (const creativeUrn of processState.copiedSponsoredCreativeUrns) {
          try {
            // Use REMOVED status to properly delete the sponsored creative
            await this.ctx.linkedInService.removeSponsoredCreative({
              adAccountUrn: adAccount.linkedInAdAccountUrn,
              adUrn: creativeUrn,
            });
            
            console.log(`Removed sponsored creative: ${creativeUrn}`);
          } catch (creativeError) {
            console.error(`Failed to remove sponsored creative ${creativeUrn}:`, creativeError);
            
            // Fallback to archiving if removal fails
            try {
              await this.ctx.linkedInService.archiveSponsoredCreative({
                adAccountUrn: adAccount.linkedInAdAccountUrn,
                adUrn: creativeUrn,
              });
              console.log(`Archived sponsored creative as fallback: ${creativeUrn}`);
            } catch (archiveError) {
              console.error(`Failed to archive sponsored creative ${creativeUrn}:`, archiveError);
              // Continue with other creatives
            }
          }
        }
      }

      // 2. Clean up LinkedIn campaign
      if (processState.linkedInCampaignUrn) {
        console.log(`Rolling back LinkedIn campaign: ${processState.linkedInCampaignUrn}`);
        
        try {
          // Use REMOVED status to properly delete the campaign
          await this.ctx.linkedInService.removeCampaign({
            adAccountUrn: adAccount.linkedInAdAccountUrn,
            campaignUrn: processState.linkedInCampaignUrn,
          });
          
          console.log(`Removed LinkedIn campaign: ${processState.linkedInCampaignUrn}`);
        } catch (campaignError) {
          console.error(`Failed to remove LinkedIn campaign ${processState.linkedInCampaignUrn}:`, campaignError);
          
          // Fallback to archiving if removal fails
          try {
            await this.ctx.linkedInService.archiveCampaign({
              adAccountUrn: adAccount.linkedInAdAccountUrn,
              campaignUrn: processState.linkedInCampaignUrn,
            });
            console.log(`Archived LinkedIn campaign as fallback: ${processState.linkedInCampaignUrn}`);
          } catch (archiveError) {
            console.error(`Failed to archive LinkedIn campaign ${processState.linkedInCampaignUrn}:`, archiveError);
          }
        }
      }

      // 3. Update database records to reflect LinkedIn deletion/archival
      if (processState.createdDbRecords) {
        console.log("Updating database records to reflect LinkedIn cleanup...");
        
        const transactionManager = new TransactionManagerService();
        await transactionManager.startTransaction(async (tx: ITransaction) => {
          // Update sponsored creative records to REMOVED
          if (processState.createdDbRecords!.sponsoredCreativeIds) {
            for (const creativeId of processState.createdDbRecords!.sponsoredCreativeIds) {
              try {
                await this.ctx.linkedInSponsoredCreativeRepository.updateStatusById(creativeId, "REMOVED", tx);
                console.log(`Updated sponsored creative to REMOVED: ${creativeId}`);
              } catch (dbError) {
                console.error(`Failed to update sponsored creative status ${creativeId}:`, dbError);
              }
            }
          }

          // Update campaign record to REMOVED
          if (processState.createdDbRecords!.campaignId) {
            try {
              await this.ctx.linkedInCampaignRepository.updateStatusById(processState.createdDbRecords!.campaignId, "REMOVED", tx);
              console.log(`Updated campaign to REMOVED: ${processState.createdDbRecords!.campaignId}`);
            } catch (dbError) {
              console.error(`Failed to update campaign status ${processState.createdDbRecords!.campaignId}:`, dbError);
            }
          }

          // Set audience to not be used since the addition failed
          if (audienceIdToCleanup) {
            try {
              await this.ctx.adAudienceRepository.updateToBeUsed(audienceIdToCleanup, false, tx);
              console.log(`Set audience to not be used during cleanup: ${audienceIdToCleanup}`);
            } catch (dbError) {
              console.error(`Failed to update audience toBeUsed flag ${audienceIdToCleanup}:`, dbError);
            }
          }
        });
      }

      console.log("LinkedIn resources rollback completed successfully");
    } catch (rollbackError) {
      console.error("Critical error during LinkedIn resources rollback:", rollbackError);
      // We can't throw here as it might mask the original error
      // Log the error and continue
    }
  }

  private async performDatabaseValidations(
    dto: AddAudienceToRunningTestDto,
    organizationId: number,
    tx: ITransaction,
  ): Promise<{ adAudience: any; currentBestId: string; stage: Stage }> {
    // Check that the stage exists and is in RUNNING status
    const stage = await this.ctx.stageRepository.getStage(dto.stageId, tx);

    if (!stage) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Stage not found",
      });
    }

    if (stage.status !== "RUNNING") {
      throw new TRPCError({
        code: "BAD_REQUEST",
        message: "Stage is not currently running",
      });
    }

    // Validate that this is an audience stage
    if (stage.stageType !== "audienceTest") {
      throw new TRPCError({
        code: "BAD_REQUEST",
        message: "Can only add audiences to audience test stages",
      });
    }

    // Validate that the abTestType matches the stage type
    if (dto.abTestType !== "audience") {
      throw new TRPCError({
        code: "BAD_REQUEST",
        message: "AB test type must be 'audience' for audience stages",
      });
    }

    // Get the audience to ensure it exists and belongs to the organization
    const adAudience = await this.ctx.adAudienceRepository.getOne(dto.newAudienceId, tx);
    if (!adAudience) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Audience not found",
      });
    }

    // Verify audience belongs to organization
    const audienceBelongsToOrg = await this.ctx.adAudienceRepository.checkIfAudienceExistsForOrganization(
      organizationId,
      dto.newAudienceId,
      tx,
    );
    
    if (!audienceBelongsToOrg) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "Audience does not belong to organization",
      });
    }

    // Determine the current best performer ID before creating the round
    const abTestRepository = new AbTestRepository();
    const abTestRoundRepository = new AbTestRoundRepository();
    
    const abTest = await abTestRepository.getOne(
      dto.stageId,
      dto.abTestType,
      tx as Transaction,
    );

    if (!abTest) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "AB test not found for this stage",
      });
    }

    if (abTest.status !== "IN_PROGRESS" && abTest.status !== "CANCELLED" && abTest.status !== "COMPLETED") {
      throw new TRPCError({
        code: "BAD_REQUEST",
        message: "AB test is not in progress or cancelled or completed",
      });
    }

    // Get all rounds for this AB test to determine current best performer
    const rounds = await abTestRoundRepository.getAllForAbTest(
      dto.stageId,
      dto.abTestType,
      tx as Transaction,
    );

    // Check if audience already exists in the test
    const existingVariants = new Set<string>();
    rounds.forEach(round => {
      existingVariants.add(round.currentBestId);
      existingVariants.add(round.contenderId);
    });

    if (existingVariants.has(dto.newAudienceId)) {
      throw new TRPCError({
        code: "BAD_REQUEST",
        message: "This audience is already part of the test",
      });
    }

    // Determine the current best performer (same logic as command handler)
    let currentBestId: string;
    const runningRounds = rounds.filter(round => round.status === "IN_PROGRESS");
    const completedRounds = rounds
      .filter(round => round.status === "COMPLETED")
      .sort((a, b) => a.roundIndex - b.roundIndex);

    if (runningRounds.length > 0) {
      // There's a round currently running - use the current best from the running round
      const runningRound = runningRounds[0];
      if (!runningRound) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to determine current best performer",
        });
      }
      currentBestId = runningRound.currentBestId;
    } else if (completedRounds.length === 0) {
      // No rounds completed yet and none running, use the initial best (first variant from first round)
      const firstRound = rounds[0];
      if (!firstRound) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to determine current best performer",
        });
      }
      currentBestId = firstRound.currentBestId;
    } else {
      // Use winner of the most recent completed round
      const lastCompletedRound = completedRounds[completedRounds.length - 1];
      if (!lastCompletedRound) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to determine current best performer",
        });
      }
      currentBestId = lastCompletedRound.winner === "CURRENT_BEST" 
        ? lastCompletedRound.currentBestId 
        : lastCompletedRound.contenderId;
    }

    return { adAudience, currentBestId, stage };
  }

  private async ensureLinkedInInfrastructureWithoutTransaction(
    adAudience: any,
    organizationId: number,
    currentBestPerformerId: string,
    processState: ProcessState,
  ): Promise<string | null> {
    // This method has been refactored to not hold a transaction open during LinkedIn API calls.
    // It is now a multi-step process:
    // 1. Read necessary data from the database in a short transaction.
    // 2. Perform all LinkedIn API calls outside of the transaction.
    // 3. Write the results back to the database in another short transaction.

    const transactionManager = new TransactionManagerService();

    // Step 1: Read data within a transaction
    const initialData = await transactionManager.startTransaction(async (tx: ITransaction) => {
      const existingCampaign = await this.ctx.linkedInCampaignRepository.getOneById(
        adAudience.id,
        tx,
      );
      if (existingCampaign) {
        return { hasExistingCampaign: true, campaignUrn: existingCampaign.linkedInCampaignUrn };
      }

      const adSegment = await this.ctx.linkedInAdSegmentRepository.getOne(
        adAudience.linkedInAdSegmentId,
        tx,
      );
      if (!adSegment) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Ad segment not found for audience",
        });
      }

      const adProgram = await this.ctx.linkedInAdProgramRepository.getOne(
        adSegment.linkedInAdProgramId,
      );
      if (!adProgram) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Ad program not found for ad segment",
        });
      }

      const adAccounts = await this.ctx.linkedInAdAccountRepository.getForOrganization(
        organizationId,
        tx,
      );
      if (!adAccounts || adAccounts.length === 0) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "LinkedIn ad account not found for organization",
        });
      }

      const linkedInCampaignGroup =
        await this.ctx.linkedInCampaignGroupRepository.getOneByAdSegmentId(
          adAudience.linkedInAdSegmentId,
          tx,
        );
      if (!linkedInCampaignGroup) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Campaign group information not found for ad segment",
        });
      }

      let bestPerformerCampaign = null;
      if (currentBestPerformerId && currentBestPerformerId.trim() !== "") {
        bestPerformerCampaign = await this.ctx.linkedInCampaignRepository.getOneById(
          currentBestPerformerId,
          tx,
        );
      }
      
      return {
        hasExistingCampaign: false,
        adSegment,
        adProgram,
        adAccount: adAccounts[0]!,
        linkedInCampaignGroup,
        bestPerformerCampaign,
      };
    });

    if (initialData.hasExistingCampaign) {
      return initialData.campaignUrn!;
    }
    
    const { adSegment, adProgram, adAccount, linkedInCampaignGroup, bestPerformerCampaign } = initialData;

    if (!adSegment || !adProgram || !adAccount || !linkedInCampaignGroup) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to retrieve necessary data for LinkedIn infrastructure setup.",
      });
    }

    // Step 2: Perform LinkedIn API calls (outside of transaction)
    
    let campaignProperties: {
      unitCost: number;
      manualBidding: boolean;
      objectiveType: "BRAND_AWARENESS" | "ENGAGEMENT" | "LEAD_GENERATION" | "WEBSITE_CONVERSIONS" | "WEBSITE_VISIT" | "VIDEO_VIEWS";
    } = {
      unitCost: 2,
      manualBidding: true,
      objectiveType: "BRAND_AWARENESS",
    };

    let locationTargeting = [
      {
        or: [
          {
            facetUrn: "urn:li:adTargetingFacet:locations",
            facetName: "Locations",
            facetEntites: [
              {
                facetUrn: "urn:li:adTargetingFacet:locations",
                entityUrn: "urn:li:geo:*********",
                entityName: "United States"
              }
            ]
          }
        ]
      }
    ];

    try {
      if (bestPerformerCampaign) {
        const linkedInCampaignDetails = await this.ctx.linkedInService.getCampaign({
          adAccountUrn: adAccount.linkedInAdAccountUrn,
          campaignUrn: bestPerformerCampaign.linkedInCampaignUrn,
        });

        campaignProperties = {
          unitCost: parseInt(linkedInCampaignDetails.unitCost.amount),
          manualBidding: !!linkedInCampaignDetails.unitCost,
          objectiveType: linkedInCampaignDetails.objectiveType as any,
        };

        if ((linkedInCampaignDetails as any).targeting?.include?.and) {
          const locationTargets = (linkedInCampaignDetails as any).targeting.include.and.filter((target: any) =>
            target.or && target.or.some((item: any) =>
              item.facetUrn === "urn:li:adTargetingFacet:locations"
            )
          );
          if (locationTargets.length > 0) {
            locationTargeting = locationTargets;
          }
        }
      }
    } catch (error) {
      console.error("Failed to get campaign properties from best performer, using defaults:", error);
    }
    
    let campaignType: "SPONSORED_UPDATES" | "SPONSORED_INMAILS" = "SPONSORED_UPDATES";
    if (adProgram.adFormat.type === "SPONSORED_INMAIL") {
      campaignType = "SPONSORED_INMAILS";
    }

    const startDate = new Date();
    let endDate: Date | undefined;
    if (adProgram.type === "EVENT_DRIVEN") {
      endDate = new Date();
      endDate.setMonth(endDate.getMonth() + 1);
    }

    const audienceTargetsWithDefaults = {
      ...adAudience.audienceTargetCriteria,
      include: {
        and: [
          ...adAudience.audienceTargetCriteria.include.and,
          ...locationTargeting
        ]
      }
    };

    const facetNames: string[] = [];
    for (const orGroup of adAudience.audienceTargetCriteria.include.and) {
      for (const eachFacet of orGroup.or) {
        facetNames.push(eachFacet.facetName);
      }
    }
    const campaignName = `${facetNames.join(", ")}`;

    const minDailyBudget = 10;
    const baseBudgetPerCampaign = Math.ceil(linkedInCampaignGroup.totalBudget / 2);
    let newCampaignBudget = baseBudgetPerCampaign;
    let newCampaignBudgetType: "TOTAL" | "DAILY" = "TOTAL";
    
    if (adProgram.type === "EVERGREEN") {
      newCampaignBudgetType = "DAILY";
      newCampaignBudget = Math.ceil(baseBudgetPerCampaign / 30);
      if (newCampaignBudget < minDailyBudget) {
        throw new Error(`Daily budget is too low for EVERGREEN campaign: ${newCampaignBudget} < ${minDailyBudget} for campaign ${campaignName}`);
      }
    }
    
    const newCampaignUrn = await this.ctx.linkedInService.createCampaign({
      adAccountUrn: adAccount.linkedInAdAccountUrn,
      campaignGroupUrn: linkedInCampaignGroup.linkedInCampaignGroupUrn,
      budgetType: newCampaignBudgetType,
      budget: newCampaignBudget,
      name: campaignName,
      startDate: startDate,
      endDate: endDate,
      audienceTargets: audienceTargetsWithDefaults,
      unitCost: campaignProperties.unitCost,
      manualBidding: campaignProperties.manualBidding,
      objectiveType: campaignProperties.objectiveType,
      type: campaignType,
    });
    
    // Track the created campaign for rollback
    processState.linkedInCampaignUrn = newCampaignUrn;
    
    // Step 3: Write results to DB in a new transaction
    return await transactionManager.startTransaction(async (tx: ITransaction) => {
      await this.ctx.linkedInCampaignRepository.createOne(
        {
          linkedInAudienceId: adAudience.id,
          linkedInCampaignUrn: newCampaignUrn,
          totalBudget: baseBudgetPerCampaign,
          status: "ACTIVE",
        },
        tx,
      );

      // Track the DB record for rollback
      if (!processState.createdDbRecords) {
        processState.createdDbRecords = {};
      }
      processState.createdDbRecords.campaignId = adAudience.id;

      const campaigns = await this.ctx.linkedInCampaignRepository.getManyForAdSegment(
        adAudience.linkedInAdSegmentId,
        tx,
      );
      
      let linkedInBudgetForAllCampaigns = baseBudgetPerCampaign;
      let linkedInBudgetType: "TOTAL" | "DAILY" = "TOTAL";
      if (adProgram.type === "EVERGREEN") {
        linkedInBudgetType = "DAILY";
        linkedInBudgetForAllCampaigns = Math.ceil(baseBudgetPerCampaign / 30);
      }
      
      for (const campaign of campaigns) {
        if (campaign.totalBudget !== baseBudgetPerCampaign) {
          await this.ctx.linkedInCampaignRepository.updateTotalBudgetById(
            campaign.linkedInAudienceId,
            baseBudgetPerCampaign,
            tx,
          );
          
          try {
            const budgetPayload: any = {
              adAccountUrn: adAccount.linkedInAdAccountUrn,
              campaignUrn: campaign.linkedInCampaignUrn,
            };
            if (linkedInBudgetType === "DAILY") {
              budgetPayload.dailyBudget = linkedInBudgetForAllCampaigns;
            } else {
              budgetPayload.budget = linkedInBudgetForAllCampaigns;
            }
            await this.ctx.linkedInService.updateCampaignBudget(budgetPayload);
          } catch (linkedInErr) {
            console.error(
              `Failed to update LinkedIn budget for campaign ${campaign.linkedInCampaignUrn}:`,
              linkedInErr,
            );
          }
        }
      }
      
      return newCampaignUrn;
    });
  }

  private async waitForAdApprovalForSpecificAudience(
    audienceId: string,
    organizationId: number,
  ): Promise<void> {
    const maxRetries = 120; // 10 hours with 5-minute intervals
    let retryCount = 0;
    
    while (retryCount < maxRetries) {
      const status = await this.checkAdApprovalStatusForSpecificAudience(audienceId, organizationId);
      
      if (status === "APPROVED") {
        console.log("All ads approved for new audience, proceeding with AB test round creation");
        return;
      }
      
      if (status === "REJECTED") {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "One or more ads were rejected by LinkedIn for the new audience",
        });
      }
      
      // Status is PENDING, wait and retry
      console.log(`Ads still pending approval for new audience ${audienceId}, waiting... (${retryCount + 1}/${maxRetries})`);
      await new Promise(resolve => setTimeout(resolve, 5 * 60 * 1000)); // 5 minutes
      retryCount++;
    }
    
    throw new TRPCError({
      code: "INTERNAL_SERVER_ERROR", 
      message: "Timeout waiting for ad approval for new audience",
    });
  }

  private async checkAdApprovalStatusForSpecificAudience(
    audienceId: string,
    organizationId: number,
  ): Promise<"APPROVED" | "REJECTED" | "PENDING"> {
    const transactionManager = new TransactionManagerService();
    
    // Get all necessary data in a single, short transaction
    const { adAccount, creativesToCheck } = await transactionManager.startTransaction(async (tx: ITransaction) => {
      // Get ad account
      const adAccounts = await this.ctx.linkedInAdAccountRepository.getForOrganization(organizationId, tx);
      if (!adAccounts || adAccounts.length === 0) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "LinkedIn ad account not found for organization",
        });
      }
      const adAccount = adAccounts[0]!;

      // Check status of sponsored creatives only for the specific audience
      const sponsoredCreatives = await this.ctx.linkedInSponsoredCreativeRepository.getAllForCampaign(
        audienceId,
        tx,
      );
      
      // Prepare data for API calls (outside of transaction)
      const creativesToCheck = sponsoredCreatives.map(creative => ({
        urn: creative.linkedInSponseredCreativeUrn,
        id: creative.id,
      }));
      
      return { adAccount, creativesToCheck };
    });
    
      // Process LinkedIn API calls outside of database transaction to avoid timeout
  for (const creative of creativesToCheck) {
    try {
      const status = await this.ctx.linkedInService.getSponsoredCreativeStatus({
        adAccountUrn: adAccount.linkedInAdAccountUrn,
        adUrn: creative.urn,
      });
      
      if (!status || !status.review || !status.review.status) {
        console.warn(`Sponsored creative ${creative.urn} has incomplete status response, treating as PENDING`);
        return "PENDING";
      }

      console.log(`Sponsored creative ${creative.urn} is ${status.review.status}`);

      if (status.review.status === "REJECTED") {
        return "REJECTED";
      }

      if (status.review.status !== "APPROVED") {
        return "PENDING";
      }
    } catch (error) {
      console.error(`Error checking status for creative ${creative.urn}:`, error);
      throw error;
    }
  }
    
    return "APPROVED";
  }

  private async copyAdsFromBestPerformerWithoutTransaction(
    sourceCampaignId: string,
    destinationCampaignUrn: string,
    organizationId: number,
    processState: ProcessState,
  ): Promise<{ needsPolling: boolean; adSegmentId?: string }> {
    const adCopyingService = new AdCopyingService({
      linkedInSponsoredCreativeRepository: this.ctx.linkedInSponsoredCreativeRepository,
      linkedInSponsoredCreativeService: this.ctx.linkedInSponsoredCreativeService,
      linkedInPostRepository: this.ctx.linkedInPostRepository,
      linkedInService: this.ctx.linkedInService,
      linkedInAdAccountRepository: this.ctx.linkedInAdAccountRepository,
      adCreativeService: this.ctx.adCreativeService,
      linkedInCampaignRepository: this.ctx.linkedInCampaignRepository,
      // Add the missing repository dependencies
      linkedInAdProgramCreativeRepository: new LinkedInAdProgramCreativeRepository(),
      socialPostCopyRepository: new SocialPostCopyRepository(),
      socialPostCallToActionCopyRepository: new SocialPostCallToActionCopyRepository(),
      adSegmentValuePropRepository: new AdSegmentValuePropRepository(),
      conversationCopyRepository: new ConversationCopyRepository(),
      conversationCallToActionCopyRepository: new ConversationCallToActionCopyRepository(),
      conversationMessageCopyRepository: new ConversationMessageCopyRepository(),
      conversationSubjectCopyRepository: new ConversationSubjectCopyRepository(),
      linkedInAdSegmentRepository: this.ctx.linkedInAdSegmentRepository,
      linkedInAdAudienceRepository: new LinkedInAdAudienceRepository(),
    });

    const copyResult = await adCopyingService.copySponsoredCreativeAds(
      sourceCampaignId,
      destinationCampaignUrn,
      organizationId,
      new TransactionManagerService(),
    );

    if (copyResult.success) {
      console.log(`Successfully copied ${copyResult.copiedAdsCount} ads to new campaign`);
      if (copyResult.errors.length > 0) {
        console.warn("Some ads had issues during copying:", copyResult.errors);
      }
      
      // Track the copied creatives for rollback
      if (copyResult.copiedCreativeUrns) {
        processState.copiedSponsoredCreativeUrns = copyResult.copiedCreativeUrns;
      }
      
      // Return polling info - copied ads need approval before test rounds can start
      return { 
        needsPolling: copyResult.copiedAdsCount > 0,
        adSegmentId: undefined // We'd need to pass this if available
      };
    } else {
      console.error("Failed to copy ads:", copyResult.errors);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to copy ads",
        cause: copyResult.errors,
      });
    }
  }
} 