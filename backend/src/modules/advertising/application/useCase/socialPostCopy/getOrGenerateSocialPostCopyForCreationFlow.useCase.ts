import { ILLMCompletionService } from "../../../../core/application/interfaces/infrastructure/services/llmCompletionService.interface";
import { IPromptStorageService } from "../../../../core/application/interfaces/infrastructure/services/promptStorage.service.interface";
import { SegmentService } from "../../../../core/domain/services/segment.service";
import { segmentRepository } from "../../../../core/infrastructure/repositories/segment.repository";
import { createUuid } from "../../../../core/utils/uuid";
import { AdSegmentService } from "../../../domain/services/adSegment.service";
import { AdSegmentValuePropService } from "../../../domain/services/adSegmentValueProp.service";
import { ExampleSocialPostService } from "../../../domain/services/exampleSocialPost.service";
import { LinkedInAdProgramService } from "../../../domain/services/linkedInAdProgram.service";
import { LinkedInAdSegmentSocialPostBaseCopyService } from "../../../domain/services/linkedInAdSegmentSocialPostBaseCopy.service";
import { PositioningService } from "../../../domain/services/positioning.service";
import { SocialPostAdCopyService } from "../../../domain/services/socialPostAdCopy.service";
import { SocialPostStyleGuideService } from "../../../domain/services/socialPostStyleGuide.service";
import { SocialPostCallToActionCopyRepository } from "../../../infrastructure/repositories/socialPostCallToActionCopy.repository";
import { GetOrGenerateSocialPostCopyForCreationFlowRequestDto } from "../../dtos/controllerDtos/socialPostCopy/getOrGenerateSocialPostCopyForCreationFlow.dto";
import { IAdvertisingLlmCompletionsService } from "../../interfaces/infrastructure/services/advertisingLlmCompletions.service.interface";

export class GetOrGenerateSocialPostCopyForCreationFlowUseCase {
  constructor(
    private readonly ctx: {
      socialPostAdCopyService: SocialPostAdCopyService;
      adSegmentValuePropService: AdSegmentValuePropService;
      llmCompletions: ILLMCompletionService;
      promptStorage: IPromptStorageService;
      advertisingLlmCompletionsService: IAdvertisingLlmCompletionsService;
      adSegmentService: AdSegmentService;
      segmentService: SegmentService;
      positioningService: PositioningService;
      styleGuideService: SocialPostStyleGuideService;
      exampleSocialPostService: ExampleSocialPostService;
      organizationId: number;
      adSegmentSocialPostBaseCopyService: LinkedInAdSegmentSocialPostBaseCopyService;
      adProgramService: LinkedInAdProgramService;
      socialPostCallToActionCopyRepository: SocialPostCallToActionCopyRepository;
    },
  ) { }

  async *execute(
    input: GetOrGenerateSocialPostCopyForCreationFlowRequestDto,
  ): AsyncGenerator<{
    body: string;
    title: string;
    done: boolean;
    leadGenFormUrn: string | null;
  }> {
    console.log("STARTING");
    const socialPostCopy = await this.ctx.socialPostAdCopyService.getOne({
      valuePropId: input.linkedInAdSegmentValuePropId,
      socialPostCopyType: input.socialPostCopyType,
      status: "DRAFT",
    });

    const socialPostCallToAction =
      await this.ctx.socialPostCallToActionCopyRepository.getOne({
        valuePropId: input.linkedInAdSegmentValuePropId,
        socialPostCopyType: "Standard",
        status: "DRAFT",
      });
    if (socialPostCopy && socialPostCallToAction) {
      yield {
        body: socialPostCopy.body,
        title: socialPostCallToAction.callToAction,
        done: true,
        leadGenFormUrn: socialPostCopy.leadGenFormUrn ?? null,
      };
    } else {
      const adSegmentValueProp =
        await this.ctx.adSegmentValuePropService.getOne(
          input.linkedInAdSegmentValuePropId,
          "DRAFT",
        );
      if (!adSegmentValueProp) {
        throw new Error("Ad segment value prop not found");
      }
      const adSegment = await this.ctx.adSegmentService.getOne(
        adSegmentValueProp.linkedInAdSegmentId,
      );
      if (!adSegment) {
        throw new Error("Ad segment not found");
      }
      const segment = await this.ctx.segmentService.getSegmentById(
        adSegment.segmentId,
        {
          segmentRepository: segmentRepository,
        },
      );
      if (!segment) {
        throw new Error("Segment not found");
      }

      const adProgram = await this.ctx.adProgramService.getOne(
        adSegment.linkedInAdProgramId,
      );

      if (!adProgram) {
        throw new Error("Ad program not found");
      }

      const leadGenFormUrn = adProgram.leadGenForm;
      console.log("LEAD GEN FOR URN", leadGenFormUrn);

      const positioning = await this.ctx.positioningService.getOne(
        this.ctx.organizationId,
      );
      const styleGuide = await this.ctx.styleGuideService.getOne(
        this.ctx.organizationId,
      );

      const exampleSocialPost =
        await this.ctx.exampleSocialPostService.getAllForOrganization(
          this.ctx.organizationId,
        );
      const baseCopy =
        await this.ctx.adSegmentSocialPostBaseCopyService.getAdSegmentBaseSocialPostCopy(
          adSegment.id,
        );
      if (!baseCopy) {
        throw new Error("Base copy not found");
      }

      let bodyText = "";

      console.log("GENERATING BODY");

      const body =
        this.ctx.advertisingLlmCompletionsService.generateSocialPostBody(
          {
            adSegmentValueProp: adSegmentValueProp.valueProp,
            baseCopy: baseCopy.baseCopy,
            styleGuide: styleGuide?.content ?? "",
            exampleSocialPost: exampleSocialPost.map((e) => e.content),
            positioning: positioning?.content ?? "",
            adTargeting: {
              jobFunction: segment.jobFunction,
              jobSeniority: segment.jobSeniority,
              verticals: segment.verticals,
              annualRevenueLowBound: segment.annualRevenueLowBound,
              annualRevenueHighBound: segment.annualRevenueHighBound,
              numberOfEmployeesLowBound: segment.numberOfEmployeesLowBound,
              numberOfEmployeesHighBound: segment.numberOfEmployeesHighBound,
            },
          },
          {
            lllmCompletions: this.ctx.llmCompletions,
            promptStorage: this.ctx.promptStorage,
          },
        );
      for await (const each of body) {
        yield {
          body: each,
          title: "",
          done: false,
          leadGenFormUrn: leadGenFormUrn ?? null,
        };
        bodyText += each;
      }
      let titleText = "";
      console.log("GENERATING TITLE");
      const title =
        this.ctx.advertisingLlmCompletionsService.generateSocialPostHeadline(
          {
            baseCopy: baseCopy.baseCopy,
            adSegmentValueProp: adSegmentValueProp.valueProp,
            styleGuide: styleGuide?.content ?? "",
            exampleSocialPost: exampleSocialPost.map((e) => e.content),
            adTargeting: {
              jobFunction: segment.jobFunction,
              jobSeniority: segment.jobSeniority,
              verticals: segment.verticals,
              annualRevenueLowBound: segment.annualRevenueLowBound,
              annualRevenueHighBound: segment.annualRevenueHighBound,
              numberOfEmployeesLowBound: segment.numberOfEmployeesLowBound,
              numberOfEmployeesHighBound: segment.numberOfEmployeesHighBound,
            },
          },
          {
            lllmCompletions: this.ctx.llmCompletions,
            promptStorage: this.ctx.promptStorage,
          },
        );
      for await (const each of title) {
        yield {
          body: "",
          title: each,
          done: false,
          leadGenFormUrn: leadGenFormUrn ?? null,
        };
        titleText += each;
      }
      console.log("CREATING SOCIAL POST COPY");

      await this.ctx.socialPostAdCopyService.createOneOrUpdateOneIfExists({
        linkedInAdSegmentValuePropId: input.linkedInAdSegmentValuePropId,
        socialPostCopyType: input.socialPostCopyType,
        title: titleText,
        body: bodyText,
        leadGenFormUrn: leadGenFormUrn,
        status: "DRAFT",
      });

      await this.ctx.socialPostCallToActionCopyRepository.createOneOrUpdateOneIfExists(
        {
          adSegmentValuePropId: input.linkedInAdSegmentValuePropId,
          callToAction: titleText,
          type: "Standard",
          id: createUuid(),
          status: "DRAFT",
        },
      );

      yield {
        body: "",
        title: "",
        done: true,
        leadGenFormUrn: leadGenFormUrn ?? null,
      };
    }
  }
}
