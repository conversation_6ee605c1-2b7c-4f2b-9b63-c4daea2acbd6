import { createUuid } from "../../../../core/utils/uuid";
import { SocialPostAdCopyService } from "../../../domain/services/socialPostAdCopy.service";
import { SocialPostCallToActionCopyRepository } from "../../../infrastructure/repositories/socialPostCallToActionCopy.repository";
import { UpdateSocialPostCopyRequestDto } from "../../dtos/controllerDtos/socialPostCopy/updateSocialPostCopy.dto";

export class UpdateSocialPostCopyUseCase {
  constructor(
    private readonly socialPostAdCopyService: SocialPostAdCopyService,
    private readonly socialPostCallToActionCopyRepository: SocialPostCallToActionCopyRepository,
  ) { }

  async execute(input: UpdateSocialPostCopyRequestDto) {
    await this.socialPostAdCopyService.createOneOrUpdateOneIfExists({
      linkedInAdSegmentValuePropId: input.linkedInAdSegmentValuePropId,
      socialPostCopyType: input.copyType,
      body: input.body,
      title: input.title,
      status: "DRAFT",
    });
    await this.socialPostCallToActionCopyRepository.createOneOrUpdateOneIfExists(
      {
        adSegmentValuePropId: input.linkedInAdSegmentValuePropId,
        callToAction: input.title,
        type: "Standard",
        id: createUuid(),
        status: "DRAFT",
      },
    );
  }
}
