import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { LinkedInCampaignGroupService } from "../../../domain/services/linkedInCampaignGroup.service";

export class CreateCampaignGroupUseCase {
  constructor(
    private readonly linkedInCampaignGroupService: LinkedInCampaignGroupService,
  ) {}

  async execute(params: {
    name: string;
    status: string;
    campaignGroupUrn: string;
    linkedInAdAccountId: string; // This is passed in but used as linkedInAdSegmentId
    createdFromLinkedIn?: boolean;
    objectiveType?:
      | "BRAND_AWARENESS"
      | "ENGAGEMENT"
      | "JOB_APPLICANTS"
      | "LEAD_GENERATION"
      | "WEBSITE_CONVERSIONS"
      | "WEBSITE_VISITS"
      | "VIDEO_VIEWS"
      | null;
    startDatetime: Date;
    tx?: ITransaction; // Using any for transaction type
  }) {
    try {
      const campaignGroup = await this.linkedInCampaignGroupService.createOne(
        {
          name: params.name,
          status: params.status as
            | "ACTIVE"
            | "PAUSED"
            | "ARCHIVED"
            | "CANCELLED"
            | "DRAFT"
            | "PENDING_DELETION"
            | "REMOVED", // Cast to the appropriate status type
          linkedInCampaignGroupUrn: params.campaignGroupUrn, // Corrected property name
          createdFromLinkedIn: params.createdFromLinkedIn ?? false,
          linkedInAdSegmentId: null,
          objectiveType: params.objectiveType ?? null,
          linkedInAdAccountId: params.linkedInAdAccountId,
          totalBudget: 0, // Required field with default value
          startDatetime: params.startDatetime,
        },
        params.tx,
      );

      return {
        success: true,
        campaignGroup,
      };
    } catch (error) {
      console.error("Error creating campaign group:", error);
      return {
        success: false,
        error:
          error instanceof Error ? error.message : "Unknown error occurred",
      };
    }
  }
}
