import { LinkedInCampaignGroupService } from "../../../domain/services/linkedInCampaignGroup.service";
import { LinkedInService } from "../../../infrastructure/services/linkedIn.service";
import { GetCampaignGroupBatchAnalyticsDto } from "../../dtos/controllerDtos/linkedInCampaignGroup/linkedinCampaignGroup.dto";
import { ILinkedInAdAccountRepository } from "../../interfaces/infrastructure/repositories/linkedInAdAccount.repository.interface";
import { ILinkedInCampaignGroupRepository } from "../../interfaces/infrastructure/repositories/linkedInCampaignGroup.repository.interface";

export type SegmentAnalytics = {
  name: string;
  totalEngagements: number;
  oneClickLeads: number;
  costInUsd: number;
};

export type CampaignPreview = {
  id: string;
  name: string;
  status?: string;
};

export type AdCreativePreview = {
  id: string;
  name?: string;
  type?: string;
  campaignId: string;
  campaignName?: string;
};

export type CampaignGroupPreviewResult = {
  campaignGroup: {
    id: string;
    name: string;
    status?: string;
  };
  campaignsCount: number;
  campaigns: CampaignPreview[];
  adCreativesCount: number;
  adCreatives: AdCreativePreview[];
  hasData: boolean;
  error?: string;
};

export class CampaignGroupIngestionUseCase {
  constructor(
    private readonly ctx: {
      linkedInCampaignGroupService: LinkedInCampaignGroupService;
      linkedInService: LinkedInService;
    },
  ) {}

  async execute(params: {
    campaignGroupId: string;
    linkedInAdAccountId: string;
    storeInDatabase?: boolean;
  }): Promise<CampaignGroupPreviewResult> {
    try {
      //Use campaigngroupid to get campaign group- name, details
      const linkedInCampaignGroup =
        await this.ctx.linkedInService.getCampaignGroup({
          linkedInAdAccountId: params.linkedInAdAccountId,
          linkedInCampaignGroupId: params.campaignGroupId,
        });

      if (!linkedInCampaignGroup) {
        console.log("CAMPAIGN GROUP NULL ");
        return {
          campaignGroup: {
            id: params.campaignGroupId,
            name: "Unknown",
          },
          campaignsCount: 0,
          campaigns: [],
          adCreativesCount: 0,
          adCreatives: [],
          hasData: false,
          error: "Campaign group not found",
        };
      }

      // use campaign groupid to get campaigns
      const campaigns =
        await this.ctx.linkedInService.getCampaignsByCampaignGroupId({
          linkedInAdAccountId: params.linkedInAdAccountId,
          campaignGroupId: params.campaignGroupId,
        });
      console.log("Ad Campaigns", campaigns);

      if (!campaigns) {
        return {
          campaignGroup: {
            id: params.campaignGroupId,
            name: linkedInCampaignGroup.name || "Unknown",
            status: linkedInCampaignGroup.status,
          },
          campaignsCount: 0,
          campaigns: [],
          adCreativesCount: 0,
          adCreatives: [],
          hasData: false,
          error: "No campaigns data available",
        };
      }

      if (!campaigns.elements || campaigns?.elements.length === 0) {
        console.log("NO CAMPAIGNS ");
        return {
          campaignGroup: {
            id: params.campaignGroupId,
            name: linkedInCampaignGroup.name || "Unknown",
            status: linkedInCampaignGroup.status,
          },
          campaignsCount: 0,
          campaigns: [],
          adCreativesCount: 0,
          adCreatives: [],
          hasData: false,
          error: "No campaigns found for this campaign group",
        };
      }

      const campaignIds = [];

      // use campaigns to get ads
      for (const campaign of campaigns?.elements) {
        campaignIds.push(campaign.id.toString());
      }

      console.log("Campaign Group", linkedInCampaignGroup);
      console.log("Campaigns", campaigns);
      const adCreatives =
        await this.ctx.linkedInService.getAdCreativesByCampaigns({
          linkedInAdAccountId: params.linkedInAdAccountId,
          campaignIds: campaignIds,
        });

      const adCreativesCount = adCreatives.elements?.length || 0;

      // Only store in database if explicitly requested
      if (params.storeInDatabase) {
        // TODO: Store data in db
        // campaign group
        // campaigns
        // ads
        console.log("Storing data in database...");
      }

      // Extract campaign information
      const campaignPreviews = campaigns.elements.map((campaign) => ({
        id: campaign.id.toString(),
        name: campaign.name || `Campaign ${campaign.id}`,
        status: campaign.status,
      }));

      // Create a map of campaign IDs to names for reference
      const campaignIdToName = new Map<string, string>();
      campaigns.elements.forEach((campaign) => {
        campaignIdToName.set(
          campaign.id.toString(),
          campaign.name || `Campaign ${campaign.id}`,
        );
      });

      // console.log("Ad Creatives", adCreatives);
      // Extract ad creative information
      const adCreativePreviews = adCreatives.elements.map((adCreative) => {
        // console.log("Ad Creative", adCreative);
        // Determine ad type from content or other properties

        return {
          id: adCreative.id.toString(),
          name: adCreative.name || `Ad ${adCreative.id}`,
          // type: adType,
          campaignId: adCreative.campaign?.toString() || "",
          campaignName: adCreative.campaign
            ? campaignIdToName.get(adCreative.campaign.toString())
            : undefined,
        };
      });

      return {
        campaignGroup: {
          id: params.campaignGroupId,
          name: linkedInCampaignGroup.name || "Unknown",
          status: linkedInCampaignGroup.status,
        },
        campaignsCount: campaigns.elements.length,
        campaigns: campaignPreviews,
        adCreativesCount: adCreativesCount,
        adCreatives: adCreativePreviews,
        hasData: campaigns.elements.length > 0 && adCreativesCount > 0,
      };
    } catch (error) {
      console.error("Error in CampaignGroupIngestionUseCase:", error);
      return {
        campaignGroup: {
          id: params.campaignGroupId,
          name: "Unknown",
        },
        campaignsCount: 0,
        campaigns: [],
        adCreativesCount: 0,
        adCreatives: [],
        hasData: false,
        error:
          error instanceof Error ? error.message : "An unknown error occurred",
      };
    }
  }
}
