import { LinkedInCampaignGroupService } from "../../../domain/services/linkedInCampaignGroup.service";
import { LinkedInService } from "../../../infrastructure/services/linkedIn.service";
import { GetCampaignGroupBatchAnalyticsDto } from "../../dtos/controllerDtos/linkedInCampaignGroup/linkedinCampaignGroup.dto";
import { ILinkedInAdAccountRepository } from "../../interfaces/infrastructure/repositories/linkedInAdAccount.repository.interface";
import { ILinkedInCampaignGroupRepository } from "../../interfaces/infrastructure/repositories/linkedInCampaignGroup.repository.interface";

export type SegmentAnalytics = {
  name: string;
  totalEngagements: number;
  oneClickLeads: number;
  costInUsd: number;
};

export type CampaignPreview = {
  id: string;
  name: string;
  format: string;
  status: string;
};

export type AdCreativePreview = {
  id: string;
  name?: string;
  type?: string;
  campaignId: string;
  campaignName?: string;
  campaignStatus?: string;
  campaignGroupStatus?: string;
  intendedStatus: string;
};

export type CampaignGroupPreviewResult = {
  campaignGroup: {
    id: string;
    name: string;
    startDate: Date | undefined;
    endDate: Date | undefined;
    status?: string;
    objectiveType?:
      | "BRAND_AWARENESS"
      | "JOB_APPLICANTS"
      | "ENGAGEMENT"
      | "WEBSITE_VISITS"
      | "WEBSITE_CONVERSIONS"
      | "LEAD_GENERATION"
      | "VIDEO_VIEWS";
  };
  campaignsCount: number;
  campaigns: CampaignPreview[];
  adCreativesCount: number;
  adCreatives: AdCreativePreview[];
  hasData: boolean;
  error?: string;
};

export class GetLinkedInCampaignGroupWithAdsUseCase {
  constructor(
    private readonly ctx: {
      linkedInCampaignGroupService: LinkedInCampaignGroupService;
      linkedInService: LinkedInService;
    },
  ) {}

  async execute(params: {
    campaignGroupId: string;
    linkedInAdAccountId: string;
    storeInDatabase?: boolean;
  }): Promise<CampaignGroupPreviewResult> {
    try {
      //Use campaigngroupid to get campaign group- name, details
      const linkedInCampaignGroup =
        await this.ctx.linkedInService.getCampaignGroup({
          linkedInAdAccountId: params.linkedInAdAccountId,
          linkedInCampaignGroupId: params.campaignGroupId,
        });

      if (!linkedInCampaignGroup) {
        return {
          campaignGroup: {
            id: params.campaignGroupId,
            name: "",
            startDate: undefined,
            endDate: undefined,
            status: undefined,
            objectiveType: undefined,
          },
          campaignsCount: 0,
          campaigns: [],
          adCreativesCount: 0,
          adCreatives: [],
          hasData: false,
          error: "Campaign group not found",
        };
      }

      // use campaign groupid to get campaigns
      const campaigns =
        await this.ctx.linkedInService.getCampaignsByCampaignGroupId({
          linkedInAdAccountId: params.linkedInAdAccountId,
          campaignGroupId: params.campaignGroupId,
        });

      if (!campaigns) {
        return {
          campaignGroup: {
            id: params.campaignGroupId,
            name: linkedInCampaignGroup.name,
            status: linkedInCampaignGroup.status,
            startDate: new Date(linkedInCampaignGroup.runSchedule.start),
            endDate: linkedInCampaignGroup.runSchedule.end
              ? new Date(linkedInCampaignGroup.runSchedule.end)
              : undefined,
            objectiveType: linkedInCampaignGroup.objectiveType,
          },
          campaignsCount: 0,
          campaigns: [],
          adCreativesCount: 0,
          adCreatives: [],
          hasData: false,
          error: "No campaigns data available",
        };
      }

      if (!campaigns.elements || campaigns?.elements.length === 0) {
        return {
          campaignGroup: {
            id: params.campaignGroupId,
            name: linkedInCampaignGroup.name,
            status: linkedInCampaignGroup.status,
            startDate: new Date(linkedInCampaignGroup.runSchedule.start),
            endDate: linkedInCampaignGroup.runSchedule.end
              ? new Date(linkedInCampaignGroup.runSchedule.end)
              : undefined,
            objectiveType: linkedInCampaignGroup.objectiveType,
          },
          campaignsCount: 0,
          campaigns: [],
          adCreativesCount: 0,
          adCreatives: [],
          hasData: false,
          error: "No campaigns found for this campaign group",
        };
      }

      const campaignIds = [];

      // use campaigns to get ads
      for (const campaign of campaigns?.elements) {
        campaignIds.push(campaign.id.toString());
      }

      const adCreatives =
        await this.ctx.linkedInService.getAdCreativesByCampaigns({
          linkedInAdAccountId: params.linkedInAdAccountId,
          campaignIds: campaignIds,
        });

      const adCreativesCount = adCreatives.elements?.length || 0;

      // Only store in database if explicitly requested
      if (params.storeInDatabase) {
        // TODO: Store data in db
        // campaign group
        // campaigns
        // ads
        console.log("Storing data in database...");
      }

      // Extract campaign information
      const campaignPreviews = campaigns.elements.map((campaign) => ({
        id: campaign.id.toString(),
        name: campaign.name,
        status: campaign.status,
        format: campaign.format,
      }));

      // Create a map of campaign IDs to names for reference
      const campaignIdToName = new Map<string, string>();
      campaigns.elements.forEach((campaign) => {
        campaignIdToName.set(
          "urn:li:sponsoredCampaign:" + campaign.id.toString(),
          campaign.name,
        );
      });

      adCreatives.elements.forEach((element) => {
        console.log("Ad Creative", element);
      });

      const creativesWithNoNames: Record<string, string> = {};

      const adCreativePreviews = [];
      for (const adCreative of adCreatives.elements) {
        const campaignId = adCreative.campaign?.toString() || "";
        const campaign = campaigns.elements.find(
          (c) => "urn:li:sponsoredCampaign:" + c.id.toString() === campaignId,
        );

        const liAdCreativePreviewRes =
          await this.ctx.linkedInService.getAdCreativePreview({
            linkedInAdAccountId: params.linkedInAdAccountId,
            adCreativeUrn: adCreative.id.toString(),
          });

        const liAdCreativePreview = liAdCreativePreviewRes.elements[0];

        let iframe = "";
        if (liAdCreativePreview) {
          iframe = liAdCreativePreview.preview;
          iframe = iframe.replace("height=580", 'height="120"');
          iframe = iframe.replace("width=650", 'width="370"');
        }

        const adCreativePreview = {
          id: adCreative.id.toString(),
          name: adCreative.name,
          preview: iframe,
          // type: adType,
          campaignId: campaignId,
          campaignName: campaignId
            ? campaignIdToName.get(campaignId)
            : undefined,
          campaignStatus: campaign?.status,
          intendedStatus: adCreative.intendedStatus,
          campaignGroupStatus: linkedInCampaignGroup.status,
        };

        adCreativePreviews.push(adCreativePreview);
      }

      return {
        campaignGroup: {
          id: params.campaignGroupId,
          name: linkedInCampaignGroup.name,
          status: linkedInCampaignGroup.status,
          startDate: new Date(linkedInCampaignGroup.runSchedule.start),
          endDate: linkedInCampaignGroup.runSchedule.end
            ? new Date(linkedInCampaignGroup.runSchedule.end)
            : undefined,
          objectiveType: linkedInCampaignGroup.objectiveType,
        },
        campaignsCount: campaigns.elements.length,
        campaigns: campaignPreviews,
        adCreativesCount: adCreativesCount,
        adCreatives: adCreativePreviews,
        hasData: campaigns.elements.length > 0 && adCreativesCount > 0,
      };
    } catch (error) {
      console.error("Error in GetLinkedInCampaignGroupWithAdsUseCase:", error);
      return {
        campaignGroup: {
          id: params.campaignGroupId,
          name: "",
          startDate: undefined,
          endDate: undefined,
        },
        campaignsCount: 0,
        campaigns: [],
        adCreativesCount: 0,
        adCreatives: [],
        hasData: false,
        error:
          error instanceof Error ? error.message : "An unknown error occurred",
      };
    }
  }
}
