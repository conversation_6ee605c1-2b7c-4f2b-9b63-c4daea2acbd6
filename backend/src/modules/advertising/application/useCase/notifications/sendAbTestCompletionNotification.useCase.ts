import { IOrganizationRepository } from "../../../../core/application/interfaces/repositories/organization.repository.interface";
import { ISegmentRepository } from "../../../../core/application/interfaces/repositories/segment.repository.interface";
import { SlackApiService } from "../../../infrastructure/services/slack.service";
import { LinkedInAdAccountRepository } from "../../../infrastructure/repositories/linkedInAdAccount.repository";
import { LinkedInAdProgramRepository } from "../../../infrastructure/repositories/linkedInAdProgram.repository";
import { LinkedInAdSegmentRepository } from "../../../infrastructure/repositories/linkedInAdSegment.repository";
import { LinkedInCampaignGroupRepository } from "../../../infrastructure/repositories/linkedInCampaignGroup.repository";

interface SendAbTestCompletionNotificationInput {
  adSegmentId: string;
  winnerId: string;
  totalRounds: number;
}

export class SendAbTestCompletionNotificationUseCase {
  constructor(
    private readonly organizationRepository: IOrganizationRepository,
    private readonly segmentRepository: ISegmentRepository,
    private readonly adSegmentRepository: LinkedInAdSegmentRepository,
    private readonly adProgramRepository: LinkedInAdProgramRepository,
    private readonly adAccountRepository: LinkedInAdAccountRepository,
    private readonly campaignGroupRepository: LinkedInCampaignGroupRepository,
    private readonly slackApiService: SlackApiService,
  ) {}

  private buildAdSegmentUrl(
    baseUrl: string,
    adSegmentId: string,
    params: {
      name: string;
      budget: number;
      totalSpent: number;
      format: string;
      objectiveType: string;
      segmentId: string;
      startDate: string;
      endDate: string;
      status: string;
      adProgramId: string;
    }
  ): string {
    const urlParams = new URLSearchParams({
      name: params.name,
      budget: params.budget.toString(),
      totalSpent: params.totalSpent.toString(),
      format: params.format,
      objectiveType: params.objectiveType,
      segmentId: params.segmentId,
      startDate: params.startDate,
      endDate: params.endDate,
      status: params.status,
      adProgramId: params.adProgramId,
    });

    return `${baseUrl}/advertising/performance/${adSegmentId}?${urlParams.toString()}`;
  }

  async execute(input: SendAbTestCompletionNotificationInput): Promise<{ success: boolean; error?: string }> {
    try {
      // Get organization details for Slack webhook
      const adSegment = await this.adSegmentRepository.getOne(input.adSegmentId);
      if (!adSegment) {
        return { success: false, error: "AdSegment not found" };
      }

      const adProgram = await this.adProgramRepository.getOne(adSegment.linkedInAdProgramId);
      if (!adProgram) {
        return { success: false, error: "AdProgram not found" };
      }

      const adAccount = await this.adAccountRepository.getOneById(adProgram.linkedInAdAccountId);
      if (!adAccount) {
        return { success: false, error: "AdAccount not found" };
      }

      const organization = await this.organizationRepository.getOne(adAccount.organizationId);
      if (!organization?.slackNotificationWebhookUrl || !organization.slackNotificationsEnabled) {
        console.log("Slack notifications not configured or disabled");
        return { success: true }; // Not an error, just skip
      }

      // Get campaign group to show its title instead of ad program title
      let campaignTitle = adProgram.title;
      const campaignGroup = await this.campaignGroupRepository.getOneByAdSegmentId(input.adSegmentId);
      if (campaignGroup?.name) {
        campaignTitle = campaignGroup.name;
      }

      // Get segment details for the URL
      const segment = await this.segmentRepository.getOne(adSegment.segmentId);
      
      // Build the ad segment URL
      const baseUrl = process.env.FRONTEND_URL || process.env.NEXT_PUBLIC_APP_URL || 'https://app.getkalos.com';
      const adSegmentUrl = this.buildAdSegmentUrl(baseUrl, input.adSegmentId, {
        name: campaignTitle,
        budget: adProgram.totalBudget || adProgram.monthlyBudget || 0,
        totalSpent: 0, // We don't have analytics data in this context, so default to 0
        format: adProgram.adFormat.format,
        objectiveType: adProgram.objectiveType,
        segmentId: adSegment.segmentId,
        startDate: adProgram.startDatetime.toLocaleDateString(),
        endDate: adProgram.endDatetime?.toLocaleDateString() || "",
        status: adProgram.status === "ACTIVE" ? "ACTIVE" : "PAUSED",
        adProgramId: adProgram.id,
      });

      // Create Slack message for audience test completion
      const message = {
        blocks: [
          {
            type: "section",
            text: {
              type: "mrkdwn",
              text: `🎯 *Audience Test Complete*\n*${campaignTitle}* • ${input.totalRounds} rounds tested\n\nAdd more audiences or proceed to next stage.`
            }
          },
          {
            type: "section",
            text: {
              type: "mrkdwn",
              text: `<${adSegmentUrl}|View Campaign>`
            }
          }
        ]
      };

      const slackResponse = await this.slackApiService.sendMessage(organization.slackNotificationWebhookUrl, message);
      
      if (!slackResponse.ok) {
        return { success: false, error: slackResponse.error };
      }

      console.log("AB test completion slack notification message sent successfully");

      return { success: true };
    } catch (error) {
      console.error("Error sending audience test completion notification:", error);
      return { success: false, error: "Failed to send notification" };
    }
  }
} 