import { linkedInAd } from "../../../../../../../packages/database/src/schema/linkedInAd";
import { linkedInAdAccount } from "../../../../../../../packages/database/src/schema/linkedInAdAccount";
import { Transaction } from "../../../../../database/dbTransactionType";
import { linkedInAdSegmentValuePropTable } from "../../../../../database/schemas/advertising/linkedInAdSegmentValueProp.table";
import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { SegmentService } from "../../../../core/domain/services/segment.service";
import { segmentRepository } from "../../../../core/infrastructure/repositories/segment.repository";
import { AdAudienceService } from "../../../domain/services/adAudience.service";
import { AdCreativeService } from "../../../domain/services/adCreative.service";
import { AdSegmentService } from "../../../domain/services/adSegment.service";
import { AdSegmentValuePropService } from "../../../domain/services/adSegmentValueProp.service";
import { LinkedInAdAccountService } from "../../../domain/services/linkedInAdAccount.service";
import { LinkedInAdProgramService } from "../../../domain/services/linkedInAdProgram.service";
import { LinkedInAdProgramAdCreativeService } from "../../../domain/services/linkedInAdProgramAdCreative.service";
import { LinkedInCampaignService } from "../../../domain/services/linkedInCampaign.service";
import { LinkedInCampaignGroupService } from "../../../domain/services/linkedInCampaignGroup.service";
import { LinkedInPostService } from "../../../domain/services/linkedInPost.service";
import { LinkedInSponsoredCreativeService } from "../../../domain/services/linkedInSponsoredCreative.service";
import { OrganizationConversionService } from "../../../domain/services/organizationConversion.service";
import { SocialPostAdCopyService } from "../../../domain/services/socialPostAdCopy.service";
import { AdSegmentValuePropCreativeRepository } from "../../../infrastructure/repositories/adSegmentValuePropCreative.repository";
import { SocialPostCallToActionCopyRepository } from "../../../infrastructure/repositories/socialPostCallToActionCopy.repository";
import { advertisingInngestClient } from "../../../utils/advertisingInngestClient";
import { idToLinkedInUrn } from "../../../utils/linkedInUrnUtils";
import { DeployLinkedInAdProgramRequestDto } from "../../dtos/controllerDtos/linkedInAdProgram/deployLinkedInAdProgram.dto";
import { IAdCreativeStorageService } from "../../interfaces/infrastructure/services/adCreativeStorage.service.interface";
import { ILinkedInService } from "../../interfaces/infrastructure/services/thirdPartyApis/linkedInApi/linkedIn.service.interface";

export class DeployLinkedInAdProgramUseCase {
  constructor(
    private readonly ctx: {
      linkedInAdProgramService: LinkedInAdProgramService;
      adSegmentService: AdSegmentService;
      linkedInService: ILinkedInService;
      adAccountService: LinkedInAdAccountService;
      segmentService: SegmentService;
      organizationId: number;
      linkedInCampaignGroupService: LinkedInCampaignGroupService;
      adAudienceService: AdAudienceService;
      linkedInCampaignService: LinkedInCampaignService;
      linkedInPostService: LinkedInPostService;
      adSegmentValuePropService: AdSegmentValuePropService;
      socialPostCopySerice: SocialPostAdCopyService;
      adProgramCreativeService: LinkedInAdProgramAdCreativeService;
      adCreativeService: AdCreativeService;
      adCreativeStorageService: IAdCreativeStorageService;
      linkedInSponsoredCreativeService: LinkedInSponsoredCreativeService;
      organizationConversionService: OrganizationConversionService;
      adSegmentValuePropCreativeRepository: AdSegmentValuePropCreativeRepository;
      socialPostCallToActionCopyRepository: SocialPostCallToActionCopyRepository;
    },
  ) {}

  async execute(input: DeployLinkedInAdProgramRequestDto, tx: ITransaction) {
    const [adProgram, doesAdProgramBelongToOrganization] = await Promise.all([
      this.ctx.linkedInAdProgramService.getOne(input.adProgramId),
      this.ctx.linkedInAdProgramService.checkIfAdProgramBelongsToOrganization(
        input.adProgramId,
        this.ctx.organizationId,
      ),
    ]);

    if (!adProgram) {
      throw new Error("Ad program not found");
    }
    if (!doesAdProgramBelongToOrganization) {
      throw new Error("Ad program does not belong to organization");
    }
    const adCreativesForProgram =
      await this.ctx.adProgramCreativeService.getAllForLinkedInAdProgram(
        input.adProgramId,
      );

    const adAccount = await this.ctx.adAccountService.getOneById(
      adProgram.linkedInAdAccountId,
    );
    if (!adAccount) {
      throw new Error("Ad account not found");
    }

    /*const imageUrn =
      adProgram.adFormat.format == "DOCUMENT"
        ? await this.ctx.linkedInService.uploadDocument({
            linkedInOrganizationUrn: adAccount.linkedInOrganizationUrn,
            body: adCreativeFile,
            type: "document",
            fileSizeBytes: adCreativeMetadata.metadata.size,
          })
        : await this.ctx.linkedInService.uploadImage({
            linkedInOrganizationUrn: adAccount.linkedInOrganizationUrn,
            body: adCreativeFile,
            type: adProgram.adFormat.format == "VIDEO" ? "video" : "image",
            fileSizeBytes: adCreativeMetadata.metadata.size,
          });*/

    const adSegments = await this.ctx.adSegmentService.getAllForAdProgram(
      input.adProgramId,
    );

    for (const adSegment of adSegments) {
      const adSegmentValuePropsToSetToActive = [];
      const socialPostCopiesToSetToActive = [];
      const socialPostCallToActionCopiesToSetToActive = [];
      for (const currValueProp of await this.ctx.adSegmentValuePropService.getManyForAdSegment(
        adSegment.id,
        "DRAFT",
      )) {
        const adSegmentValuePropCreatives =
          await this.ctx.adSegmentValuePropCreativeRepository.getAllForValueProp(
            currValueProp.id,
          );
        for (const adCreativeForProgram of adSegmentValuePropCreatives) {
          const socialPostCopy = await this.ctx.socialPostCopySerice.getOne({
            valuePropId: currValueProp.id,
            socialPostCopyType: "standard",
            status: "DRAFT",
          });
          if (!socialPostCopy) {
            throw "No value prop available";
          }
          const socialPostCallToActionCopy =
            await this.ctx.socialPostCallToActionCopyRepository.getOne({
              valuePropId: currValueProp.id,
              socialPostCopyType: "Standard",
              status: "DRAFT",
            });

          if (!socialPostCallToActionCopy) {
            throw "No social post call to action copy found";
          }

          if (adProgram.adFormat.format == "SINGLE_IMAGE") {
            await this.ctx.linkedInPostService.createOne(
              {
                content: {
                  type: "SINGLE_IMAGE",
                  adCreativeId: adCreativeForProgram.adProgramCreativeId,
                  socialPostCopyType: socialPostCopy.socialPostCopyType,
                  linkedInAdSegmentValuePropId:
                    socialPostCopy.linkedInAdSegmentValuePropId,
                  socialPostCallToActionType: socialPostCallToActionCopy.type,
                },
              },
              tx,
            );
          } else if (adProgram.adFormat.format == "VIDEO") {
            await this.ctx.linkedInPostService.createOne(
              {
                content: {
                  type: "SINGLE_VIDEO",
                  adCreativeId: adCreativeForProgram.adProgramCreativeId,
                  socialPostCopyType: socialPostCopy.socialPostCopyType,
                  socialPostCallToActionType: socialPostCallToActionCopy.type,
                  linkedInAdSegmentValuePropId:
                    socialPostCopy.linkedInAdSegmentValuePropId,
                },
              },
              tx,
            );
          } else if (adProgram.adFormat.format == "DOCUMENT") {
            console.log("HI ITS DOC");
            await this.ctx.linkedInPostService.createOne(
              {
                content: {
                  type: "DOCUMENT",
                  adCreativeId: adCreativeForProgram.adProgramCreativeId,
                  socialPostCopyType: socialPostCopy.socialPostCopyType,
                  socialPostCallToActionType: socialPostCallToActionCopy.type,
                  linkedInAdSegmentValuePropId:
                    socialPostCopy.linkedInAdSegmentValuePropId,
                },
              },
              tx,
            );
            console.log("created doc");
          } else {
            throw "Invalid ad format";
          }
        }
      }
      const adSegmentBudget = input.adSegmentBudgets[adSegment.id];
      if (!adSegmentBudget) {
        throw new Error("Ad segment budget not found");
      }
      const segment = await this.ctx.segmentService.getSegmentById(
        adSegment.segmentId,
        {
          segmentRepository: segmentRepository,
        },
      );
      if (!segment) {
        throw new Error("Segment not found");
      }
      let campaignGroupName = adProgram.title;
      if (segment.name) {
        campaignGroupName += ` - ${segment.name}`;
      } else {
        const segmentDetails = getSegmentDetails({
          row: {
            original: segment,
          },
        });
        campaignGroupName += ` - ${segmentDetails ?? ""}`;
      }

      const startDate =
        adProgram.startDatetime < new Date(Date.now())
          ? new Date(Date.now())
          : adProgram.startDatetime;

      const endDate = adProgram.endDatetime
        ? new Date(startDate.getTime() + 24 * 60 * 60 * 1000) >
          adProgram.endDatetime
          ? new Date(startDate.getTime() + 24 * 60 * 60 * 1000)
          : adProgram.endDatetime
        : undefined;

      let objectiveType:
        | "BRAND_AWARENESS"
        | "ENGAGEMENT"
        | "LEAD_GENERATION"
        | "VIDEO_VIEWS"
        | "JOB_APPLICANTS"
        | "WEBSITE_CONVERSIONS"
        | "WEBSITE_VISIT"
        | null = null;
      switch (adProgram.objectiveType) {
        case "BRAND_AWARENESS":
          objectiveType = "WEBSITE_VISIT";
          break;
        case "LEAD_GENERATION":
          objectiveType = "LEAD_GENERATION";
          break;
        case "VIDEO_VIEW":
          objectiveType = "VIDEO_VIEWS";
          break;
        default:
          throw new Error("Invalid objective type");
      }
      const campaignGroupId =
        await this.ctx.linkedInService.createCampaignGroup({
          adAccountUrn: adAccount.linkedInAdAccountUrn,
          name: campaignGroupName,
          startDate: startDate,
          endDate: endDate,
          status: "ACTIVE",
          objectiveType: objectiveType,
        });
      const campaignGroupIdNumber = parseInt(campaignGroupId);
      if (isNaN(campaignGroupIdNumber)) {
        throw new Error("Campaign group id is not a number");
      }
      const campaignGroupUrn = idToLinkedInUrn(
        campaignGroupIdNumber,
        "sponsoredCampaignGroup",
      );
      await this.ctx.linkedInCampaignGroupService.createOne(
        {
          linkedInAdSegmentId: adSegment.id,
          linkedInCampaignGroupUrn: campaignGroupUrn,
          name: campaignGroupName,
          status: "ACTIVE",
          startDatetime: startDate,
          totalBudget: adSegmentBudget,
          createdFromLinkedIn: false,
          linkedInAdAccountId: adAccount.id,
          objectiveType: null,
        },
        tx,
      );

      const adAudiences = await this.ctx.adAudienceService.getManyForAdSegment(
        adSegment.id,
      );

      const postThing: {
        postId: string;
        body: string;
        headline: string;
        valuePropName: string;
        adCreativeId: string;
        leadGenFormUrn: string | null;
      }[] = [];
      for (const currValueProp of await this.ctx.adSegmentValuePropService.getManyForAdSegment(
        adSegment.id,
        "DRAFT",
      )) {
        adSegmentValuePropsToSetToActive.push(currValueProp.id);
        const adSegmentValuePropCreatives =
          await this.ctx.adSegmentValuePropCreativeRepository.getAllForValueProp(
            currValueProp.id,
          );

        for (const adCreativeForProgram of adSegmentValuePropCreatives) {
          if (adProgram.adFormat.format == "SINGLE_IMAGE") {
            const post =
              await this.ctx.linkedInPostService.getSingleImagePostByContent(
                {
                  linkedInAdSegmentValuePropId: currValueProp.id,
                  socialPostCopyType: "standard",
                  socialPostCallToActionType: "Standard",
                  adProgramCreativeId: adCreativeForProgram.adProgramCreativeId,
                },
                tx,
              );
            if (!post) {
              throw "No image post found for value prop";
            }
            const socialPostCopy = await this.ctx.socialPostCopySerice.getOne({
              valuePropId: currValueProp.id,
              socialPostCopyType: "standard",
              status: "DRAFT",
            });
            if (!socialPostCopy) {
              throw "No image social post copy found for value prop";
            }
            socialPostCopiesToSetToActive.push(socialPostCopy.id);

            const socialPostCallToActionCopy =
              await this.ctx.socialPostCallToActionCopyRepository.getOne({
                valuePropId: currValueProp.id,
                socialPostCopyType: "Standard",
                status: "DRAFT",
              });
            if (!socialPostCallToActionCopy) {
              throw "No image social post call to action copy found for value prop";
            }
            socialPostCallToActionCopiesToSetToActive.push(
              socialPostCallToActionCopy.id,
            );

            const adProgramCreative = adCreativesForProgram.find(
              (x) => x.id == adCreativeForProgram.adProgramCreativeId,
            );
            if (!adProgramCreative) {
              throw "No ad program creative found for value prop";
            }

            postThing.push({
              postId: post.id,
              body: socialPostCopy.body,
              headline: socialPostCallToActionCopy.callToAction,
              valuePropName: currValueProp.valueProp,
              adCreativeId: adProgramCreative.adCreativeId,
              leadGenFormUrn: socialPostCopy.leadGenFormUrn ?? null,
            });
          } else if (adProgram.adFormat.format == "VIDEO") {
            const post =
              await this.ctx.linkedInPostService.getSingleVideoPostByContent(
                {
                  linkedInAdSegmentValuePropId: currValueProp.id,
                  socialPostCopyType: "standard",
                  socialPostCallToActionType: "Standard",
                  adProgramCreativeId: adCreativeForProgram.adProgramCreativeId,
                },
                tx,
              );
            if (!post) {
              throw "No video post found for value prop";
            }

            const socialPostCopy = await this.ctx.socialPostCopySerice.getOne({
              valuePropId: currValueProp.id,
              socialPostCopyType: "standard",
              status: "DRAFT",
            });
            if (!socialPostCopy) {
              throw "No video social post copy found for value prop";
            }
            socialPostCopiesToSetToActive.push(socialPostCopy.id);
            const adProgramCreative = adCreativesForProgram.find(
              (x) => x.id == adCreativeForProgram.adProgramCreativeId,
            );
            if (!adProgramCreative) {
              throw "No ad program creative found for value prop";
            }

            const socialPostCallToActionCopy =
              await this.ctx.socialPostCallToActionCopyRepository.getOne({
                valuePropId: currValueProp.id,
                socialPostCopyType: "Standard",
                status: "DRAFT",
              });
            if (!socialPostCallToActionCopy) {
              throw "No video social post call to action copy found for value prop";
            }
            socialPostCallToActionCopiesToSetToActive.push(
              socialPostCallToActionCopy.id,
            );
            postThing.push({
              postId: post.id,
              body: socialPostCopy.body,
              headline: socialPostCallToActionCopy.callToAction,
              valuePropName: currValueProp.valueProp,
              adCreativeId: adProgramCreative.adCreativeId,
              leadGenFormUrn: socialPostCopy.leadGenFormUrn ?? null,
            });
          } else if (adProgram.adFormat.format == "DOCUMENT") {
            const post =
              await this.ctx.linkedInPostService.getSingleDocumentPostByContent(
                {
                  linkedInAdSegmentValuePropId: currValueProp.id,
                  socialPostCopyType: "standard",
                  socialPostCallToActionType: "Standard",
                  adProgramCreativeId: adCreativeForProgram.adProgramCreativeId,
                },
                tx,
              );
            if (!post) {
              throw "No document post found for value prop";
            }

            const socialPostCopy = await this.ctx.socialPostCopySerice.getOne({
              valuePropId: currValueProp.id,
              socialPostCopyType: "standard",
              status: "DRAFT",
            });
            if (!socialPostCopy) {
              throw "No document social post copy found for value prop";
            }
            socialPostCopiesToSetToActive.push(socialPostCopy.id);
            const adProgramCreative = adCreativesForProgram.find(
              (x) => x.id == adCreativeForProgram.adProgramCreativeId,
            );
            if (!adProgramCreative) {
              throw "No ad program creative found for value prop";
            }

            const socialPostCallToActionCopy =
              await this.ctx.socialPostCallToActionCopyRepository.getOne({
                valuePropId: currValueProp.id,
                socialPostCopyType: "Standard",
                status: "DRAFT",
              });
            if (!socialPostCallToActionCopy) {
              throw "No document social post call to action copy found for value prop";
            }
            socialPostCallToActionCopiesToSetToActive.push(
              socialPostCallToActionCopy.id,
            );
            postThing.push({
              postId: post.id,
              body: socialPostCopy.body,
              headline: socialPostCallToActionCopy.callToAction,
              valuePropName: currValueProp.valueProp,
              adCreativeId: adProgramCreative.adCreativeId,
              leadGenFormUrn: socialPostCopy.leadGenFormUrn ?? null,
            });
          }
        }
      }
      let numOfAudiences = 0;

      adAudiences.forEach((aud) => {
        if (aud.toBeUsed) {
          numOfAudiences += 1;
        }
      });

      console.log("NUMOFAUD", numOfAudiences);
      for (const eachAdAudience of adAudiences) {
        if (!eachAdAudience.toBeUsed) {
          continue;
        }
        const adAudience = await this.ctx.adAudienceService.getOne(
          eachAdAudience.id,
        );
        if (!adAudience) {
          throw new Error("Ad audience not found");
        }

        const facetNames: string[] = [];
        for (const orGroup of adAudience.audienceTargetCriteria.include.and) {
          for (const eachFacet of orGroup.or) {
            facetNames.push(eachFacet.facetName);
          }
        }
        const campaignName = `${facetNames.join(", ")}`;
        let budgetType: "TOTAL" | "DAILY" = "TOTAL";

        // If there is more than 1 audience, split budget by 2 so
        // each audience test gets half of the segment budget
        const budget =
          numOfAudiences === 1 ? adSegmentBudget : adSegmentBudget / 2;

        let dailyBudget = budget;

        if (adProgram.type == "EVERGREEN") {
          budgetType = "DAILY";
          dailyBudget = dailyBudget / 30;
        }

        const campaignUrn = await this.ctx.linkedInService.createCampaign({
          adAccountUrn: adAccount.linkedInAdAccountUrn,
          campaignGroupUrn: campaignGroupUrn,
          budget: dailyBudget,
          budgetType: budgetType,
          name: campaignName,
          startDate: startDate,
          endDate: endDate,
          audienceTargets: adAudience.audienceTargetCriteria,
          unitCost: 2,
          manualBidding: true, // Determines budget, has implications if changed here, will require udpate in endStage.inngestFunction
          objectiveType: objectiveType,
        });
        const organizationConversions =
          await this.ctx.organizationConversionService.getForOrganization(
            adAccount.organizationId,
          );
        for (const organizationConversion of organizationConversions) {
          await this.ctx.linkedInService.associateConversationWithCampaign(
            organizationConversion.conversionUrn,
            campaignUrn,
          );
        }

        await this.ctx.linkedInCampaignService.createOne(
          {
            linkedInAudienceId: eachAdAudience.id,
            linkedInCampaignUrn: campaignUrn,
            totalBudget: budget,
            status: "ACTIVE",
          },
          tx,
        );

        for (const post of postThing) {
          const adCreative = await this.ctx.adCreativeService.getOneById(
            post.adCreativeId,
          );
          if (!adCreative) {
            throw "Ad creative not found";
          }
          const adCreativeMetadata =
            await this.ctx.adCreativeService.getOneWithDownloadPreSignedUrlAndMetadata(
              post.adCreativeId,
            );

          if (!adCreativeMetadata?.metadata.size) {
            throw "Cannot get creative file size";
          }
          let imageUrn = null;
          let retryCount = 0;
          const maxRetries = 3;

          while (imageUrn === null && retryCount < maxRetries) {
            if (retryCount > 0) {
              // Wait for 20 seconds before retrying
              await new Promise((resolve) => setTimeout(resolve, 20000));
              console.log(
                `Retrying LinkedIn upload (attempt ${retryCount + 1}/${maxRetries})...`,
              );
            }
            const adCreativeFile =
              await this.ctx.adCreativeService.getAdCreativeFile(
                post.adCreativeId,
              );
            if (adCreativeFile == null) {
              throw "Cannot get creative file";
            }

            imageUrn =
              adProgram.adFormat.format == "DOCUMENT"
                ? await this.ctx.linkedInService.uploadDocument({
                    linkedInOrganizationUrn: adAccount.linkedInOrganizationUrn,
                    body: adCreativeFile,
                    type: "document",
                    fileSizeBytes: adCreativeMetadata.metadata.size,
                  })
                : await this.ctx.linkedInService.uploadImage({
                    linkedInOrganizationUrn: adAccount.linkedInOrganizationUrn,
                    body: adCreativeFile,
                    type:
                      adProgram.adFormat.format == "VIDEO" ? "video" : "image",
                    fileSizeBytes: adCreativeMetadata.metadata.size,
                  });

            retryCount++;
          }

          if (imageUrn === null) {
            throw new Error(
              "Failed to upload creative to LinkedIn after multiple attempts",
            );
          }

          const creative = await this.ctx.linkedInService.createInlineCreative({
            adAccountUrn: adAccount.linkedInAdAccountUrn,
            campaignUrn: campaignUrn,
            imageUrn: imageUrn,
            commentary: post.body,
            headline: post.headline,
            linkedInOrgId: adAccount.linkedInOrganizationUrn,
            destinationUrl:
              adProgram.objectiveType != "LEAD_GENERATION"
                ? (input.destinationUrls.find(
                    (url) => url.adSegmentId === adSegment.id,
                  )?.url ?? "")
                : undefined,
            adFormUrn:
              adProgram.objectiveType == "LEAD_GENERATION" &&
              post.leadGenFormUrn
                ? post.leadGenFormUrn
                : undefined,
            adName: `${campaignName} - ${post.valuePropName.slice(0, 10)} - ${adCreative.fileName.slice(0, 10)} - standard`,
          });

          await this.ctx.linkedInSponsoredCreativeService.createOne(
            {
              cmapaignId: eachAdAudience.id,
              linkedInSponseredCreativeUrn: creative,
              status: "PAUSED",
              content: {
                type: "SPONSORED_CONTENT",
                postId: post.postId,
              },
            },
            tx,
          );
        }
      }
      await this.ctx.adSegmentValuePropService.updateManyToActive(
        adSegmentValuePropsToSetToActive,
        tx,
      );
      await this.ctx.socialPostCopySerice.updateManyToActive(
        socialPostCopiesToSetToActive,
        tx,
      );

      await this.ctx.socialPostCallToActionCopyRepository.updateManyToActive(
        socialPostCallToActionCopiesToSetToActive,
        tx as Transaction,
      );

      await this.ctx.adSegmentService.setSenderAndDestinationUrl(
        adSegment.id,
        null,
        input.destinationUrls.find((url) => url.adSegmentId === adSegment.id)
          ?.url ?? null,
        tx,
      );
    }
  }
}

function getSegmentDetails({
  row,
}: {
  row: {
    original: {
      id: string;
      name?: string | null;
      verticals: string[];
      annualRevenueLowBound?: number | null;
      annualRevenueHighBound?: number | null;
      numberOfEmployeesLowBound?: number | null;
      numberOfEmployeesHighBound?: number | null;
      annualContractValueLowBound?: number | null;
      annualContractValueHighBound?: number | null;
      jobFunction?: string | null;
      jobSeniority?: string | null;
    };
  };
}) {
  if (!row.original.name) {
    const nameArray: string[] = [];
    if (row.original.jobFunction) {
      nameArray.push(row.original.jobFunction);
    }
    if (row.original.jobSeniority) {
      nameArray.push(row.original.jobSeniority);
    }

    if (row.original.verticals) {
      nameArray.push(row.original.verticals.join(", "));
    }

    if (
      row.original.annualContractValueLowBound &&
      row.original.annualContractValueHighBound
    ) {
      nameArray.push(
        `$${row.original.annualContractValueLowBound
          .toString()
          .replace(
            /\B(?=(\d{3})+(?!\d))/g,
            ",",
          )}-$${row.original.annualContractValueHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} ACV`,
      );
    }
    if (
      row.original.annualContractValueLowBound &&
      !row.original.annualContractValueHighBound
    ) {
      nameArray.push(
        `$${row.original.annualContractValueLowBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")}+ ACV`,
      );
    }
    if (
      !row.original.annualContractValueLowBound &&
      row.original.annualContractValueHighBound
    ) {
      nameArray.push(
        `<$${row.original.annualContractValueHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} ACV`,
      );
    }

    if (
      row.original.annualRevenueLowBound &&
      row.original.annualRevenueHighBound
    ) {
      nameArray.push(
        `$${row.original.annualRevenueLowBound
          .toString()
          .replace(
            /\B(?=(\d{3})+(?!\d))/g,
            ",",
          )}-$${row.original.annualRevenueHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} prospect revenue`,
      );
    }
    if (
      row.original.annualRevenueLowBound &&
      !row.original.annualRevenueHighBound
    ) {
      nameArray.push(
        `$${row.original.annualRevenueLowBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")}+ prospect revenue`,
      );
    }
    if (
      !row.original.annualRevenueLowBound &&
      row.original.annualRevenueHighBound
    ) {
      nameArray.push(
        `<$${row.original.annualRevenueHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} prospect revenue`,
      );
    }

    if (
      row.original.numberOfEmployeesLowBound &&
      row.original.numberOfEmployeesHighBound
    ) {
      nameArray.push(
        `${row.original.numberOfEmployeesLowBound
          .toString()
          .replace(
            /\B(?=(\d{3})+(?!\d))/g,
            ",",
          )}-${row.original.numberOfEmployeesHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} employees`,
      );
    }
    if (
      row.original.numberOfEmployeesLowBound &&
      !row.original.numberOfEmployeesHighBound
    ) {
      nameArray.push(
        `${row.original.numberOfEmployeesLowBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")}+ employees`,
      );
    }
    if (
      !row.original.numberOfEmployeesLowBound &&
      row.original.numberOfEmployeesHighBound
    ) {
      nameArray.push(
        `<${row.original.numberOfEmployeesHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} employees`,
      );
    }

    let acvQueryParam = "";
    if (
      row.original.annualContractValueHighBound == 20000 &&
      row.original.annualContractValueLowBound == undefined
    ) {
      acvQueryParam = "1";
    } else if (
      row.original.annualContractValueHighBound == 50000 &&
      row.original.annualContractValueLowBound == 20000
    ) {
      acvQueryParam = "2";
    } else if (
      row.original.annualContractValueHighBound == 100000 &&
      row.original.annualContractValueLowBound == 50000
    ) {
      acvQueryParam = "3";
    } else if (
      row.original.annualContractValueHighBound == undefined &&
      row.original.annualContractValueLowBound == 100000
    ) {
      acvQueryParam = "4";
    }

    let empoyeesQueryParam = "";
    if (
      row.original.numberOfEmployeesHighBound == 100 &&
      row.original.numberOfEmployeesLowBound == undefined
    ) {
      empoyeesQueryParam = "1";
    } else if (
      row.original.numberOfEmployeesHighBound == 500 &&
      row.original.numberOfEmployeesLowBound == 100
    ) {
      empoyeesQueryParam = "2";
    } else if (
      row.original.numberOfEmployeesHighBound == undefined &&
      row.original.numberOfEmployeesLowBound == 500
    ) {
      empoyeesQueryParam = "3";
    }

    let prospectRevenue = "";
    if (
      row.original.annualRevenueLowBound == 1000000 &&
      row.original.annualRevenueHighBound == 10000000
    ) {
      prospectRevenue = "1";
    } else if (
      row.original.annualRevenueLowBound == 10000000 &&
      row.original.annualRevenueHighBound == 200000000
    ) {
      prospectRevenue = "2";
    } else if (
      row.original.annualRevenueLowBound == 200000000 &&
      row.original.annualRevenueHighBound == undefined
    ) {
      prospectRevenue = "3";
    }

    // Search params
    const params = new URLSearchParams({
      acv: acvQueryParam,
      vertical: row.original.verticals?.join(",") ?? "",
      employees: empoyeesQueryParam,
      prospectRevenue: prospectRevenue,
    });

    return nameArray.join(" • ");
  }
}
