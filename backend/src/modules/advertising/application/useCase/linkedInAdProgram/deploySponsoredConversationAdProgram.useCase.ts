import { Transaction } from "../../../../../database/dbTransactionType";
import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { SegmentService } from "../../../../core/domain/services/segment.service";
import { segmentRepository } from "../../../../core/infrastructure/repositories/segment.repository";
import { AdAudienceService } from "../../../domain/services/adAudience.service";
import { AdCreativeService } from "../../../domain/services/adCreative.service";
import { AdSegmentService } from "../../../domain/services/adSegment.service";
import { AdSegmentValuePropService } from "../../../domain/services/adSegmentValueProp.service";
import { ConversationCallToActionCopyService } from "../../../domain/services/conversationCallToActionCopy.service";
import { ConversationCopySerivce } from "../../../domain/services/conversationCopy.service";
import { LinkedInAdAccountService } from "../../../domain/services/linkedInAdAccount.service";
import { LinkedInAdProgramService } from "../../../domain/services/linkedInAdProgram.service";
import { LinkedInAdProgramAdCreativeService } from "../../../domain/services/linkedInAdProgramAdCreative.service";
import { LinkedInCampaignService } from "../../../domain/services/linkedInCampaign.service";
import { LinkedInCampaignGroupService } from "../../../domain/services/linkedInCampaignGroup.service";
import { LinkedInPostService } from "../../../domain/services/linkedInPost.service";
import { LinkedInSponsoredCreativeService } from "../../../domain/services/linkedInSponsoredCreative.service";
import { OrganizationConversionService } from "../../../domain/services/organizationConversion.service";
import { SocialPostAdCopyService } from "../../../domain/services/socialPostAdCopy.service";
import { ConversationMessageCopyRepository } from "../../../infrastructure/repositories/conversationMessageCopy.repository";
import { ConversationSubjectCopyRepository } from "../../../infrastructure/repositories/conversationSubjectCopy.repository";
import { advertisingInngestClient } from "../../../utils/advertisingInngestClient";
import { idToLinkedInUrn } from "../../../utils/linkedInUrnUtils";
import { DeployLinkedInAdProgramRequestDto } from "../../dtos/controllerDtos/linkedInAdProgram/deployLinkedInAdProgram.dto";
import { IAdCreativeStorageService } from "../../interfaces/infrastructure/services/adCreativeStorage.service.interface";
import { ILinkedInService } from "../../interfaces/infrastructure/services/thirdPartyApis/linkedInApi/linkedIn.service.interface";

export class DeploySponsoredConversationAdProgramUseCase {
  constructor(
    private readonly ctx: {
      linkedInAdProgramService: LinkedInAdProgramService;
      adSegmentService: AdSegmentService;
      linkedInService: ILinkedInService;
      adAccountService: LinkedInAdAccountService;
      segmentService: SegmentService;
      organizationId: number;
      linkedInCampaignGroupService: LinkedInCampaignGroupService;
      adAudienceService: AdAudienceService;
      linkedInCampaignService: LinkedInCampaignService;
      linkedInPostService: LinkedInPostService;
      adSegmentValuePropService: AdSegmentValuePropService;
      linkedInSponsoredCreativeService: LinkedInSponsoredCreativeService;
      conversationCopyService: ConversationCopySerivce;
      conversationCallToActionCopyService: ConversationCallToActionCopyService;
      organizationConversionService: OrganizationConversionService;
      conversationSubjectCopyRepository: ConversationSubjectCopyRepository;
      conversationMessageCopyRepository: ConversationMessageCopyRepository;
    },
  ) {}

  async execute(input: DeployLinkedInAdProgramRequestDto, tx: ITransaction) {
    if (!input.sender) {
      throw new Error("Sender not found");
    }
    const [adProgram, doesAdProgramBelongToOrganization] = await Promise.all([
      this.ctx.linkedInAdProgramService.getOne(input.adProgramId),
      this.ctx.linkedInAdProgramService.checkIfAdProgramBelongsToOrganization(
        input.adProgramId,
        this.ctx.organizationId,
      ),
    ]);

    if (!adProgram) {
      throw new Error("Ad program not found");
    }

    const leadGenForm = adProgram?.leadGenForm;
    const destinationUrl = adProgram?.destinationUrl;
    if (!leadGenForm && !destinationUrl) {
      throw new Error("Lead gen form or destination url not found");
    }
    if (!doesAdProgramBelongToOrganization) {
      throw new Error("Ad program does not belong to organization");
    }

    const adAccount = await this.ctx.adAccountService.getOneById(
      adProgram.linkedInAdAccountId,
    );
    if (!adAccount) {
      throw new Error("Ad account not found");
    }

    const adSegments = await this.ctx.adSegmentService.getAllForAdProgram(
      input.adProgramId,
    );

    for (const adSegment of adSegments) {
      const adSegmentValuePropsToSetToActive = [];
      const conversationSubjectCopiesToSetToActive = [];
      const conversationMessageCopiesToSetToActive = [];
      const conversationCallToActionCopiesToSetToActive = [];
      const adSegmentBudget = input.adSegmentBudgets[adSegment.id];
      if (!adSegmentBudget) {
        throw new Error("Ad segment budget not found");
      }
      const segment = await this.ctx.segmentService.getSegmentById(
        adSegment.segmentId,
        {
          segmentRepository: segmentRepository,
        },
      );
      if (!segment) {
        throw new Error("Segment not found");
      }
      let campaignGroupName = adProgram.title;
      if (segment.name) {
        campaignGroupName += ` - ${segment.name}`;
      } else {
        const segmentDetails = getSegmentDetails({
          row: {
            original: segment,
          },
        });
        campaignGroupName += ` - ${segmentDetails ?? ""}`;
      }

      const startDate =
        adProgram.startDatetime < new Date(Date.now())
          ? new Date(Date.now())
          : adProgram.startDatetime;

      const endDate = adProgram.endDatetime
        ? new Date(startDate.getTime() + 24 * 60 * 60 * 1000) >
          adProgram.endDatetime
          ? new Date(startDate.getTime() + 24 * 60 * 60 * 1000)
          : adProgram.endDatetime
        : undefined;

      let objectiveType:
        | "BRAND_AWARENESS"
        | "ENGAGEMENT"
        | "LEAD_GENERATION"
        | "VIDEO_VIEWS"
        | "JOB_APPLICANTS"
        | "WEBSITE_CONVERSIONS"
        | "WEBSITE_VISIT"
        | null = null;
      switch (adProgram.objectiveType) {
        case "LEAD_GENERATION":
          objectiveType = destinationUrl ? "WEBSITE_VISIT" : "LEAD_GENERATION";
          break;
        default:
          throw new Error("Invalid objective type");
      }
      const campaignGroupId =
        await this.ctx.linkedInService.createCampaignGroup({
          adAccountUrn: adAccount.linkedInAdAccountUrn,
          name: campaignGroupName,
          startDate: startDate,
          endDate: endDate,
          status: "ACTIVE",
          objectiveType: objectiveType,
        });
      const campaignGroupIdNumber = parseInt(campaignGroupId);
      if (isNaN(campaignGroupIdNumber)) {
        throw new Error("Campaign group id is not a number");
      }
      const campaignGroupUrn = idToLinkedInUrn(
        campaignGroupIdNumber,
        "sponsoredCampaignGroup",
      );
      await this.ctx.linkedInCampaignGroupService.createOne(
        {
          linkedInAdSegmentId: adSegment.id,
          linkedInCampaignGroupUrn: campaignGroupUrn,
          name: campaignGroupName,
          status: "ACTIVE",
          startDatetime: startDate,
          totalBudget: adSegmentBudget,
          createdFromLinkedIn: false,
          linkedInAdAccountId: adAccount.id,
          objectiveType: null,
        },
        tx,
      );

      const valueProps =
        await this.ctx.adSegmentValuePropService.getManyForAdSegment(
          adSegment.id,
          "DRAFT",
        );

      const copies = [];

      for (const valueProp of valueProps) {
        const copy =
          await this.ctx.conversationCallToActionCopyService.getAllForValuePropWithMessageAndSubjectCopies(
            valueProp.id,
            "DRAFT",
          );
        if (!copy) {
          throw new Error("No conversation copy found");
        }

        copies.push(...copy);
        adSegmentValuePropsToSetToActive.push(valueProp.id);
        conversationSubjectCopiesToSetToActive.push(
          ...copy.map((x) => x.conversationSubjectCopyId),
        );
        conversationMessageCopiesToSetToActive.push(
          ...copy.map((x) => x.conversationMessageCopyId),
        );
        conversationCallToActionCopiesToSetToActive.push(
          ...copy.map((x) => x.conversationCallToActionId),
        );
      }

      const adAudiences = await this.ctx.adAudienceService.getManyForAdSegment(
        adSegment.id,
      );

      let numOfAudiences = 0;

      adAudiences.forEach((aud) => {
        if (aud.toBeUsed) {
          numOfAudiences += 1;
        }
      });

      console.log("NUMOFAUD", numOfAudiences);
      for (const eachAdAudience of adAudiences) {
        if (!eachAdAudience.toBeUsed) {
          continue;
        }
        const adAudience = await this.ctx.adAudienceService.getOne(
          eachAdAudience.id,
        );
        if (!adAudience) {
          throw new Error("Ad audience not found");
        }

        const facetNames: string[] = [];
        for (const orGroup of adAudience.audienceTargetCriteria.include.and) {
          for (const eachFacet of orGroup.or) {
            facetNames.push(eachFacet.facetName);
          }
        }
        const campaignName = `${facetNames.join(", ")}`;
        let budgetType: "TOTAL" | "DAILY" = "TOTAL";
        const budget =
          numOfAudiences === 1 ? adSegmentBudget : adSegmentBudget / 2;

        let dailyBudget = budget;
        if (adProgram.type == "EVERGREEN") {
          budgetType = "DAILY";
          dailyBudget = dailyBudget / 30;
        }

        const campaignUrn = await this.ctx.linkedInService.createCampaign({
          adAccountUrn: adAccount.linkedInAdAccountUrn,
          campaignGroupUrn: campaignGroupUrn,
          budgetType: budgetType,
          budget: dailyBudget,
          name: campaignName,
          startDate: startDate,
          endDate: endDate,
          audienceTargets: adAudience.audienceTargetCriteria,
          unitCost: 0.5,
          manualBidding: true,
          currencyCode: "USD",
          objectiveType: objectiveType,
          type: "SPONSORED_INMAILS",
        });

        const organizationConversions =
          await this.ctx.organizationConversionService.getForOrganization(
            adAccount.organizationId,
          );
        for (const organizationConversion of organizationConversions) {
          await this.ctx.linkedInService.associateConversationWithCampaign(
            organizationConversion.conversionUrn,
            campaignUrn,
          );
        }

        await this.ctx.linkedInCampaignService.createOne(
          {
            linkedInAudienceId: eachAdAudience.id,
            linkedInCampaignUrn: campaignUrn,
            totalBudget: budget,
            status: "ACTIVE",
          },
          tx,
        );

        for (const copy of copies) {
          if (!copy.leadGenFormUrn && !copy.destinationUrl) {
            throw new Error("Lead gen form urn or destination url not found");
          }

          let conversationUrn: string | undefined;
          if (copy.destinationUrl) {
            conversationUrn =
              await this.ctx.linkedInService.createDestinationUrlConversation({
                adAccountUrn: adAccount.linkedInAdAccountUrn,
                destinationUrl: copy.destinationUrl,
                senderUrn: input.sender,
                campaignUrn: campaignUrn,
                body: copy.messageCopyContent,
                subject: copy.subjectCopyContent,
                leadGenButtonText: copy.callToActionCopyContent,
              });
          } else if (copy.leadGenFormUrn) {
            conversationUrn =
              await this.ctx.linkedInService.createLeadGenConversation({
                adAccountUrn: adAccount.linkedInAdAccountUrn,
                leadGenFormUrn: copy.leadGenFormUrn,
                senderUrn: input.sender,
                campaignUrn: campaignUrn,
                body: copy.messageCopyContent,
                subject: copy.subjectCopyContent,
                leadGenButtonText: copy.callToActionCopyContent,
              });
          }

          if (!conversationUrn) {
            throw new Error("Conversation urn not found");
          }

          await this.ctx.linkedInService.updateSponsoredCreativeStatus({
            adAccountUrn: adAccount.linkedInAdAccountUrn,
            adUrn: conversationUrn,
            status: "ACTIVE",
          });

          await this.ctx.linkedInSponsoredCreativeService.createOne(
            {
              cmapaignId: eachAdAudience.id,
              linkedInSponseredCreativeUrn: conversationUrn,
              status: "ACTIVE",
              content: {
                type: "SPONSORED_INMAIL",
                conversationCallToActionId: copy.conversationCallToActionId,
              },
            },
            tx,
          );
        }
      }
      await this.ctx.adSegmentValuePropService.updateManyToActive(
        adSegmentValuePropsToSetToActive,
        tx,
      );
      await this.ctx.conversationSubjectCopyRepository.updateManyToActive(
        {
          ids: conversationSubjectCopiesToSetToActive,
        },
        tx as Transaction,
      );
      await this.ctx.conversationMessageCopyRepository.updateManyToActive(
        {
          ids: conversationMessageCopiesToSetToActive,
        },
        tx as Transaction,
      );
      await this.ctx.conversationCallToActionCopyService.updateManyToActive(
        {
          ids: conversationCallToActionCopiesToSetToActive,
        },
        tx as Transaction,
      );

      await this.ctx.adSegmentService.setSenderAndDestinationUrl(
        adSegment.id,
        input.sender,
        null,
        tx,
      );
    }
  }
}

function getSegmentDetails({
  row,
}: {
  row: {
    original: {
      id: string;
      name?: string | null;
      verticals: string[];
      annualRevenueLowBound?: number | null;
      annualRevenueHighBound?: number | null;
      numberOfEmployeesLowBound?: number | null;
      numberOfEmployeesHighBound?: number | null;
      annualContractValueLowBound?: number | null;
      annualContractValueHighBound?: number | null;
      jobFunction?: string | null;
      jobSeniority?: string | null;
    };
  };
}) {
  if (!row.original.name) {
    const nameArray: string[] = [];
    if (row.original.jobFunction) {
      nameArray.push(row.original.jobFunction);
    }
    if (row.original.jobSeniority) {
      nameArray.push(row.original.jobSeniority);
    }

    if (row.original.verticals) {
      nameArray.push(row.original.verticals.join(", "));
    }

    if (
      row.original.annualContractValueLowBound &&
      row.original.annualContractValueHighBound
    ) {
      nameArray.push(
        `$${row.original.annualContractValueLowBound
          .toString()
          .replace(
            /\B(?=(\d{3})+(?!\d))/g,
            ",",
          )}-$${row.original.annualContractValueHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} ACV`,
      );
    }
    if (
      row.original.annualContractValueLowBound &&
      !row.original.annualContractValueHighBound
    ) {
      nameArray.push(
        `$${row.original.annualContractValueLowBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")}+ ACV`,
      );
    }
    if (
      !row.original.annualContractValueLowBound &&
      row.original.annualContractValueHighBound
    ) {
      nameArray.push(
        `<$${row.original.annualContractValueHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} ACV`,
      );
    }

    if (
      row.original.annualRevenueLowBound &&
      row.original.annualRevenueHighBound
    ) {
      nameArray.push(
        `$${row.original.annualRevenueLowBound
          .toString()
          .replace(
            /\B(?=(\d{3})+(?!\d))/g,
            ",",
          )}-$${row.original.annualRevenueHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} prospect revenue`,
      );
    }
    if (
      row.original.annualRevenueLowBound &&
      !row.original.annualRevenueHighBound
    ) {
      nameArray.push(
        `$${row.original.annualRevenueLowBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")}+ prospect revenue`,
      );
    }
    if (
      !row.original.annualRevenueLowBound &&
      row.original.annualRevenueHighBound
    ) {
      nameArray.push(
        `<$${row.original.annualRevenueHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} prospect revenue`,
      );
    }

    if (
      row.original.numberOfEmployeesLowBound &&
      row.original.numberOfEmployeesHighBound
    ) {
      nameArray.push(
        `${row.original.numberOfEmployeesLowBound
          .toString()
          .replace(
            /\B(?=(\d{3})+(?!\d))/g,
            ",",
          )}-${row.original.numberOfEmployeesHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} employees`,
      );
    }
    if (
      row.original.numberOfEmployeesLowBound &&
      !row.original.numberOfEmployeesHighBound
    ) {
      nameArray.push(
        `${row.original.numberOfEmployeesLowBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")}+ employees`,
      );
    }
    if (
      !row.original.numberOfEmployeesLowBound &&
      row.original.numberOfEmployeesHighBound
    ) {
      nameArray.push(
        `<${row.original.numberOfEmployeesHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} employees`,
      );
    }

    let acvQueryParam = "";
    if (
      row.original.annualContractValueHighBound == 20000 &&
      row.original.annualContractValueLowBound == undefined
    ) {
      acvQueryParam = "1";
    } else if (
      row.original.annualContractValueHighBound == 50000 &&
      row.original.annualContractValueLowBound == 20000
    ) {
      acvQueryParam = "2";
    } else if (
      row.original.annualContractValueHighBound == 100000 &&
      row.original.annualContractValueLowBound == 50000
    ) {
      acvQueryParam = "3";
    } else if (
      row.original.annualContractValueHighBound == undefined &&
      row.original.annualContractValueLowBound == 100000
    ) {
      acvQueryParam = "4";
    }

    let empoyeesQueryParam = "";
    if (
      row.original.numberOfEmployeesHighBound == 100 &&
      row.original.numberOfEmployeesLowBound == undefined
    ) {
      empoyeesQueryParam = "1";
    } else if (
      row.original.numberOfEmployeesHighBound == 500 &&
      row.original.numberOfEmployeesLowBound == 100
    ) {
      empoyeesQueryParam = "2";
    } else if (
      row.original.numberOfEmployeesHighBound == undefined &&
      row.original.numberOfEmployeesLowBound == 500
    ) {
      empoyeesQueryParam = "3";
    }

    let prospectRevenue = "";
    if (
      row.original.annualRevenueLowBound == 1000000 &&
      row.original.annualRevenueHighBound == 10000000
    ) {
      prospectRevenue = "1";
    } else if (
      row.original.annualRevenueLowBound == 10000000 &&
      row.original.annualRevenueHighBound == 200000000
    ) {
      prospectRevenue = "2";
    } else if (
      row.original.annualRevenueLowBound == 200000000 &&
      row.original.annualRevenueHighBound == undefined
    ) {
      prospectRevenue = "3";
    }

    // Search params
    const params = new URLSearchParams({
      acv: acvQueryParam,
      vertical: row.original.verticals?.join(",") ?? "",
      employees: empoyeesQueryParam,
      prospectRevenue: prospectRevenue,
    });

    return nameArray.join(" • ");
  }
}
