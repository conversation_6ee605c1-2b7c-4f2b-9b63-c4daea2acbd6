import { err, ok, Result } from "neverthrow";

import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { StageRepository } from "../../../infrastructure/repositories/stage.repository";

interface ResumeCurrentStageInput {
  stageId: string;
  tx: ITransaction;
}

type ResumeCurrentStageError = {
  type: "STAGE_NOT_FOUND" | "STAGE_NOT_PAUSED";
};

export class ResumeCurrentStageUseCase {
  constructor(private readonly stageRepository: StageRepository) {}

  async execute(
    input: ResumeCurrentStageInput,
  ): Promise<Result<void, ResumeCurrentStageError>> {
    // Get the stage
    const stage = await this.stageRepository.getStage(input.stageId, input.tx);
    if (!stage) {
      return err({ type: "STAGE_NOT_FOUND" });
    }

    // Verify the stage is paused for user input
    if (stage.status !== "PAUSED") {
      return err({ type: "STAGE_NOT_PAUSED" });
    }

    // Resume current stage - addAudienceToRunningTest handles completed AB tests
    await this.stageRepository.updateStageStatus(stage.id, "RUNNING", input.tx);

    return ok();
  }
}
