import { err, ok, Result } from "neverthrow";

import { getLinkedInApiClientFromOrganizationId } from "@kalos/linkedin-api";

import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { AbTestType } from "../../../abTest/internal/domain/abTestType.valueObject";
import { AbTestRepository } from "../../../abTest/internal/repositories/abTest.repository";
import { DataProviderService } from "../../../abTest/internal/services/abTestDataProviders/dataProvider.service";
import { RunWinnerService } from "../../../abTest/internal/services/runWinner.service";
import { ConversationCallToActionCopyRepository } from "../../../infrastructure/repositories/conversationCallToActionCopy.repository";
import { LinkedInAdAccountRepository } from "../../../infrastructure/repositories/linkedInAdAccount.repository";
import { LinkedInAdProgramRepository } from "../../../infrastructure/repositories/linkedInAdProgram.repository";
import { LinkedInAdSegmentRepository } from "../../../infrastructure/repositories/linkedInAdSegment.repository";
import { LinkedInCampaignRepository } from "../../../infrastructure/repositories/linkedInCampaign.repository";
import { LinkedInPostRepository } from "../../../infrastructure/repositories/linkedInPost.repository";
import { LinkedInSponsoredCreativeRepository } from "../../../infrastructure/repositories/linkedInSponsoredCreative.repository";
import { StageRepository } from "../../../infrastructure/repositories/stage.repository";
import { LinkedInService } from "../../../infrastructure/services/linkedIn.service";
import { MapLinkedInStateInputToSponsoredCreativesService } from "../../../linkedInStateOrchestrator/application/services/mapLinkedInStateInputToSponsoredCreatives.service";
import { SetLinkedInStateService } from "../../../linkedInStateOrchestrator/application/services/setLinkedInState.service";
import { LinkedInStateConfigRepository } from "../../../linkedInStateOrchestrator/repository/linkedInStateConfig.repository";
import { advertisingInngestClient } from "../../../utils/advertisingInngestClient";

interface ContinueToNextStageInput {
  stageId: string;
  organizationId: number;
  tx: ITransaction;
}

type ContinueToNextStageError = {
  type:
    | "STAGE_NOT_FOUND"
    | "STAGE_NOT_PAUSED"
    | "AD_SEGMENT_NOT_FOUND"
    | "AD_PROGRAM_NOT_FOUND"
    | "AD_ACCOUNT_NOT_FOUND"
    | "LINKEDIN_CLIENT_NOT_FOUND";
};

// Helper function to map stage type to AB test type
function getAbTestTypeFromStageType(stageType: string): AbTestType | null {
  switch (stageType) {
    case "audienceTest":
      return "audience";
    case "valuePropTest":
      return "valueProp";
    case "creativeTest":
      return "creative";
    case "conversationSubjectTest":
      return "conversationSubject";
    case "conversationMessageCopyTest":
      return "conversationMessageCopy";
    case "conversationCallToActionTest":
      return "conversationCallToAction";
    case "socialPostBodyCopyTest":
      return "socialPostBodyCopy";
    case "socialPostCallToActionTest":
      return "socialPostCallToAction";
    default:
      return null;
  }
}

export class ContinueToNextStageUseCase {
  constructor(
    private readonly stageRepository: StageRepository,
    private readonly adSegmentRepository: LinkedInAdSegmentRepository,
    private readonly adProgramRepository: LinkedInAdProgramRepository,
    private readonly adAccountRepository: LinkedInAdAccountRepository,
  ) {}

  async execute(
    input: ContinueToNextStageInput,
  ): Promise<Result<void, ContinueToNextStageError>> {
    // Get the stage
    const stage = await this.stageRepository.getStage(input.stageId, input.tx);
    if (!stage) {
      return err({ type: "STAGE_NOT_FOUND" });
    }

    // Verify the stage is paused for user input
    if (stage.status !== "PAUSED") {
      return err({ type: "STAGE_NOT_PAUSED" });
    }

    // Get related entities
    const adSegment = await this.adSegmentRepository.getOne(stage.adSegmentId);
    if (!adSegment) {
      return err({ type: "AD_SEGMENT_NOT_FOUND" });
    }

    const adProgram = await this.adProgramRepository.getOne(
      adSegment.linkedInAdProgramId,
    );
    if (!adProgram) {
      return err({ type: "AD_PROGRAM_NOT_FOUND" });
    }

    const adAccount = await this.adAccountRepository.getOneById(
      adProgram.linkedInAdAccountId,
    );
    if (!adAccount) {
      return err({ type: "AD_ACCOUNT_NOT_FOUND" });
    }

    // Before continuing to next stage, run winner service to deactivate contender if needed
    const abTestType = getAbTestTypeFromStageType(stage.stageType);
    if (abTestType) {
      const abTestRepository = new AbTestRepository();
      const abTest = await abTestRepository.getOne(
        stage.id,
        abTestType,
        input.tx,
      );

      if (
        abTest &&
        (abTest.status === "COMPLETED" ||
          abTest.status === "AUTO_RESOLVED" ||
          abTest.status === "USER_RESOLVED")
      ) {
        // Get LinkedIn client for the organization
        const linkedInClient = await getLinkedInApiClientFromOrganizationId(
          adAccount.organizationId,
        );
        if (!linkedInClient) {
          return err({ type: "LINKEDIN_CLIENT_NOT_FOUND" });
        }

        const linkedInService = new LinkedInService(linkedInClient);

        // Initialize RunWinnerService dependencies
        const dataProviderService = new DataProviderService({
          adSegmentRepository: this.adSegmentRepository,
          adProgramRepository: this.adProgramRepository,
        });

        const setLinkedInStateService = new SetLinkedInStateService({
          linkedInStateConfigRepository: new LinkedInStateConfigRepository(),
          linkedInCampaignRepository: new LinkedInCampaignRepository(),
          linkedInSponsoredCreativeRepository:
            new LinkedInSponsoredCreativeRepository(),
          adSegmentRepository: this.adSegmentRepository,
          adProgramRepository: this.adProgramRepository,
          linkedInAdAccountRepository: new LinkedInAdAccountRepository(),
          linkedInService: linkedInService,
        });

        const mapLinkedInStateInputToSponsoredCreativesService =
          new MapLinkedInStateInputToSponsoredCreativesService({
            linkedInPostRepository: new LinkedInPostRepository(),
            linkedInSponsoredCreativeRepository:
              new LinkedInSponsoredCreativeRepository(),
            linkedInCampaignRepository: new LinkedInCampaignRepository(),
            linkedInConversationCallToActionRepository:
              new ConversationCallToActionCopyRepository(),
            linkedInStateConfigRepository: new LinkedInStateConfigRepository(),
            linkedInService: linkedInService,
            linkedInAdAccountRepository: new LinkedInAdAccountRepository(),
            adSegmentRepository: this.adSegmentRepository,
            adProgramRepository: this.adProgramRepository,
          });

        const runWinnerService = new RunWinnerService(
          dataProviderService,
          setLinkedInStateService,
          mapLinkedInStateInputToSponsoredCreativesService,
          new LinkedInCampaignRepository(),
          new LinkedInSponsoredCreativeRepository(),
        );

        // Call run winner service to deactivate contender
        await runWinnerService.execute({
          abTest,
          adSegmentId: stage.adSegmentId,
          adProgramType: adProgram.adFormat.type,
          tx: input.tx,
        });
      }
    }

    // Mark current stage as finished
    await this.stageRepository.updateStageStatus(
      stage.id,
      "FINISHED",
      input.tx,
    );

    // Trigger next stage after transaction commits
    setImmediate(async () => {
      await advertisingInngestClient.send({
        name: "linkedin/stage.run",
        data: {
          adSegmentId: stage.adSegmentId,
          organizationId: adAccount.organizationId,
        },
      });
    });

    return ok();
  }
}
