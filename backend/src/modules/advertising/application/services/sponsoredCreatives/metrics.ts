import { getLinkedInApiClientFromOrganizationId } from "@kalos/linkedin-api";

import { LinkedInAdAccountRepository } from "../../../infrastructure/repositories/linkedInAdAccount.repository";
import { LinkedInAdProgramRepository } from "../../../infrastructure/repositories/linkedInAdProgram.repository";
import { LinkedInService } from "../../../infrastructure/services/linkedIn.service";

/**
 * Gets metrics data for LinkedIn sponsored creatives
 * @param sponsoredCreativeUrns Array of LinkedIn sponsored creative URNs
 * @param linkedInAdAccountId LinkedIn ad account ID
 * @param fromDate Optional start date for metrics
 * @param toDate Optional end date for metrics
 * @returns Object with metrics data keyed by sponsored creative URN
 */
export async function getMetricsForSponsoredCreatives(
  sponsoredCreativeUrns: string[],
  linkedInAdAccountId: string,
  fromDate: Date,
  toDate?: Date,
) {
  const linkedInAdAccountRepository = new LinkedInAdAccountRepository();
  const linkedInAdProgramRepository = new LinkedInAdProgramRepository();

  // Get the ad account
  const adAccount =
    await linkedInAdAccountRepository.getOneById(linkedInAdAccountId);

  if (!adAccount) {
    throw new Error("[getMetricsForSponsoredCreatives] Ad account not found");
  }

  // Get LinkedIn client for the organization
  const linkedInClient = await getLinkedInApiClientFromOrganizationId(
    adAccount.organizationId,
  );

  if (!linkedInClient) {
    throw new Error(
      "[getMetricsForSponsoredCreatives] LinkedIn client not found",
    );
  }

  // Get ad program to determine start date if not provided
  let startDate = fromDate;
  if (!startDate) {
    const adProgram =
      await linkedInAdProgramRepository.getOne(linkedInAdAccountId);
    if (adProgram) {
      startDate = adProgram.startDatetime;
    }
  }

  // Create LinkedIn service and get analytics
  const linkedInService = new LinkedInService(linkedInClient);
  const metricsForAds = await linkedInService.getAnalyticsForCreatives({
    sponsoredCreativeUrns,
    startDate: startDate, // Provide a default date if startDate is undefined
    endDate: toDate,
    timeGranularity: "ALL",
  });

  // Convert array to object keyed by URN for easier lookup
  const adDataWithMetrics: Record<string, any> = {};
  metricsForAds.forEach((adMetric) => {
    adDataWithMetrics[adMetric.sponsoredCreatieUrn] = { ...adMetric };
  });

  return adDataWithMetrics;
}
