import { LangfusePromptStorageService } from "../../../../core/infrastructure/services/langfusePromptStorage.service";
import { OpenAiCompletionService } from "../../../../core/infrastructure/services/openAiCompletion.service";
import { SocialPostAdCopyService } from "../../../domain/services/socialPostAdCopy.service";
import { SocialPostCopyRepository } from "../../../infrastructure/repositories/socialPostCopy.repository";
import { advertisingLangfuseClient } from "../../../utils/advertisingLangfuseClient";
import { IAdvertisingLlmCompletionsService } from "../../interfaces/infrastructure/services/advertisingLlmCompletions.service.interface";
import { ICopyVariantService } from "./copyVariant.service.interface";

export class SponsoredContentBodyCopyVariantService
  implements ICopyVariantService {
  constructor(
    private readonly advertisingLlmCompletionsService: IAdvertisingLlmCompletionsService,
    private readonly valuePropId: string,
    private readonly id: string,
  ) { }

  async *getCopyVariantStreaming(
    standardCopy: string,
    type: string,
  ): AsyncGenerator<{
    data: string;
    done: boolean;
  }> {
    const socialPostAdCopyService = new SocialPostAdCopyService(
      new SocialPostCopyRepository(),
    );
    const socialPostAdCopy = await socialPostAdCopyService.getOne({
      valuePropId: this.valuePropId,
      socialPostCopyType: type,
      status: "DRAFT",
    });
    if (socialPostAdCopy) {
      console.log("socialPostAdCopy", socialPostAdCopy);
      console.log({
        valuePropId: this.valuePropId,
        socialPostCopyType: type,
        status: "DRAFT",
      });
      yield {
        data: socialPostAdCopy.body,
        done: true,
      };
    } else {
      let content = "";
      const stream =
        this.advertisingLlmCompletionsService.generateSocialPostBodyCopyVarient(
          {
            standardCopy,
            type,
          },
          {
            lllmCompletions: new OpenAiCompletionService(),
            promptStorage: new LangfusePromptStorageService(
              advertisingLangfuseClient,
            ),
          },
        );

      for await (const each of stream) {
        content += each;
        yield {
          data: each,
          done: false,
        };
      }

      await socialPostAdCopyService.createOneOrUpdateOneIfExists({
        linkedInAdSegmentValuePropId: this.valuePropId,
        socialPostCopyType: type,
        title: "",
        body: content,
        status: "DRAFT",
      });

      yield {
        data: "",
        done: true,
      };
    }
  }
}
