import { ITransaction } from "../../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { Stage } from "../../../../domain/entites/stage";

export interface IStageRepository {
  createStage(stage: Stage, tx?: ITransaction): Promise<Stage>;
  getStage(id: string, tx?: ITransaction): Promise<Stage | null>;
  updateStageStatus(
    id: string,
    status: Stage["status"],
    tx?: ITransaction,
  ): Promise<Stage>;
  getCurrentRunningStage(
    adSegmentId: string,
    tx?: ITransaction,
  ): Promise<Stage | null>;
  getLastRanStage(
    adSegmentId: string,
    tx?: ITransaction,
  ): Promise<Stage | null>;
  getStagesForAdSegment(
    adSegmentId: string,
    tx?: ITransaction,
  ): Promise<Stage[]>;
  updateStage(stage: Stage, tx?: ITransaction): Promise<void>;
}
