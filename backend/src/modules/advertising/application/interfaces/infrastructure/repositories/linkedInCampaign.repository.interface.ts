import { ITransaction } from "../../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { LinkedInCampaign } from "../../../../domain/entites/linkedInCampaign";

export interface ILinkedInCampaignRepositoryInterface {
  createOne(input: LinkedInCampaign, tx?: ITransaction): Promise<void>;
  getOneById(
    audienceId: string,
    tx?: ITransaction,
  ): Promise<LinkedInCampaign | null>;
  getOneByLinkedInCampaignUrn(
    linkedInCampaignUrn: string,
    tx?: ITransaction,
  ): Promise<LinkedInCampaign | null>;
  updateStatusByUrn(
    linkedInCampaignUrn: string,
    status:
      | "ACTIVE"
      | "PAUSED"
      | "ARCHIVED"
      | "CANCELLED"
      | "DRAFT"
      | "PENDING_DELETION"
      | "REMOVED",
    tx?: ITransaction,
  ): Promise<void>;
  updateStatusById(
    id: string,
    status:
      | "ACTIVE"
      | "PAUSED"
      | "ARCHIVED"
      | "CANCELLED"
      | "DRAFT"
      | "PENDING_DELETION"
      | "REMOVED",
    tx?: ITransaction,
  ): Promise<void>;
  getManyByIds(ids: string[], tx?: ITransaction): Promise<LinkedInCampaign[]>;
  getManyForAdSegment(
    adSegmentId: string,
    tx?: ITransaction,
  ): Promise<LinkedInCampaign[]>;
  updateTotalBudgetById(
    id: string,
    totalBudget: number,
    tx?: ITransaction,
  ): Promise<void>;
  updateBudgetByUrn(
    linkedInCampaignUrn: string,
    totalBudget: number,
    tx?: ITransaction,
  ): Promise<void>;
}
