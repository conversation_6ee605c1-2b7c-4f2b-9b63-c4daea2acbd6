import { ITransaction } from "../../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { AdProgram } from "../../../../domain/entites/adProgram";
import { LinkedInCampaignGroup } from "../../../../domain/entites/linkedInCampaignGroup";

export interface ILinkedInCampaignGroupRepository {
  createOne(
    linkedInCampaignGroup: LinkedInCampaignGroup,
    tx?: ITransaction,
  ): Promise<LinkedInCampaignGroup>;
  getOneByAdSegmentId(
    id: string,
    tx?: ITransaction,
  ): Promise<LinkedInCampaignGroup | null>;
  getOneByLinkedInUrn(
    linkedInUrn: string,
    tx?: ITransaction,
  ): Promise<LinkedInCampaignGroup | null>;
  updateStatusById(
    id: string,
    status:
      | "ACTIVE"
      | "PAUSED"
      | "ARCHIVED"
      | "CANCELLED"
      | "DRAFT"
      | "PENDING_DELETION"
      | "REMOVED",
    tx?: ITransaction,
  ): Promise<void>;
  updateStatusByLinkedInUrn(
    linkedInUrn: string,
    status:
      | "ACTIVE"
      | "PAUSED"
      | "ARCHIVED"
      | "CANCELLED"
      | "DRAFT"
      | "PENDING_DELETION"
      | "REMOVED",
    tx?: ITransaction,
  ): Promise<void>;

  getManyByLinkedInAdSegmentIds(
    linkedInAdSegmentIds: string[],
    tx?: ITransaction,
  ): Promise<LinkedInCampaignGroup[]>;
  getForOrganizationInTimeRange(
    organizationId: number,
    timeRange: {
      startDate: Date;
      endDate: Date;
    },
    tx?: ITransaction,
  ): Promise<LinkedInCampaignGroup[]>;

  getAllForLinkedInAdAccountId(
    linkedInAdAccountId: string,
    tx?: ITransaction,
  ): Promise<LinkedInCampaignGroup[]>;

  getAllForLinkedInAdAccountIdWithAdProgram(
    linkedInAdAccountId: string,

    status?: (
      | "ACTIVE"
      | "PAUSED"
      | "ARCHIVED"
      | "CANCELLED"
      | "DRAFT"
      | "PENDING_DELETION"
      | "REMOVED"
    )[],
    tx?: ITransaction,
  ): Promise<
    { campaignGroup: LinkedInCampaignGroup; adProgram: AdProgram | null }[]
  >;
  /**
   * Update (overwrite) the total_budget column for the campaign-group row that
   * is keyed by the ad-segment ID.  Use this whenever campaigns are added to or
   * removed from a segment so the group ceiling stays in sync.
   */
  updateBudgetByAdSegmentId(
    adSegmentId: string,
    newTotalBudget: number,
    tx?: ITransaction,
  ): Promise<void>;

  updateBudgetByAdSegmentId(
    adSegmentId: string,
    totalBudget: number,
    tx?: ITransaction,
  ): Promise<void>;
}
