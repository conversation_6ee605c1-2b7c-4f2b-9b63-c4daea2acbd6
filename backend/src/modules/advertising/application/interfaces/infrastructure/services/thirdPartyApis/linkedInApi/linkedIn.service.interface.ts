import { Readable } from "stream";

import type { LinkedInApiClient } from "@kalos/linkedin-api";

import { TargetingCriteria } from "../../../../../../../../../../packages/linkedInApi/src/linkedInClient";
import { LinkedInAudienceTargetCriteria } from "../../../../../../domain/valueObjects/linkedinAudienceTargeting/linkedinTargetAudienceCriteria";
import {
  GetLinkedInApiAdAccountRequestDto,
  GetLinkedInApiAdAccountResponseDto,
} from "../../../../../dtos/serviceDtos/thirdPartyApi/linkedInApi/adAccount/getLinkedInApiAdAccount.dto";
import { GetLinkedInApiAdAccountIdsResponseDto } from "../../../../../dtos/serviceDtos/thirdPartyApi/linkedInApi/adAccount/getLinkedInApiAdAccountIds.dto";
import { GetLinkedInApiAdAccountsResponseDto } from "../../../../../dtos/serviceDtos/thirdPartyApi/linkedInApi/adAccount/getLinkedInApiAdAccounts.dto";
import {
  GetLinkedInApiLeadGenFormsForAccountRequestDto,
  GetLinkedInApiLeadGenFormsForAccountResponseDto,
} from "../../../../../dtos/serviceDtos/thirdPartyApi/linkedInApi/leadGenForm/getLinkedInApiLeadGenFormsForAccount.dto";

export interface ILinkedInService {
  getSenders(adAccountUrn: string): Promise<
    {
      urn: string;
      firstName: string;
      lastName: string;
    }[]
  >;
  getLinkedInAdAccount(
    input: GetLinkedInApiAdAccountRequestDto,
  ): Promise<GetLinkedInApiAdAccountResponseDto | null>;

  getLinkedInAdAccountIdsForUser(): Promise<GetLinkedInApiAdAccountIdsResponseDto>;
  createDestinationUrlConversation(data: {
    adAccountUrn: string;
    senderUrn: string;
    campaignUrn: string;
    body: string;
    subject: string;
    leadGenButtonText: string;
    destinationUrl: string;
  }): Promise<string>;
  getLinkedInAccountsForUser(): Promise<GetLinkedInApiAdAccountsResponseDto>;
  getLeadGenForms(
    input: GetLinkedInApiLeadGenFormsForAccountRequestDto,
  ): Promise<GetLinkedInApiLeadGenFormsForAccountResponseDto>;
  createCampaignGroup(input: {
    adAccountUrn: string;
    name: string;
    startDate: Date;
    endDate?: Date;
    status: "ACTIVE" | "DRAFT";
    objectiveType:
      | "BRAND_AWARENESS"
      | "ENGAGEMENT"
      | "JOB_APPLICANTS"
      | "LEAD_GENERATION"
      | "WEBSITE_CONVERSIONS"
      | "WEBSITE_VISIT"
      | "VIDEO_VIEWS"
      | null;
  }): Promise<string>;
  createCampaign(input: {
    adAccountUrn: string;
    campaignGroupUrn: string;
    budgetType: "DAILY" | "TOTAL";
    budget: number;
    name: string;
    startDate: Date;
    endDate?: Date;
    audienceTargets: LinkedInAudienceTargetCriteria;
    unitCost: number;
    manualBidding?: boolean;
    currencyCode?: string;
    objectiveType:
      | "BRAND_AWARENESS"
      | "ENGAGEMENT"
      | "LEAD_GENERATION"
      | "WEBSITE_CONVERSIONS"
      | "WEBSITE_VISIT"
      | "VIDEO_VIEWS"
      | "JOB_APPLICANTS";
    type?: "SPONSORED_UPDATES" | "SPONSORED_INMAILS";
  }): Promise<string>;

  updateCampaign(input: {
    adAccountUrn: string;
    campaignUrn: string;
    audienceTargets?: LinkedInAudienceTargetCriteria;
    budget?: number;
    unitCost?: number;
    startDate?: Date;
    endDate?: Date;
    status?: "ACTIVE" | "PAUSED";
  }): Promise<unknown>;

  uploadImage(input: {
    linkedInOrganizationUrn: string;
    body: Readable;
    type: "image" | "video";
    fileSizeBytes: number;
  }): Promise<string>;

  uploadDocument(input: {
    linkedInOrganizationUrn: string;
    body: Readable;
    type: "document";
    fileSizeBytes: number;
  }): Promise<string | null>;

  createInlineCreative(data: {
    adAccountUrn: string;
    campaignUrn: string;
    imageUrn: string;
    commentary: string;
    headline: string;
    linkedInOrgId: string;
    destinationUrl?: string;
    adFormUrn?: string;
    adName: string;
  }): Promise<string>;

  getAdTargetingFacets(): Promise<
    {
      facetName: string;
      adTargetingFacetUrn: string;
      availableEntityFinders: ("TYPEAHEAD" | "AD_TARGETING_FACET")[];
    }[]
  >;
  getAdTargetingFacetEntitesByTypehead(input: {
    facetUrn: string;
    query: string;
  }): Promise<
    {
      name: string;
      urn: string;
    }[]
  >;
  getAdTargetingFacetEntitiesForFacet(input: { facetUrn: string }): Promise<
    {
      name: string;
      urn: string;
    }[]
  >;

  getAdSegments(input: {
    adAccountUrn: string;
    types: "BULK" | "RETARGETING" | "MARKET_AUTOMATION";
    start: number;
    count: number;
  }): Promise<{
    paging: {
      start: number;
      count: number;
      total: number;
    };
    elements: {
      approximateMemberCount: number;
      created: {
        actor: string;
        time: number;
      };
      name: string;
      versionTag: string;
      lastModified: {
        actor: string;
        time: number;
      };
      id: number;
      type: "BULK" | "RETARGETING" | "MARKET_AUTOMATION";
      account: string;
      status:
        | "BUILDING"
        | "UPDATING"
        | "READY"
        | "FAILED"
        | "ARCHIVED"
        | "EXPIRED";
      description?: string;
    }[];
  }>;
  getSponsoredCreativeStatus(input: {
    adAccountUrn: string;
    adUrn: string;
  }): Promise<{
    review: { status: string };
    intendedStatus: "ACTIVE" | "PAUSED" | "DRAFT";
  }>;
  updateCampaignStatus(params: {
    adAccountUrn: string;
    campaignUrn: string;
    status: "ACTIVE" | "PAUSED" | "ARCHIVED" | "CANCELED" | "REMOVED";
  }): Promise<void>;

  updateCampaignBudget(input: {
    adAccountUrn: string;
    campaignUrn: string;
    budget: number;
    dailyBudget?: number;
  }): Promise<unknown>;
  updateSponsoredCreativeStatus(params: {
    adAccountUrn: string;
    adUrn: string;
    status: "ACTIVE" | "PAUSED" | "ARCHIVED" | "CANCELED" | "REMOVED";
  }): Promise<void>;

  getAnalyticsForCreatives(input: {
    sponsoredCreativeUrns: string[];
    startDate: Date;
    endDate?: Date;
    timeGranularity: "ALL" | "DAILY" | "MONTHLY" | "YEARLY";
  }): Promise<
    {
      sponsoredCreatieUrn: string;
      clicks: number;
      impressions: number;
      costInUsd: number;
      oneClickLeads: number;
      externalWebsiteConversions: number;
      videoViews: number;
      sends: number;
      opens: number;
      landingPageClicks: number;
      videoCompletions: number;
      videoFirstQuartileCompletions: number;
      videoMidpointCompletions: number;
      videoThirdQuartileCompletions: number;
      videoStarts: number;
      oneClickLeadFormOpens: number;
      totalEngagements: number;
      actionClicks: number;
    }[]
  >;
  getAnalyticsForCampaigns(input: {
    campaignUrns: string[];
    startDate: Date;
    endDate?: Date;
    timeGranularity: "ALL" | "DAILY" | "MONTHLY" | "YEARLY";
  }): Promise<
    {
      campaignUrn: string;
      clicks: number;
      impressions: number;
      costInUsd: number;
      oneClickLeads: number;
      externalWebsiteConversions: number;
      videoViews: number;
      sends: number;
      opens: number;
      landingPageClicks: number;
      videoCompletions: number;
      videoFirstQuartileCompletions: number;
      videoMidpointCompletions: number;
      videoThirdQuartileCompletions: number;
      videoStarts: number;
      oneClickLeadFormOpens: number;
      totalEngagements: number;
      actionClicks: number;
    }[]
  >;

  createLeadGenConversation(data: {
    adAccountUrn: string;
    leadGenFormUrn: string;
    senderUrn: string;
    campaignUrn: string;
    body: string;
    subject: string;
    leadGenButtonText: string;
    destinationUrl?: string;
  }): Promise<string>;

  getAudienceCount(input: {
    audienceTargets: LinkedInAudienceTargetCriteria;
    adAccountUrn?: string;
  }): Promise<number>;

  getSuggestedBidding(input: {
    adAccountUrn: string;
    bidType: "CPM" | "CPC" | "CPV";
    objectiveType: "VIDEO_VIEW" | "WEBSITE_VISIT" | "LEAD_GENERATION";
    campaignType: "TEXT_AD" | "SPONSORED_UPDATES" | "SPONSORED_INMAILS";
    audienceTargets:
      | {
          mode: "kalos";
          targets: LinkedInAudienceTargetCriteria;
        }
      | {
          mode: "linkedIn";
          targetingCriteria: TargetingCriteria;
        };
    optimizationTargetType?: string;
  }): Promise<{
    maxBid: number;
    minBid: number;
    suggestedBid: number;
    dailyBudgetMax: number;
    dailyBudgetMin: number;
    dailyBudgetSuggested: number;
  }>;

  getCampaign(input: { adAccountUrn: string; campaignUrn: string }): Promise<{
    account: string;
    associatedEntity: string;
    audienceExpansionEnabled: boolean;
    campaignGroup: string;
    changeAuditStamps: {
      created: {
        actor: string;
        time: number;
      };
      lastModified: {
        actor: string;
        time: number;
      };
    };
    connectedTelevisionOnly: boolean;
    costType: string;
    creativeSelection: string;
    dailyBudget: {
      amount: string;
      currencyCode: string;
    };
    format: string;
    id: number;
    locale: {
      country: string;
      language: string;
    };
    name: string;
    offsiteDeliveryEnabled: boolean;
    optimizationTargetType: string;
    runSchedule: {
      end: number;
      start: number;
    };
    servingStatuses: string[];
    objectiveType: string;
    status: string;
    storyDeliveryEnabled: boolean;
    targetingCriteria: {
      include: {
        and: {
          or: Record<string, string[]>;
        }[];
      };
    };
    test: boolean;
    totalBudget: {
      amount: string;
      currencyCode: string;
    };
    type: string;
    unitCost: {
      amount: string;
      currencyCode: string;
    };
    version: {
      versionTag: string;
    };
  }>;

  updateCampaignBid(input: {
    adAccountUrn: string;
    campaignUrn: string;
    bid: number;
  }): Promise<void>;

  associateConversationWithCampaign(
    conversationUrn: string,
    campaignUrn: string,
  ): Promise<void>;

  getConversionsForAdAccount(adAccountUrn: string): Promise<{
    elements: {
      postClickAttributionWindowSize: number;
      viewThroughAttributionWindowSize: number;
      created: number;
      type: string;
      enabled: boolean;
      name: string;
      lastModified: number;
      id: number;
      attributionType: string;
      conversionMethod: string;
      account: string;
    }[];
  }>;

  getSpendForCampaignGroups(input: {
    campaignGroupUrns: string[];
    startDate: Date;
    endDate?: Date;
  }): Promise<
    {
      campaignGroupUrn: string;
      costInUsd: number;
    }[]
  >;

  // New convenience methods for cleanup operations
  archiveCampaign(params: {
    adAccountUrn: string;
    campaignUrn: string;
  }): Promise<void>;

  cancelCampaign(params: {
    adAccountUrn: string;
    campaignUrn: string;
  }): Promise<void>;

  removeCampaign(params: {
    adAccountUrn: string;
    campaignUrn: string;
  }): Promise<void>;

  archiveSponsoredCreative(params: {
    adAccountUrn: string;
    adUrn: string;
  }): Promise<void>;

  cancelSponsoredCreative(params: {
    adAccountUrn: string;
    adUrn: string;
  }): Promise<void>;

  removeSponsoredCreative(params: {
    adAccountUrn: string;
    adUrn: string;
  }): Promise<void>;
}
