import { TRPCError } from "@trpc/server";
import { TransactionManagerService } from "../../../core/infrastructure/services/transcationManager.service";
import { ITransaction } from "../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { ILinkedInPostRepositoryInterface } from "../../application/interfaces/infrastructure/repositories/linkedInPost.repository.interface";
import { ILinkedInSponsoredCreativeRepository } from "../../application/interfaces/infrastructure/repositories/linkedInSponsoredCreative.repository.interface";
import { ILinkedInService } from "../../application/interfaces/infrastructure/services/thirdPartyApis/linkedInApi/linkedIn.service.interface";
import { ILinkedInAdAccountRepository } from "../../application/interfaces/infrastructure/repositories/linkedInAdAccount.repository.interface";
import { ILinkedInCampaignRepositoryInterface } from "../../application/interfaces/infrastructure/repositories/linkedInCampaign.repository.interface";
import { LinkedInSponsoredCreative } from "../entites/linkedInSponsoredCreative";
import { AdCreativeService } from "./adCreative.service";
import { LinkedInAdProgramCreativeRepository } from "../../infrastructure/repositories/linkedInAdProgramCreative.repository";
import { SocialPostCopyRepository } from "../../infrastructure/repositories/socialPostCopy.repository";
import { SocialPostCallToActionCopyRepository } from "../../infrastructure/repositories/socialPostCallToActionCopy.repository";
import { AdSegmentValuePropRepository } from "../../infrastructure/repositories/adSegmentValueProp.repository";
import { ConversationCopyRepository } from "../../infrastructure/repositories/conversationCopy.repository";
import { ConversationCallToActionCopyRepository } from "../../infrastructure/repositories/conversationCallToActionCopy.repository";
import { ConversationMessageCopyRepository } from "../../infrastructure/repositories/conversationMessageCopy.repository";
import { ConversationSubjectCopyRepository } from "../../infrastructure/repositories/conversationSubjectCopy.repository";
import { LinkedInAdSegmentRepository } from "../../infrastructure/repositories/linkedInAdSegment.repository";
import { LinkedInAdAudienceRepository } from "../../infrastructure/repositories/linkedInAdAudience.repository";
import { LinkedInSponsoredCreativeService } from "./linkedInSponsoredCreative.service";

export interface AdCopyingResult {
  success: boolean;
  copiedAdsCount: number;
  errors: string[];
  copiedCreativeUrns?: string[];
}

export class AdCopyingService {
  constructor(
    private readonly ctx: {
      linkedInSponsoredCreativeRepository: ILinkedInSponsoredCreativeRepository;
      linkedInSponsoredCreativeService: LinkedInSponsoredCreativeService;
      linkedInPostRepository: ILinkedInPostRepositoryInterface;
      linkedInService: ILinkedInService;
      linkedInAdAccountRepository: ILinkedInAdAccountRepository;
      adCreativeService: AdCreativeService;
      linkedInCampaignRepository: ILinkedInCampaignRepositoryInterface;
      linkedInAdProgramCreativeRepository: LinkedInAdProgramCreativeRepository;
      socialPostCopyRepository: SocialPostCopyRepository;
      socialPostCallToActionCopyRepository: SocialPostCallToActionCopyRepository;
      adSegmentValuePropRepository: AdSegmentValuePropRepository;
      conversationCopyRepository: ConversationCopyRepository;
      conversationCallToActionCopyRepository: ConversationCallToActionCopyRepository;
      conversationMessageCopyRepository: ConversationMessageCopyRepository;
      conversationSubjectCopyRepository: ConversationSubjectCopyRepository;
      linkedInAdSegmentRepository: LinkedInAdSegmentRepository;
      linkedInAdAudienceRepository: LinkedInAdAudienceRepository;
    },
  ) {}

  /**
   * Copy all sponsored creative ads from source campaign to destination campaign
   * @param sourceCampaignId Campaign ID to copy ads from
   * @param destinationCampaignUrn LinkedIn Campaign URN to copy ads to
   * @param organizationId Organization ID for ad account lookup
   * @param transactionManager Transaction manager
   * @param destinationUrl Optional destination URL for the copied ads
   * @returns Result with success status and copied ads count
   */
  async copySponsoredCreativeAds(
    sourceCampaignId: string,
    destinationCampaignUrn: string,
    organizationId: number,
    transactionManager: TransactionManagerService,
    destinationUrl?: string,
  ): Promise<AdCopyingResult> {
    const errors: string[] = [];
    let copiedAdsCount = 0;
    const copiedCreativeUrns: string[] = [];

    // Step 1: Read all necessary data from the database in a single transaction.
    const initialData = await transactionManager.startTransaction(async (tx: ITransaction) => {
      const sourceAds = await this.ctx.linkedInSponsoredCreativeRepository.getAllForCampaign(
        sourceCampaignId,
        tx,
      );

      if (sourceAds.length === 0) {
        return { sourceAds: [], adAccount: null, destinationUrl: undefined, destinationCampaign: null };
      }

      let finalDestinationUrl = destinationUrl;
      if (!finalDestinationUrl) {
        finalDestinationUrl = await this.getDestinationUrlFromSourceCampaign(sourceCampaignId, tx);
      }

      const adAccounts = await this.ctx.linkedInAdAccountRepository.getForOrganization(
        organizationId,
        tx,
      );
      if (!adAccounts || adAccounts.length === 0) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "LinkedIn ad account not found for organization",
        });
      }
      const adAccount = adAccounts[0]!;

      const destinationCampaign = await this.ctx.linkedInCampaignRepository.getOneByLinkedInCampaignUrn(
        destinationCampaignUrn,
        tx,
      );
      if (!destinationCampaign) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: `Campaign not found for URN: ${destinationCampaignUrn}`,
        });
      }

      return { sourceAds, adAccount, destinationUrl: finalDestinationUrl, destinationCampaign };
    });

    if (initialData.sourceAds.length === 0) {
      return { success: true, copiedAdsCount: 0, errors: ["No ads found in source campaign to copy"], copiedCreativeUrns: [] };
    }
    const { sourceAds, adAccount, destinationCampaign } = initialData;
    let finalDestinationUrl = initialData.destinationUrl;

    if (!adAccount || !destinationCampaign) {
        throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Failed to retrieve ad account or destination campaign.",
        });
    }

    // Step 2: Perform API calls and prepare DB objects (outside of transaction)
    const newCreativesToCreate: Omit<LinkedInSponsoredCreative, "id">[] = [];
    for (const sourceAd of sourceAds) {
      try {
        const newCreativeData = await this.prepareNewCreative(
          sourceAd,
          destinationCampaignUrn,
          adAccount,
          destinationCampaign,
          transactionManager,
          finalDestinationUrl,
        );

        if (newCreativeData.success && newCreativeData.creative) {
          newCreativesToCreate.push(newCreativeData.creative);
          copiedAdsCount++;
          
          // Track the LinkedIn URN for rollback purposes
          copiedCreativeUrns.push(newCreativeData.creative.linkedInSponseredCreativeUrn);
        } else {
          errors.push(`Failed to copy ad ${sourceAd.id}: ${newCreativeData.error}`);
        }
      } catch (error) {
        console.error(`Error copying ad ${sourceAd.id}:`, error);
        errors.push(`Error copying ad ${sourceAd.id}: ${error instanceof Error ? error.message : "Unknown error"}`);
      }
    }

    // Step 3: Write all new creatives to the database in a single transaction
    if (newCreativesToCreate.length > 0) {
      await transactionManager.startTransaction(async (tx) => {
        for (const creative of newCreativesToCreate) {
          await this.ctx.linkedInSponsoredCreativeService.createOne(creative, tx);
        }
      });
    }

    return {
      success: copiedAdsCount > 0 || sourceAds.length === 0,
      copiedAdsCount,
      errors,
      copiedCreativeUrns,
    };
  }

  private async prepareNewCreative(
    sourceAd: LinkedInSponsoredCreative,
    destinationCampaignUrn: string,
    adAccount: any,
    destinationCampaign: any,
    transactionManager: TransactionManagerService,
    destinationUrl?: string,
  ): Promise<{ success: boolean; error?: string; creative?: Omit<LinkedInSponsoredCreative, "id"> }> {
    if (sourceAd.content.type === "SPONSORED_CONTENT") {
        return await this.prepareSponsoredContentAd(sourceAd, destinationCampaignUrn, destinationCampaign, adAccount, transactionManager, destinationUrl);
    } else if (sourceAd.content.type === "SPONSORED_INMAIL") {
        return await this.prepareSponsoredInmailAd(sourceAd, destinationCampaignUrn, destinationCampaign, adAccount, transactionManager, destinationUrl);
    } else {
        return { success: false, error: `Unsupported ad type: ${(sourceAd.content as any).type}` };
    }
  }

  private async prepareSponsoredContentAd(
    sourceAd: LinkedInSponsoredCreative,
    destinationCampaignUrn: string,
    destinationCampaign: any,
    adAccount: any,
    transactionManager: TransactionManagerService,
    destinationUrl?: string,
  ): Promise<{ success: boolean; error?: string; creative?: Omit<LinkedInSponsoredCreative, "id"> }> {
    try {
        if (sourceAd.content.type !== "SPONSORED_CONTENT") {
            return { success: false, error: `Invalid ad type for sponsored content handler` };
        }
        const sponsoredContent = sourceAd.content;
        
        const data = await transactionManager.startTransaction(async tx => {
            const linkedInPost = await this.ctx.linkedInPostRepository.getOneById(
                sponsoredContent.postId,
                tx,
            );
            if (!linkedInPost) throw new Error(`Post not found for ad ${sourceAd.id}`);

            if (linkedInPost.content.type === "SINGLE_IMAGE" || 
                linkedInPost.content.type === "SINGLE_VIDEO" || 
                linkedInPost.content.type === "DOCUMENT") {

                const adCopyData = await this.getAdCopyContent(
                    linkedInPost.content.linkedInAdSegmentValuePropId,
                    linkedInPost.content.socialPostCopyType,
                    linkedInPost.content.socialPostCallToActionType,
                    tx,
                );

                // Get the ad creative file within the transaction
                const adCreativeFile = await this.getAdCreativeFile(linkedInPost.content.adCreativeId, tx);

                return { linkedInPost, adCopyData, adCreativeFile, isSupported: true };
            }
            return { linkedInPost, adCopyData: null, adCreativeFile: null, isSupported: false };
        });

        const { linkedInPost, adCopyData, adCreativeFile, isSupported } = data;

        if (!isSupported || !adCopyData || !adCreativeFile) {
            return { success: false, error: `Unsupported post format: ${linkedInPost.content.type}` };
        }
        
        if (linkedInPost.content.type === "SINGLE_IMAGE" || 
            linkedInPost.content.type === "SINGLE_VIDEO" || 
            linkedInPost.content.type === "DOCUMENT") {
            
            let imageUrn: string;
            
            try {
                imageUrn = await this.ctx.linkedInService.uploadImage({
                    linkedInOrganizationUrn: adAccount.linkedInOrganizationUrn,
                    body: adCreativeFile.Body,
                    type: linkedInPost.content.type === "SINGLE_VIDEO" ? "video" : "image",
                    fileSizeBytes: adCreativeFile.ContentLength || 1,
                });
            } catch (uploadError) {
                return { success: false, error: `Failed to upload creative: ${uploadError}` };
            }

            try {
                const creativeUrn = await this.ctx.linkedInService.createInlineCreative({
                    adAccountUrn: adAccount.linkedInAdAccountUrn,
                    campaignUrn: destinationCampaignUrn,
                    imageUrn: imageUrn,
                    commentary: adCopyData.body,
                    headline: adCopyData.headline,
                    linkedInOrgId: adAccount.linkedInOrganizationUrn,
                    destinationUrl: destinationUrl,
                    adName: `AB Test Copy - ${adCopyData.valuePropName.slice(0, 10)} - ${new Date().toISOString().split('T')[0]}`,
                });
    
                const newSponsoredCreative: Omit<LinkedInSponsoredCreative, "id"> = {
                    cmapaignId: destinationCampaign.linkedInAudienceId,
                    linkedInSponseredCreativeUrn: creativeUrn,
                    status: "PAUSED",
                    content: {
                        type: "SPONSORED_CONTENT",
                        postId: linkedInPost.id,
                    },
                };
    
                return { success: true, creative: newSponsoredCreative };
            } catch (creativeError) {
                return { success: false, error: `Failed to create LinkedIn creative: ${creativeError}` };
            }
        } else {
            return { success: false, error: `Unsupported post format: ${linkedInPost.content.type}` };
        }
    } catch (error) {
        return { success: false, error: `Error copying sponsored content ad: ${error instanceof Error ? error.message : 'Unknown error'}` };
    }
  }
  
  private async prepareSponsoredInmailAd(
    sourceAd: LinkedInSponsoredCreative,
    destinationCampaignUrn: string,
    destinationCampaign: any,
    adAccount: any,
    transactionManager: TransactionManagerService,
    destinationUrl?: string,
    ): Promise<{ success: boolean; error?: string; creative?: Omit<LinkedInSponsoredCreative, "id"> }> {
        try {
            if (sourceAd.content.type !== "SPONSORED_INMAIL") {
                return { success: false, error: `Invalid ad type for sponsored inmail handler` };
            }
            const sponsoredInmail = sourceAd.content;
            
            const { inmailCopyData, senderUrn } = await transactionManager.startTransaction(async tx => {
                const inmailCopyData = await this.getInmailCopyContent(
                    sponsoredInmail.conversationCallToActionId,
                    tx,
                );
                if (!inmailCopyData) throw new Error(`Inmail copy not found for ad ${sourceAd.id}`);

                const senderUrn = await this.getSenderUrnFromAdSegment(destinationCampaign.linkedInAudienceId, tx);
                if (!senderUrn) throw new Error(`Sender URN not found for destination campaign`);

                return { inmailCopyData, senderUrn };
            });

            try {
                let conversationUrn: string;
        
                if (inmailCopyData.leadGenFormUrn) {
                    conversationUrn = await this.ctx.linkedInService.createLeadGenConversation({
                        adAccountUrn: adAccount.linkedInAdAccountUrn,
                        leadGenFormUrn: inmailCopyData.leadGenFormUrn,
                        senderUrn: senderUrn,
                        campaignUrn: destinationCampaignUrn,
                        body: inmailCopyData.messageContent,
                        subject: inmailCopyData.subjectContent,
                        leadGenButtonText: inmailCopyData.callToActionContent,
                    });
                } else {
                    const finalDestinationUrl = inmailCopyData.destinationUrl || destinationUrl;
                    if (!finalDestinationUrl) {
                        return { success: false, error: `No destination URL available for inmail ad` };
                    }
        
                    conversationUrn = await this.ctx.linkedInService.createDestinationUrlConversation({
                        adAccountUrn: adAccount.linkedInAdAccountUrn,
                        destinationUrl: finalDestinationUrl,
                        senderUrn: senderUrn,
                        campaignUrn: destinationCampaignUrn,
                        body: inmailCopyData.messageContent,
                        subject: inmailCopyData.subjectContent,
                        leadGenButtonText: inmailCopyData.callToActionContent,
                    });
                }

                await this.ctx.linkedInService.updateSponsoredCreativeStatus({
                    adAccountUrn: adAccount.linkedInAdAccountUrn,
                    adUrn: conversationUrn,
                    status: "ACTIVE",
                });
        
                const newSponsoredCreative: Omit<LinkedInSponsoredCreative, "id"> = {
                    cmapaignId: destinationCampaign.linkedInAudienceId,
                    linkedInSponseredCreativeUrn: conversationUrn,
                    status: "ACTIVE",
                    content: {
                        type: "SPONSORED_INMAIL",
                        conversationCallToActionId: sourceAd.content.conversationCallToActionId,
                    },
                };
        
                return { success: true, creative: newSponsoredCreative };
            } catch (inmailError) {
                return { success: false, error: `Failed to create inmail ad: ${inmailError}` };
            }
        } catch (error) {
            return { success: false, error: `Error copying sponsored inmail ad: ${error instanceof Error ? error.message : 'Unknown error'}` };
        }
  }

  /**
   * Get ad creative file from storage
   */
  private async getAdCreativeFile(adProgramCreativeId: string, tx: ITransaction): Promise<{ Body: any; ContentLength?: number }> {
    // First, resolve the adProgramCreativeId to get the actual adCreativeId
    const adProgramCreative = await this.ctx.linkedInAdProgramCreativeRepository.getOne(adProgramCreativeId, tx);
    if (!adProgramCreative) {
      throw new Error("Ad program creative not found");
    }
    
    const fileStream = await this.ctx.adCreativeService.getAdCreativeFile(adProgramCreative.adCreativeId);
    if (!fileStream) {
      throw new Error("Ad creative file not found");
    }
    return { Body: fileStream };
  }

  /**
   * Get ad copy content for sponsored content ads (body, headline, value prop name)
   */
  private async getAdCopyContent(
    valuePropId: string,
    socialPostCopyType: string,
    socialPostCallToActionType: string,
    tx: ITransaction,
  ): Promise<{ body: string; headline: string; valuePropName: string }> {
    // Get social post copy
    const socialPostCopy = await this.ctx.socialPostCopyRepository.getOne({
      valuePropId,
      socialPostCopyType,
      status: "ACTIVE",
    }, tx);

    if (!socialPostCopy) {
      throw new Error("Social post copy not found");
    }

    // Get call to action copy
    const callToActionCopy = await this.ctx.socialPostCallToActionCopyRepository.getOne({
      valuePropId,
      socialPostCopyType: socialPostCallToActionType,
      status: "ACTIVE",
    }, tx);

    if (!callToActionCopy) {
      throw new Error("Call to action copy not found");
    }

    // Get value prop for the name
    const valueProp = await this.ctx.adSegmentValuePropRepository.getOne(valuePropId, "ACTIVE", tx);
    if (!valueProp) {
      throw new Error("Value prop not found");
    }

    return {
      body: socialPostCopy.body,
      headline: callToActionCopy.callToAction,
      valuePropName: valueProp.valueProp,
    };
  }

  /**
   * Get conversation copy content for SPONSORED_CONVERSATION ads
   */
  private async getConversationCopyContent(
    sponsoredConversationId: string,
    tx: ITransaction,
  ): Promise<{
    subjectContent: string;
    messageContent: string;
    callToActionContent: string;
    leadGenFormUrn?: string;
    destinationUrl?: string;
  } | null> {
    try {
      // Get the deprecated conversation copy (for SPONSORED_CONVERSATION)
      const conversationCopy = await this.ctx.conversationCopyRepository.getOneById(sponsoredConversationId, tx);
      
      if (!conversationCopy) {
        return null;
      }

      return {
        subjectContent: conversationCopy.title || "",
        messageContent: conversationCopy.body,
        callToActionContent: conversationCopy.callToAction,
        leadGenFormUrn: undefined, // deprecated conversation copy doesn't have leadGenFormUrn
        destinationUrl: undefined, // deprecated conversation copy doesn't have destinationUrl
      };
    } catch (error) {
      console.error("Error getting conversation copy content:", error);
      return null;
    }
  }

  /**
   * Get inmail copy content for SPONSORED_INMAIL ads
   */
  private async getInmailCopyContent(
    conversationCallToActionId: string,
    tx: ITransaction,
  ): Promise<{
    subjectContent: string;
    messageContent: string;
    callToActionContent: string;
    leadGenFormUrn?: string;
    destinationUrl?: string;
  } | null> {
    try {
      // Get call to action copy with related data
      const callToActionCopy = await this.ctx.conversationCallToActionCopyRepository.getOneById(conversationCallToActionId, "ACTIVE", tx);
      
      if (!callToActionCopy) {
        return null;
      }

      // Get message copy
      const messageCopy = await this.ctx.conversationMessageCopyRepository.getOneById(callToActionCopy.conversationMessageCopyId, "ACTIVE", tx);
      if (!messageCopy) {
        return null;
      }

      // Get subject copy
      const subjectCopy = await this.ctx.conversationSubjectCopyRepository.getOneById(callToActionCopy.conversationSubjectCopyId, "ACTIVE", tx);
      if (!subjectCopy) {
        return null;
      }

      return {
        subjectContent: subjectCopy.content,
        messageContent: messageCopy.content,
        callToActionContent: callToActionCopy.content,
        leadGenFormUrn: subjectCopy.leadGenForm || undefined,
        destinationUrl: subjectCopy.destinationUrl || undefined,
      };
    } catch (error) {
      console.error("Error getting inmail copy content:", error);
      return null;
    }
  }

  /**
   * Get sender URN from ad segment by audience ID
   */
  private async getSenderUrnFromAdSegment(
    audienceId: string,
    tx: ITransaction,
  ): Promise<string | null> {
    try {
      // Get the audience to find its ad segment
      const audience = await this.ctx.linkedInAdAudienceRepository.getOne(audienceId, tx);
      if (!audience) {
        console.log(`Audience not found for ID: ${audienceId}`);
        return null;
      }

      // Get the ad segment for this audience
      const adSegment = await this.ctx.linkedInAdSegmentRepository.getById(audience.linkedInAdSegmentId, tx);
      if (!adSegment) {
        console.log(`Ad segment not found for audience: ${audienceId}`);
        return null;
      }

      // Return the sender from the ad segment
      return adSegment.sender || null;
    } catch (error) {
      console.error("Error getting sender URN from ad segment:", error);
      return null;
    }
  }

  /**
   * Get destination URL from source campaign's ad segment
   */
  private async getDestinationUrlFromSourceCampaign(
    sourceCampaignId: string,
    tx: ITransaction,
  ): Promise<string | undefined> {
    try {
      // Get the source campaign to find its audience ID
      const sourceCampaign = await this.ctx.linkedInCampaignRepository.getOneById(sourceCampaignId, tx);
      if (!sourceCampaign) {
        console.log(`Source campaign ${sourceCampaignId} not found`);
        return undefined;
      }

      // Get the ad segment ID from the audience
      const audience = await this.ctx.linkedInAdAudienceRepository.getOne(sourceCampaign.linkedInAudienceId, tx);
      if (!audience) {
        console.log(`Audience not found for campaign ${sourceCampaignId}`);
        return undefined;
      }

      // Get the ad segment using the ad segment ID from the audience
      const adSegment = await this.ctx.linkedInAdSegmentRepository.getById(audience.linkedInAdSegmentId, tx);
      if (!adSegment) {
        console.log(`Ad segment not found for audience ${audience.id}`);
        return undefined;
      }

      // Return the destination URL from the ad segment
      if (adSegment.destinationUrl) {
        console.log(`Using destination URL from source campaign: ${adSegment.destinationUrl}`);
        return adSegment.destinationUrl;
      }

      console.log("No destination URL found in source campaign ad segment");
      return undefined;
    } catch (error) {
      console.error("Error getting destination URL from source campaign:", error);
      return undefined;
    }
  }
} 