import { ITransaction } from "../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { createUuid } from "../../../core/utils/uuid";
import { ILinkedInCampaignGroupRepository } from "../../application/interfaces/infrastructure/repositories/linkedInCampaignGroup.repository.interface";
import { LinkedInCampaignGroup } from "../entites/linkedInCampaignGroup";

type PartialBy<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

export class LinkedInCampaignGroupService {
  constructor(
    private readonly linkedInCampaignGroupRepository: ILinkedInCampaignGroupRepository,
  ) {}

  // Id is optional, if no id, create uuid for it
  async createOne(
    linkedInCampaignGroup: PartialBy<LinkedInCampaignGroup, "id">,
    tx?: ITransaction,
  ): Promise<LinkedInCampaignGroup> {
    if (!linkedInCampaignGroup.id) {
      linkedInCampaignGroup.id = createUuid();
    }

    return await this.linkedInCampaignGroupRepository.createOne(
      linkedInCampaignGroup as LinkedInCampaignGroup,
      tx,
    );
  }

  async updateStatusById(
    id: string,
    status:
      | "ACTIVE"
      | "PAUSED"
      | "ARCHIVED"
      | "CANCELLED"
      | "DRAFT"
      | "PENDING_DELETION"
      | "REMOVED",
    tx?: ITransaction,
  ): Promise<void> {
    return await this.linkedInCampaignGroupRepository.updateStatusById(
      id,
      status,
      tx,
    );
  }

  async updateStatusByLinkedInUrn(
    linkedInUrn: string,
    status:
      | "ACTIVE"
      | "PAUSED"
      | "ARCHIVED"
      | "CANCELLED"
      | "DRAFT"
      | "PENDING_DELETION"
      | "REMOVED",
    tx?: ITransaction,
  ): Promise<void> {
    return await this.linkedInCampaignGroupRepository.updateStatusByLinkedInUrn(
      linkedInUrn,
      status,
      tx,
    );
  }

  async getOneById(
    adSegmentId: string,
    tx?: ITransaction,
  ): Promise<LinkedInCampaignGroup | null> {
    return await this.linkedInCampaignGroupRepository.getOneByAdSegmentId(
      adSegmentId,
      tx,
    );
  }

  async getOneByLinkedInUrn(
    linkedInUrn: string,
    tx?: ITransaction,
  ): Promise<LinkedInCampaignGroup | null> {
    return await this.linkedInCampaignGroupRepository.getOneByLinkedInUrn(
      linkedInUrn,
      tx,
    );
  }

  async getAllForLinkedInAccountId(
    linkedInAdAccountId: string,
    tx?: ITransaction,
  ) {
    return await this.linkedInCampaignGroupRepository.getAllForLinkedInAdAccountId(
      linkedInAdAccountId,
      tx,
    );
  }

  async getAllForLinkedInAccountIdWithAdProgram(
    linkedInAdAccountId: string,
    status?: (
      | "ACTIVE"
      | "PAUSED"
      | "ARCHIVED"
      | "CANCELLED"
      | "DRAFT"
      | "PENDING_DELETION"
      | "REMOVED"
    )[],
    tx?: ITransaction,
  ) {
    return await this.linkedInCampaignGroupRepository.getAllForLinkedInAdAccountIdWithAdProgram(
      linkedInAdAccountId,
      status,
      tx,
    );
  }

  async updateBudgetByAdSegmentId(
    adSegmentId: string,
    newTotalBudget: number,
    tx?: ITransaction,
  ): Promise<void> {
    return await this.linkedInCampaignGroupRepository.updateBudgetByAdSegmentId(
      adSegmentId,
      newTotalBudget,
      tx,
    );
  }
}
