import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { IAbTestRepository } from "../../../application/interfaces/infrastructure/repositories/abTest.repository.interface";
import { IStageRepository } from "../../../application/interfaces/infrastructure/repositories/stage.repository.interface";
import { AbTestRepository } from "../../../infrastructure/repositories/abTest.repository";
import { AbTestRoundDayRepository } from "../../../infrastructure/repositories/abTestRoundDay.repository";
import { StageRepository } from "../../../infrastructure/repositories/stage.repository";
import { AbTest } from "../../entites/abTest";
import { Stage } from "../../entites/stage";

export class AbTestService {
  constructor(
    private readonly ctx: {
      abTestRepository: IAbTestRepository;
    },
  ) {}

  async createAbTest(stageId: string, type: AbTest["type"], tx?: ITransaction) {
    const abTest = await this.ctx.abTestRepository.createOne(
      {
        status: "NOT_STARTED",
        stageId: stageId,
        type: type,
      },
      tx,
    );
    return abTest;
  }

  async getAbTest(abTestId: string, type: AbTest["type"], tx?: ITransaction) {
    const abTest = await this.ctx.abTestRepository.getOne(abTestId, type, tx);
    return abTest;
  }

  async setAbTestStausToInProgress(
    abTestId: string,
    stage: Stage,
    type: AbTest["type"],
    tx?: ITransaction,
  ) {
    if (stage.status !== "RUNNING") {
      throw new Error("Stage is not running");
    }

    const abTest = await this.ctx.abTestRepository.getOne(abTestId, type, tx);
    if (!abTest) {
      throw new Error("AbTest not found");
    }

    if (abTest.status !== "NOT_STARTED") {
      throw new Error(
        `AbTest cannot be set to in progress because it is not "NOT_STARTED". It is currently ${abTest.status}`,
      );
    }

    if (abTest.stageId !== stage.id) {
      throw new Error("AbTest ID does not match stage ID");
    }

    await this.ctx.abTestRepository.updateOne(
      { ...abTest, status: "IN_PROGRESS" },
      tx,
    );
  }

  async setAbTestStausToCompleted(
    abTestId: string,
    winnerId: string,
    type: AbTest["type"],
    tx?: ITransaction,
  ) {
    const abTest = await this.ctx.abTestRepository.getOne(abTestId, type, tx);
    if (!abTest) {
      throw new Error("AbTest not found");
    }
    if (abTest.status !== "IN_PROGRESS") {
      throw new Error("AbTest is not in progress");
    }
    await this.ctx.abTestRepository.updateOne(
      { ...abTest, status: "COMPLETED", winnerId: winnerId },
      tx,
    );
  }

  /**
   * Domain logic: Maps A/B test types to their corresponding stage types
   */
  getExpectedStageType(abTestType: string): string {
    const typeMap: Record<string, string> = {
      audience: "audienceTest",
      valueProp: "valuePropTest",
      creative: "creativeTest",
      conversationSubject: "conversationSubjectTest",
      conversationMessageCopy: "conversationMessageCopyTest",
      conversationCallToActionCopy: "conversationCallToActionCopyTest",
      socialPostBodyCopy: "socialPostBodyCopyTest",
      socialPostCallToActionCopy: "socialPostCallToActionCopyTest",
    };
    return typeMap[abTestType] || `${abTestType}Test`;
  }

  /**
   * Domain logic: Determines the current best performer based on completed rounds and business rules
   */
  determineCurrentBestPerformer(rounds: any[], abTest: any): string {
    const completedRounds = rounds
      .filter(round => round.status === "COMPLETED")
      .sort((a, b) => a.roundIndex - b.roundIndex);

    if (completedRounds.length === 0) {
      // No rounds completed - use the most recent audience or first available
      const mostRecentRound = rounds
        .sort((a, b) => b.roundIndex - a.roundIndex)[0];
      
      return mostRecentRound ? mostRecentRound.contenderId : rounds[0]?.currentBestId || "";
    } else {
      // Use winner of the most recent completed round
      const lastCompletedRound = completedRounds[completedRounds.length - 1];
      if (lastCompletedRound?.winner) {
        return lastCompletedRound.winner === "CURRENT_BEST" 
          ? lastCompletedRound.currentBestId 
          : lastCompletedRound.contenderId;
      } else {
        // Use abTest winnerId if it exists (when test is completed in any form)
        const winnerId = abTest.winnerId && abTest.winnerId !== null ? abTest.winnerId : undefined;
        return winnerId || lastCompletedRound?.currentBestId || "";
      }
    }
  }
}
