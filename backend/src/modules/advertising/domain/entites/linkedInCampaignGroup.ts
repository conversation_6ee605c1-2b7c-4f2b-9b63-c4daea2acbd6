import { z } from "zod";

import { Entity } from "../../../../helpers/entity";
import { linkedInCampaignGroupUrnSchema } from "../valueObjects/linkedInUrns/linkedInCampaignGroupUrn";

export const linkedInCampaignGroup = Entity(
  z.object({
    id: z.string().uuid(),
    linkedInAdSegmentId: z.string().uuid().nullable(),
    linkedInCampaignGroupUrn: linkedInCampaignGroupUrnSchema,
    name: z.string(),
    totalBudget: z.number(),
    status: z.enum([
      "ACTIVE",
      "PAUSED",
      "ARCHIVED",
      "CANCELLED",
      "DRAFT",
      "PENDING_DELETION",
      "REMOVED",
    ]),
    createdFromLinkedIn: z.boolean().default(false).optional(),
    linkedInAdAccountId: z.string().uuid().nullable(),
    objectiveType: z
      .enum([
        "BRAND_AWARENESS",
        "ENGAGEMENT",
        "JOB_APPLICANTS",
        "LEAD_GENERATION",
        "WEBSITE_CONVERSIONS",
        "WEBSITE_VISITS",
        "VIDEO_VIEWS",
      ])
      .nullable(),
    startDatetime: z.date().nullable(),
  }),
);

export type LinkedInCampaignGroup = ReturnType<typeof linkedInCampaignGroup>;
