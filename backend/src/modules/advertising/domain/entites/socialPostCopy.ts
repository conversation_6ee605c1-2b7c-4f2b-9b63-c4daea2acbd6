import { z } from "zod";

import { Entity } from "../../../../helpers/entity";

export const SocialPostCopy = Entity(
  z.object({
    id: z.string().uuid(),
    linkedInAdSegmentValuePropId: z.string().uuid(),
    title: z.string(),
    body: z.string(),
    socialPostCopyType: z.string(),
    leadGenFormUrn: z.string().nullable().optional(),
    status: z.enum(["DRAFT", "ACTIVE", "ARCHIVED"]),
  }),
);

export type SocialPostCopy = ReturnType<typeof SocialPostCopy>;
