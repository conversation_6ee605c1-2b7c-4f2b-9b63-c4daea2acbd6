import { z } from "zod";

export const stepStepSchema = z.object({
  id: z.string().uuid(),
  stageId: z.string().uuid(),
  stepName: z.string(),
  stepStatus: z.enum([
    "NOT_STATED",
    "PROVISIONING",
    "RUNNING",
    "INTERRUPTED",
    "FATAL_PROBLEM",
    "FINISHING",
    "FINISHED",
    "PAUSED",
  ]),
  onStartOutputType: z
    .enum(["CALL_STEP", "CALL_END_WORKFLOW", "CALL_END_STEP", "CALL_CONFIG"])
    .nullable()
    .optional(),
  onEndOutputType: z
    .enum(["CALL_STEP", "CALL_END_WORKFLOW", "CALL_END_STEP", "CALL_CONFIG"])
    .nullable()
    .optional(),
});

export const StageStep = (data: z.infer<typeof stepStepSchema>) => {
  return stepStepSchema.parse(data);
};

export type StageStep = z.infer<typeof stepStepSchema>;
