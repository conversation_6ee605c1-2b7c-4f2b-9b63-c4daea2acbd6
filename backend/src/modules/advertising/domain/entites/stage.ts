import { z } from "zod";

export const stageSchema = z.object({
  id: z.string().uuid(),
  adSegmentId: z.string().uuid(),
  stageType: z.enum([
    "audienceTest",
    "valuePropTest",
    "creativeTest",
    "adCopyTest",
    "conversationSubjectTest",
    "socialPostBodyCopyTest",
    "socialPostCallToActionTest",
    "conversationCallToActionTest",
    "conversationMessageCopyTest",
  ]),
  index: z.number(),
  status: z.enum([
    "NOT_STATED",
    "PROVISIONING",
    "RUNNING",
    "INTERRUPTED",
    "FATAL_PROBLEM",
    "FINISHED",
    "PAUSED",
  ]),
});

export const Stage = (data: z.infer<typeof stageSchema>) => {
  stageSchema.parse(data);
  return data;
};

export type Stage = z.infer<typeof stageSchema>;
