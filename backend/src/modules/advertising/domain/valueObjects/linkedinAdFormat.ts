import { z } from "zod";

import { linkedInAdTypeSchema } from "./linkedInAdType";

const linkedInAdFormatTypeSchema = z.enum([
  "SPONSORED_CONTENT",
  "SPONSORED_CONVERSATION",
  "SPONSORED_INMAIL",
]);

export type LinkedInAdFormatType = z.infer<typeof linkedInAdFormatTypeSchema>;

export const linkedInSponsoredContentAdFormatSchema = z.enum([
  "SINGLE_IMAGE",
  "CAROUSEL_IMAGE",
  "VIDEO",
  "DOCUMENT",
]);

export type SponsoredContentAdFormat = z.infer<
  typeof linkedInSponsoredContentAdFormatSchema
>;

const linkedInSingleImageAdFormatShema = z.object({
  type: z.literal(linkedInAdTypeSchema.enum.SPONSORED_CONTENT),
  format: z.literal(linkedInSponsoredContentAdFormatSchema.enum.SINGLE_IMAGE),
});
export type LinkedInSingleImageAdFormat = z.infer<
  typeof linkedInSingleImageAdFormatShema
>;

const linkedInCarouselImageAdFormatSchema = z.object({
  type: z.literal(linkedInAdTypeSchema.enum.SPONSORED_CONTENT),
  format: z.literal(linkedInSponsoredContentAdFormatSchema.enum.CAROUSEL_IMAGE),
});

export type LinkedInCarouselImageAdFormat = z.infer<
  typeof linkedInCarouselImageAdFormatSchema
>;

const linkedInSingleVideoAdFormatSchema = z.object({
  type: z.literal(linkedInAdTypeSchema.enum.SPONSORED_CONTENT),
  format: z.literal(linkedInSponsoredContentAdFormatSchema.enum.VIDEO),
});

export type LinkedInSingleVideoAdFormat = z.infer<
  typeof linkedInSingleVideoAdFormatSchema
>;

const linkedInDocumentAdFormatSchema = z.object({
  type: z.literal(linkedInAdTypeSchema.enum.SPONSORED_CONTENT),
  format: z.literal(linkedInSponsoredContentAdFormatSchema.enum.DOCUMENT),
});

export type LinkedInDocumentAdFormat = z.infer<
  typeof linkedInDocumentAdFormatSchema
>;

const linkedInSponsoredConversationAdFormatSchema = z.object({
  type: z.literal(linkedInAdTypeSchema.enum.SPONSORED_CONVERSATION),
  format: z.literal("SPONSORED_CONVERSATION"),
});

const linkedInSponsoredInmailAdFormatSchema = z.object({
  type: z.literal(linkedInAdTypeSchema.enum.SPONSORED_INMAIL),
  format: z.literal("SPONSORED_INMAIL"),
});

export const linkedInAdFormatSchema = z.discriminatedUnion("format", [
  linkedInSingleImageAdFormatShema,
  linkedInCarouselImageAdFormatSchema,
  linkedInSingleVideoAdFormatSchema,
  linkedInDocumentAdFormatSchema,
  linkedInSponsoredConversationAdFormatSchema,
  linkedInSponsoredInmailAdFormatSchema,
]);

export type LinkedInAdFormat = z.infer<typeof linkedInAdFormatSchema>;
