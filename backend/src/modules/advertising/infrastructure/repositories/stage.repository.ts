import { and, desc, eq } from "drizzle-orm";

import { db, Transaction } from "../../../../database/db";
import { stageTable } from "../../../../database/schemas/advertising/stage.table";
import { ITransaction } from "../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { IStageRepository } from "../../application/interfaces/infrastructure/repositories/stage.repository.interface";
import { Stage } from "../../domain/entites/stage";

export class StageRepository implements IStageRepository {
  async getStagesForAdSegment(
    adSegmentId: string,
    tx?: Transaction,
  ): Promise<Stage[]> {
    const invoker = tx ?? db;
    const res = await invoker
      .select()
      .from(stageTable)
      .where(eq(stageTable.linkedInAdSegmentid, adSegmentId));

    return res.map((stage) => ({
      id: stage.id,
      adSegmentId: stage.linkedInAdSegmentid,
      stageType: stage.stageType as "audienceTest" | "valuePropTest",
      index: stage.index,
      status: stage.status,
    }));
  }
  async createStage(stage: Stage, tx?: Transaction): Promise<Stage> {
    const invoker = tx ?? db;
    await invoker
      .insert(stageTable)
      .values({
        id: stage.id,
        linkedInAdSegmentid: stage.adSegmentId,
        index: stage.index,
        stageType: stage.stageType,
        status: stage.status,
      })
      .returning();
    return stage;
  }
  async getStage(id: string, tx?: Transaction): Promise<Stage | null> {
    const invoker = tx ?? db;
    const res = await invoker
      .select()
      .from(stageTable)
      .where(eq(stageTable.id, id));
    if (!res[0]) {
      return null;
    }
    return {
      id: res[0].id,
      adSegmentId: res[0].linkedInAdSegmentid,
      stageType: res[0].stageType as "audienceTest" | "valuePropTest",
      index: res[0].index,
      status: res[0].status,
    };
  }
  async updateStageStatus(
    id: string,
    status: Stage["status"],
    tx?: Transaction,
  ): Promise<Stage> {
    const invoker = tx ?? db;
    const res = await invoker
      .update(stageTable)
      .set({ status })
      .where(eq(stageTable.id, id))
      .returning();
    if (!res[0]) {
      throw new Error("Stage not found");
    }
    return {
      id: res[0].id,
      adSegmentId: res[0].linkedInAdSegmentid,
      index: res[0].index,
      stageType: res[0].stageType as "audienceTest" | "valuePropTest",
      status,
    };
  }

  async getLastRanStage(
    adSegmentId: string,
    tx?: Transaction,
  ): Promise<Stage | null> {
    const invoker = tx ?? db;

    const res = await invoker
      .select()
      .from(stageTable)
      .where(
        and(
          eq(stageTable.linkedInAdSegmentid, adSegmentId),
          eq(stageTable.status, "FINISHED"),
        ),
      )
      .orderBy(desc(stageTable.index))
      .limit(1);

    if (!res[0]) {
      return null;
    }

    return {
      id: res[0].id,
      adSegmentId: res[0].linkedInAdSegmentid,
      stageType: res[0].stageType as "audienceTest" | "valuePropTest",
      index: res[0].index,
      status: res[0].status,
    };
  }

  async getCurrentRunningStage(
    adSegmentId: string,
    tx?: Transaction,
  ): Promise<Stage | null> {
    const invoker = tx ?? db;
    const res = await invoker
      .select()
      .from(stageTable)
      .where(
        and(
          eq(stageTable.linkedInAdSegmentid, adSegmentId),
          eq(stageTable.status, "RUNNING"),
        ),
      );
    if (!res[0]) {
      return null;
    }
    return {
      id: res[0].id,
      adSegmentId: res[0].linkedInAdSegmentid,
      stageType: res[0].stageType as "audienceTest" | "valuePropTest",
      index: res[0].index,
      status: res[0].status,
    };
  }

  async updateStage(stage: Stage, tx?: Transaction): Promise<void> {
    const invoker = tx ?? db;
    await invoker
      .update(stageTable)
      .set(stage)
      .where(eq(stageTable.id, stage.id));
  }
}
