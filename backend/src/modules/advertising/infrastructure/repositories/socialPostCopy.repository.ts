import { and, eq, inArray, sql } from "drizzle-orm";

import { db } from "../../../../database/db";
import { Transaction } from "../../../../database/dbTransactionType";
import { socialPostCopyTable } from "../../../../database/schemas/advertising/socialPostCopy.table";
import { createUuid } from "../../../core/utils/uuid";
import { ISocialPostCopyRepository } from "../../application/interfaces/infrastructure/repositories/socialPostCopy.repository.interface";
import { SocialPostCopy } from "../../domain/entites/socialPostCopy";

export class SocialPostCopyRepository implements ISocialPostCopyRepository {
  async createOneOrUpdateOneIfExists(
    input: Omit<SocialPostCopy, "id">,
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    await invoker
      .insert(socialPostCopyTable)
      .values({
        id: createUuid(),
        title: input.title || "",
        body: input.body,
        linkedInAdSegmentValuePropId: input.linkedInAdSegmentValuePropId,
        socialPostCopyType: input.socialPostCopyType,
        leadGenFormUrn: input.leadGenFormUrn,
      })
      .onConflictDoUpdate({
        target: [
          socialPostCopyTable.linkedInAdSegmentValuePropId,
          socialPostCopyTable.socialPostCopyType,
        ],
        set: {
          title: input.title || "",
          body: input.body,
          leadGenFormUrn: input.leadGenFormUrn,
        },
      });
  }
  async getOne(
    input: {
      valuePropId: string;
      socialPostCopyType: string;
      status: "DRAFT" | "ACTIVE" | "ARCHIVED";
    },
    tx?: Transaction,
  ): Promise<SocialPostCopy | null> {
    const invoker = tx ?? db;
    const socialPostCopy = await invoker
      .select()
      .from(socialPostCopyTable)
      .where(
        and(
          eq(
            socialPostCopyTable.linkedInAdSegmentValuePropId,
            input.valuePropId,
          ),
          eq(socialPostCopyTable.socialPostCopyType, input.socialPostCopyType),
        ),
      );
    if (!socialPostCopy[0]) {
      return null;
    }
    return SocialPostCopy(socialPostCopy[0]);
  }

  async getAllForLAdSegmentValueProp(
    adSegmentValuePropId: string,
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
    tx?: Transaction,
  ): Promise<SocialPostCopy[]> {
    const invoker = tx ?? db;
    const socialPostCopies = await invoker
      .select()
      .from(socialPostCopyTable)
      .where(
        and(
          eq(
            socialPostCopyTable.linkedInAdSegmentValuePropId,
            adSegmentValuePropId,
          ),
          eq(socialPostCopyTable.status, status),
        ),
      );
    return socialPostCopies.map((socialPostCopy) =>
      SocialPostCopy(socialPostCopy),
    );
  }

  async deleteManyForLinkedInAdSegmentValueProps(
    input: {
      linkedInAdSegmentValuePropIds: string[];
      status: "DRAFT" | "ACTIVE" | "ARCHIVED";
    },
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    await invoker
      .delete(socialPostCopyTable)
      .where(
        and(
          inArray(
            socialPostCopyTable.linkedInAdSegmentValuePropId,
            input.linkedInAdSegmentValuePropIds,
          ),
          eq(socialPostCopyTable.status, input.status),
        ),
      );
  }
  async updateLeadGenFormUrn(
    input: {
      valuePropId: string;
      status: "DRAFT" | "ACTIVE" | "ARCHIVED";
      leadGenFormUrn: string;
    },
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    await invoker
      .update(socialPostCopyTable)
      .set({ leadGenFormUrn: input.leadGenFormUrn })
      .where(
        and(
          eq(
            socialPostCopyTable.linkedInAdSegmentValuePropId,
            input.valuePropId,
          ),
          eq(socialPostCopyTable.status, input.status),
        ),
      );
  }

  async updateManyToActive(ids: string[], tx?: Transaction): Promise<void> {
    const invoker = tx ?? db;
    if (ids.length == 0) {
      return;
    }
    await invoker
      .update(socialPostCopyTable)
      .set({ status: "ACTIVE" })
      .where(
        and(
          inArray(socialPostCopyTable.id, ids),
          eq(socialPostCopyTable.status, "DRAFT"),
        ),
      );
  }

  async deleteManyByTypeForValueProp(
    input: {
      valuePropId: string;
      types: string[];
      status: "DRAFT" | "ACTIVE" | "ARCHIVED";
    },
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;

    if (input.types.length == 0) {
      return;
    }

    const things = await invoker
      .select()
      .from(socialPostCopyTable)
      .where(
        and(
          eq(
            socialPostCopyTable.linkedInAdSegmentValuePropId,
            input.valuePropId,
          ),
          inArray(socialPostCopyTable.socialPostCopyType, input.types),
          eq(socialPostCopyTable.status, input.status),
        ),
      );
    console.log("input", input);
    console.log("things", things);
    await invoker
      .delete(socialPostCopyTable)
      .where(
        and(
          eq(
            socialPostCopyTable.linkedInAdSegmentValuePropId,
            input.valuePropId,
          ),
          inArray(socialPostCopyTable.socialPostCopyType, input.types),
          eq(socialPostCopyTable.status, input.status),
        ),
      );

    const thing2s = await invoker
      .select()
      .from(socialPostCopyTable)
      .where(
        and(
          eq(
            socialPostCopyTable.linkedInAdSegmentValuePropId,
            input.valuePropId,
          ),
          inArray(socialPostCopyTable.socialPostCopyType, input.types),
          eq(socialPostCopyTable.status, input.status),
        ),
      );
  }

  async getOneById(
    id: string,
    tx?: Transaction,
  ): Promise<SocialPostCopy | null> {
    const invoker = tx ?? db;
    const socialPostCopy = await invoker
      .select()
      .from(socialPostCopyTable)
      .where(eq(socialPostCopyTable.id, id));
    if (!socialPostCopy[0]) {
      return null;
    }
    return SocialPostCopy(socialPostCopy[0]);
  }
}
