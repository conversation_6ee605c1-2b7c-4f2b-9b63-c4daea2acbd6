import { organizationRoute } from "../../../../trpc/trpc";
import { TransactionManagerService } from "../../../core/infrastructure/services/transcationManager.service";
import { addAudienceToRunningTestDtoSchema } from "../../application/dtos/controllerDtos/abTest/addAudienceToRunningTest.dto";
import { getAdMetricsDtoSchema } from "../../application/dtos/controllerDtos/abTest/getAdMetrics.dto";
import { getCurrentRunningAbTestDtoSchema } from "../../application/dtos/controllerDtos/abTest/getCurrentRunningAbTest..dto";
import { getCurrentRunningAbTestsBatchDtoSchema } from "../../application/dtos/controllerDtos/abTest/getCurrentRunningAbTests.dto";
import { getPastRanAbTestsDtoSchema } from "../../application/dtos/controllerDtos/abTest/getPastRanAbTests.dto";
import { getUpcomingRunningAbTestsDtoSchema } from "../../application/dtos/controllerDtos/abTest/getUpcomingRunningAbTests.dto";
import { AddAudienceToRunningTestUseCase } from "../../application/useCase/abTest/addAudienceToRunningTest.useCase";
import { GetAdMetricsUseCase } from "../../application/useCase/abTest/getAdMetrics.useCase";
import { GetCurrentRunningAbTestUseCase } from "../../application/useCase/abTest/getCurrentRunningAbTest.useCase";
import { GetCurrentRunningAbTestsBatchUseCase } from "../../application/useCase/abTest/getCurrentRunningAbTestsBatch.useCase";
import { GetPastRanAbTestsUseCase } from "../../application/useCase/abTest/getPastRanAbTests.useCase";
import { GetUpcomingRunningAbTestsUseCase } from "../../application/useCase/abTest/getUpcomingRunningAbTests.useCase";
import { AdCreativeService } from "../../domain/services/adCreative.service";
import { AdSegmentValuePropService } from "../../domain/services/adSegmentValueProp.service";
import { ConversationCallToActionCopyService } from "../../domain/services/conversationCallToActionCopy.service";
import { ConversationMessageCopyService } from "../../domain/services/conversationMessageCopy.service";
import { ConversationSubjectCopyService } from "../../domain/services/conversationSubjectCopy.service";
import { LinkedInAdProgramAdCreativeService } from "../../domain/services/linkedInAdProgramAdCreative.service";
import { LinkedInAdSegmentSocialPostBaseCopyService } from "../../domain/services/linkedInAdSegmentSocialPostBaseCopy.service";
import { SocialPostAdCopyService } from "../../domain/services/socialPostAdCopy.service";
import { SocialPostCallToActionCopyService } from "../../domain/services/socialPostCallToActionCopy.service";
import { AdCreativeRepository } from "../../infrastructure/repositories/adCreative.repository";
import { AdSegmentValuePropRepository } from "../../infrastructure/repositories/adSegmentValueProp.repository";
import { AdSegmentValuePropCreativeRepository } from "../../infrastructure/repositories/adSegmentValuePropCreative.repository";
import { ConversationCallToActionCopyRepository } from "../../infrastructure/repositories/conversationCallToActionCopy.repository";
import { ConversationMessageCopyRepository } from "../../infrastructure/repositories/conversationMessageCopy.repository";
import { ConversationSubjectCopyRepository } from "../../infrastructure/repositories/conversationSubjectCopy.repository";
import { LinkedInAdProgramCreativeRepository } from "../../infrastructure/repositories/linkedInAdProgramCreative.repository";
import { LinkedInAdSegmentSocialPostBaseCopyRepository } from "../../infrastructure/repositories/linkedInAdSegmentSocialPostBaseCopy.repository";
import { SocialPostCallToActionCopyRepository } from "../../infrastructure/repositories/socialPostCallToActionCopy.repository";
import { SocialPostCopyRepository } from "../../infrastructure/repositories/socialPostCopy.repository";
import { StageRepository } from "../../infrastructure/repositories/stage.repository";
import { LinkedInAdAudienceRepository } from "../../infrastructure/repositories/linkedInAdAudience.repository";
import { LinkedInCampaignRepository } from "../../infrastructure/repositories/linkedInCampaign.repository";
import { LinkedInAdSegmentRepository } from "../../infrastructure/repositories/linkedInAdSegment.repository";
import { LinkedInAdAccountRepository } from "../../infrastructure/repositories/linkedInAdAccount.repository";
import { LinkedInAdProgramRepository } from "../../infrastructure/repositories/linkedInAdProgram.repository";
import { LinkedInCampaignGroupRepository } from "../../infrastructure/repositories/linkedInCampaignGroup.repository";
import { LinkedInSponsoredCreativeRepository } from "../../infrastructure/repositories/linkedInSponsoredCreative.repository";
import { LinkedInPostRepository } from "../../infrastructure/repositories/linkedInPost.repository";
import { AdCreativeS3StorageService } from "../../infrastructure/services/adCreativeS3Storage.service";
import { LinkedInService } from "../../infrastructure/services/linkedIn.service";
import { advertisingInngestClient } from "../../utils/advertisingInngestClient";
import { getLinkedInFromUserId } from "../../../../../../packages/linkedInApi/src/linkedInClient";
import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { LinkedInSponsoredCreativeService } from "../../domain/services/linkedInSponsoredCreative.service";

export const abTestController = {
  getCurrentRunningAbTest: organizationRoute
    .input(getCurrentRunningAbTestDtoSchema)
    .query(async ({ input, ctx }) => {
      const adCreativeStorageService = new AdCreativeS3StorageService();
      const adCreativeRepository = new AdCreativeRepository();
      const adCreativeService = new AdCreativeService(
        adCreativeRepository,
        adCreativeStorageService,
      );

      const adProgramRepository = new LinkedInAdProgramCreativeRepository();
      const adProgramAdCreativeService = new LinkedInAdProgramAdCreativeService(
        adProgramRepository,
      );

      const adSegmentValuePropRepository = new AdSegmentValuePropRepository();
      const adSegmentValuePropService = new AdSegmentValuePropService(
        adSegmentValuePropRepository,
      );

      const linkedInSocialPostBaseCopyService =
        new LinkedInAdSegmentSocialPostBaseCopyService(
          new LinkedInAdSegmentSocialPostBaseCopyRepository(),
        );

      const useCase = new GetCurrentRunningAbTestUseCase({
        stageRepository: new StageRepository(),
        adSegmentValuePropService: adSegmentValuePropService,
        adProgramAdCreativeService: adProgramAdCreativeService,
        adCreativeService: adCreativeService,
        socialPostCopyService: new SocialPostAdCopyService(
          new SocialPostCopyRepository(),
        ),
        socialPostCallToActionCopyService:
          new SocialPostCallToActionCopyService(
            new SocialPostCallToActionCopyRepository(),
          ),
        conversationCopyService: new ConversationSubjectCopyService(
          new ConversationSubjectCopyRepository(),
        ),
        conversationMessageCopyService: new ConversationMessageCopyService(
          new ConversationMessageCopyRepository(),
        ),
        conversationCallToActionCopyService:
          new ConversationCallToActionCopyService(
            new ConversationCallToActionCopyRepository(),
          ),
      });

      const res = await useCase.execute(input, ctx.userId);
      return res;
    }),

  getCurrentRunningAbTestsBatch: organizationRoute
    .input(getCurrentRunningAbTestsBatchDtoSchema)
    .query(async ({ input, ctx }) => {
      const useCase = new GetCurrentRunningAbTestsBatchUseCase({
        stageRepository: new StageRepository(),
      });
      const res = await useCase.execute(input, ctx.userId);
      return res;
    }),

  getUpcomingRunningAbTests: organizationRoute
    .input(getUpcomingRunningAbTestsDtoSchema)
    .query(async ({ input }) => {
      const adCreativeStorageService = new AdCreativeS3StorageService();
      const adCreativeRepository = new AdCreativeRepository();
      const adCreativeService = new AdCreativeService(
        adCreativeRepository,
        adCreativeStorageService,
      );

      const adProgramRepository = new LinkedInAdProgramCreativeRepository();
      const adProgramAdCreativeService = new LinkedInAdProgramAdCreativeService(
        adProgramRepository,
      );

      const adSegmentValuePropRepository = new AdSegmentValuePropRepository();
      const adSegmentValuePropService = new AdSegmentValuePropService(
        adSegmentValuePropRepository,
      );

      const useCase = new GetUpcomingRunningAbTestsUseCase({
        stageRepository: new StageRepository(),
        adSegmentValuePropService: adSegmentValuePropService,
        adProgramAdCreativeService: adProgramAdCreativeService,
        adCreativeService: adCreativeService,
        socialPostCopyService: new SocialPostAdCopyService(
          new SocialPostCopyRepository(),
        ),
        socialPostCallToActionCopyService:
          new SocialPostCallToActionCopyService(
            new SocialPostCallToActionCopyRepository(),
          ),
        conversationCopyService: new ConversationSubjectCopyService(
          new ConversationSubjectCopyRepository(),
        ),
        conversationMessageCopyService: new ConversationMessageCopyService(
          new ConversationMessageCopyRepository(),
        ),
        conversationCallToActionCopyService:
          new ConversationCallToActionCopyService(
            new ConversationCallToActionCopyRepository(),
          ),
        adSegmentValuePropCreativeRepository:
          new AdSegmentValuePropCreativeRepository(),
      });
      const res = await useCase.execute(input);
      return res;
    }),

  getPastRanAbTests: organizationRoute
    .input(getPastRanAbTestsDtoSchema)
    .query(async ({ input }) => {
      const adCreativeStorageService = new AdCreativeS3StorageService();
      const adCreativeRepository = new AdCreativeRepository();
      const adCreativeService = new AdCreativeService(
        adCreativeRepository,
        adCreativeStorageService,
      );

      const adProgramRepository = new LinkedInAdProgramCreativeRepository();
      const adProgramAdCreativeService = new LinkedInAdProgramAdCreativeService(
        adProgramRepository,
      );

      const adSegmentValuePropRepository = new AdSegmentValuePropRepository();
      const adSegmentValuePropService = new AdSegmentValuePropService(
        adSegmentValuePropRepository,
      );

      const useCase = new GetPastRanAbTestsUseCase({
        stageRepository: new StageRepository(),
        adSegmentValuePropService: adSegmentValuePropService,
        adProgramAdCreativeService: adProgramAdCreativeService,
        adCreativeService: adCreativeService,
        socialPostCopyService: new SocialPostAdCopyService(
          new SocialPostCopyRepository(),
        ),
        socialPostCallToActionCopyService:
          new SocialPostCallToActionCopyService(
            new SocialPostCallToActionCopyRepository(),
          ),
        conversationCopyService: new ConversationSubjectCopyService(
          new ConversationSubjectCopyRepository(),
        ),
        conversationMessageCopyService: new ConversationMessageCopyService(
          new ConversationMessageCopyRepository(),
        ),
        conversationCallToActionCopyService:
          new ConversationCallToActionCopyService(
            new ConversationCallToActionCopyRepository(),
          ),
      });
      const res = await useCase.execute(input);
      return res;
    }),

  getAdMetrics: organizationRoute
    .input(getAdMetricsDtoSchema)
    .query(async ({ input, ctx }) => {
      const useCase = new GetAdMetricsUseCase();
      const res = await useCase.execute(input, ctx.userId);
      return res;
    }),

  addAudienceToRunningTest: organizationRoute
    .input(addAudienceToRunningTestDtoSchema)
    .mutation(async ({ input, ctx }) => {
      // Get LinkedIn client for the user
      const linkedInClient = await getLinkedInFromUserId(ctx.userId);
      if (!linkedInClient) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "LinkedIn authentication required",
        });
      }

      const stageRepository = new StageRepository();
      const linkedInService = new LinkedInService(linkedInClient);
      
      // Create ad creative service dependencies
      const adCreativeStorageService = new AdCreativeS3StorageService();
      const adCreativeRepository = new AdCreativeRepository();
      const adCreativeService = new AdCreativeService(
        adCreativeRepository,
        adCreativeStorageService,
      );
      
      const useCase = new AddAudienceToRunningTestUseCase({
        stageRepository,
        adAudienceRepository: new LinkedInAdAudienceRepository(),
        linkedInCampaignRepository: new LinkedInCampaignRepository(),
        linkedInSponsoredCreativeService: new LinkedInSponsoredCreativeService(new LinkedInSponsoredCreativeRepository()),
        linkedInAdSegmentRepository: new LinkedInAdSegmentRepository(),
        linkedInAdAccountRepository: new LinkedInAdAccountRepository(),
        linkedInCampaignGroupRepository: new LinkedInCampaignGroupRepository(),
        linkedInService,
        linkedInSponsoredCreativeRepository: new LinkedInSponsoredCreativeRepository(),
        linkedInPostRepository: new LinkedInPostRepository(),
        adCreativeService,
        linkedInAdProgramRepository: new LinkedInAdProgramRepository(),
      });
      
      return await useCase.execute(input, ctx.organizationId);
    }),
};
