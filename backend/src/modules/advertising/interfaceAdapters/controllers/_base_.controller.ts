import { newAbTestController } from "../../abTest/public/interfaceAdapters/controllers/abTest.controller";
import { abTestController } from "./abTest.controller";
import { adAudienceController } from "./adAudience.controller";
import { adCreativeController } from "./adCreative.controller";
import { advertisingAdminController } from "./admin.controller";
import { adSegmentController } from "./adSegment.controller";
import { adSegmentBestVariantController } from "./adSegmentBestVariant.controller";
import { adSegmentMidCampaignNotificationController } from "./adSegmentMidCampaignNotification.controller";
import { adSegmentSelectedConversationCallToActionTypeController } from "./adSegmentSelectedConversationCallToActionType.controller";
import { adSegmentSelectedConversationMessageTypeController } from "./adSegmentSelectedConversationMessageType.controller";
import { adSegmentSelectedConversationSubjectTypeController } from "./adSegmentSelectedConversationSubjectType.controller";
import { adSegmentSelectedSocialPostBodyTypeController } from "./adSegmentSelectedSocialPostBodyType.controller";
import { adSegmentSelectedSocialPostCallToActionTypeController } from "./adSegmentSelectedSocialPostCallToActionType.controller";
import { adSegmentValuePropController } from "./adSegmentValueProp.controller";
import { adSegmentValuePropCreativeController } from "./adSegmentValuePropCreative.controller";
import { caseStudyController } from "./caseStudy.controller";
import { conversationCopyController } from "./conversationCopy.controller";
import { conversationMessageCopyController } from "./conversationMessageCopy.controller";
import { conversationSubjectCopyController } from "./conversationSubjectCopy.controller";
import { createNewConversationValuePropStateController } from "./createNewConversationValuePropState.controller";
import { exampleSocialPostController } from "./exampleSocialPost.controller";
import { leadsController } from "./leads.controller";
import { linkedInAdAccountController } from "./linkedInAdAccount.controller";
import { linkedInAdProgramController } from "./linkedInAdProgram.controller";
import { linkedInAdProgramCreativeController } from "./linkedInAdProgramCreative.controller";
import { linkedInApiController } from "./linkedinApi.controller";
import { linkedInCampaignGroupController } from "./linkedInCampaignGroup.controller";
import { linkedInLeadFormController } from "./linkedInLeadForm.controller";
import { linkedInLeadFormLeadController } from "./linkedInLeadFormLead.controller";
import { linkedInUserController } from "./linkedInUser.controller";
import { positioningController } from "./positioning.controller";
import { slackSettingsController } from "./slackSettings.controller";
import { socialPostBaseCopyController } from "./socialPostBaseCopy.controller";
import { socialPostStyleGuideController } from "./socialPostStyleGuide.controller";
import { stageController } from "./stage.controller";

export const baseAdvertisingController = {
  adAccounts: linkedInAdAccountController,
  linkedInUser: linkedInUserController,
  linkedInAdProgram: linkedInAdProgramController,
  linkedInApi: linkedInApiController,
  adCreative: adCreativeController,
  linkedInAdProgramCreative: linkedInAdProgramCreativeController,
  adSegment: adSegmentController,
  adAudience: adAudienceController,
  exampleSocialPost: exampleSocialPostController,
  socialPostStyleGuide: socialPostStyleGuideController,
  positioning: positioningController,
  caseStudy: caseStudyController,
  adSegmentValueProp: adSegmentValuePropController,
  socialPostBaseCopy: socialPostBaseCopyController,
  conversationCopy: conversationCopyController,
  abTest: abTestController,
  linkedInCampaignGroup: linkedInCampaignGroupController,
  adSegmentSelectedConversationSubjectType:
    adSegmentSelectedConversationSubjectTypeController,
  adSegmentSelectedConversationMessageType:
    adSegmentSelectedConversationMessageTypeController,
  adSegmentSelectedConversationCallToActionType:
    adSegmentSelectedConversationCallToActionTypeController,
  adSegmentSelectedSocialPostBodyType:
    adSegmentSelectedSocialPostBodyTypeController,
  adSegmentSelectedSocialPostCallToActionType:
    adSegmentSelectedSocialPostCallToActionTypeController,
  admin: advertisingAdminController,
  createNewConversationValuePropState:
    createNewConversationValuePropStateController,
  adSegmentBestVariant: adSegmentBestVariantController,
  conversationSubjectCopy: conversationSubjectCopyController,
  conversationMessageCopy: conversationMessageCopyController,
  adSegmentMidCampaignNotification: adSegmentMidCampaignNotificationController,
  linkedInLeadForm: linkedInLeadFormController,
  linkedInLeadFormLead: linkedInLeadFormLeadController,
  leads: leadsController,
  adSegmentValuePropCreative: adSegmentValuePropCreativeController,
  slackSettings: slackSettingsController,
  stage: stageController,
  abTestController: abTestController,
  newAbTestController: newAbTestController,
};
