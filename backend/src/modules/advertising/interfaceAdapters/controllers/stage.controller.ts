import { TRPCError } from "@trpc/server";
import { z } from "zod";

import { getLinkedInApiClientFromOrganizationId } from "@kalos/linkedin-api";

import { organizationRoute } from "../../../../trpc/trpc";
import { ITransaction } from "../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { TransactionManagerService } from "../../../core/infrastructure/services/transcationManager.service";
import { ContinueToNextStageUseCase } from "../../application/useCase/stage/continueToNextStage.useCase";
import { ResumeCurrentStageUseCase } from "../../application/useCase/stage/resumeCurrentStage.useCase";
import { LinkedInAdAccountRepository } from "../../infrastructure/repositories/linkedInAdAccount.repository";
import { LinkedInAdProgramRepository } from "../../infrastructure/repositories/linkedInAdProgram.repository";
import { LinkedInAdSegmentRepository } from "../../infrastructure/repositories/linkedInAdSegment.repository";
import { StageRepository } from "../../infrastructure/repositories/stage.repository";
import { advertisingInngestClient } from "../../utils/advertisingInngestClient";

const resumeStageInput = z.object({
  stageId: z.string().uuid(),
  action: z.enum(["CONTINUE_TO_NEXT_STAGE", "RESUME_CURRENT_STAGE"]),
});

const getStageStatusInput = z
  .object({
    stageId: z.string().uuid().optional(),
    adSegmentId: z.string().uuid().optional(),
  })
  .refine((data) => data.stageId || data.adSegmentId, {
    message: "Either stageId or adSegmentId must be provided",
  });

export const stageController = {
  resumeStage: organizationRoute
    .input(resumeStageInput)
    .mutation(async ({ input, ctx }) => {
      const transactionManager = new TransactionManagerService();

      return await transactionManager.startTransaction(
        async (tx: ITransaction) => {
          const stageRepository = new StageRepository();
          const adSegmentRepository = new LinkedInAdSegmentRepository();
          const adProgramRepository = new LinkedInAdProgramRepository();
          const adAccountRepository = new LinkedInAdAccountRepository();

          // Get the stage for validation and permission check
          const stage = await stageRepository.getStage(input.stageId, tx);
          if (!stage) {
            throw new TRPCError({
              code: "NOT_FOUND",
              message: "Stage not found",
            });
          }

          // Verify the stage is paused for user input
          if (stage.status !== "PAUSED") {
            throw new TRPCError({
              code: "BAD_REQUEST",
              message: "Stage is not paused for user input",
            });
          }

          // Get organization ID for permission check
          const adSegment = await adSegmentRepository.getOne(stage.adSegmentId);
          if (!adSegment) {
            throw new TRPCError({
              code: "NOT_FOUND",
              message: "Ad segment not found",
            });
          }

          const adProgram = await adProgramRepository.getOne(
            adSegment.linkedInAdProgramId,
          );
          if (!adProgram) {
            throw new TRPCError({
              code: "NOT_FOUND",
              message: "Ad program not found",
            });
          }

          const adAccount = await adAccountRepository.getOneById(
            adProgram.linkedInAdAccountId,
          );
          if (!adAccount) {
            throw new TRPCError({
              code: "NOT_FOUND",
              message: "Ad account not found",
            });
          }

          // Verify user has access to this organization
          if (adAccount.organizationId !== ctx.organizationId) {
            throw new TRPCError({
              code: "FORBIDDEN",
              message: "You don't have access to this stage",
            });
          }

          // Cancel the timeout job since user is responding
          setImmediate(async () => {
            try {
              await advertisingInngestClient.send({
                name: "stage/user-input-timeout.cancel",
                data: {
                  stageId: stage.id,
                },
              });
              console.log(
                `[Stage Timeout] Cancelled timeout for stage ${stage.id} due to user response`,
              );
            } catch (error) {
              console.error(
                `[Stage Timeout] Failed to cancel timeout for stage ${stage.id}:`,
                error,
              );
            }
          });

          // Execute the appropriate use case
          if (input.action === "CONTINUE_TO_NEXT_STAGE") {
            const continueToNextStageUseCase = new ContinueToNextStageUseCase(
              stageRepository,
              adSegmentRepository,
              adProgramRepository,
              adAccountRepository,
            );

            const result = await continueToNextStageUseCase.execute({
              stageId: input.stageId,
              organizationId: adAccount.organizationId,
              tx,
            });

            if (result.isErr()) {
              throw new TRPCError({
                code: "INTERNAL_SERVER_ERROR",
                message: `Failed to continue to next stage: ${result.error.type}`,
              });
            }
          } else if (input.action === "RESUME_CURRENT_STAGE") {
            const resumeCurrentStageUseCase = new ResumeCurrentStageUseCase(
              stageRepository,
            );

            const result = await resumeCurrentStageUseCase.execute({
              stageId: input.stageId,
              tx,
            });

            if (result.isErr()) {
              throw new TRPCError({
                code: "INTERNAL_SERVER_ERROR",
                message: `Failed to resume current stage: ${result.error.type}`,
              });
            }
          }

          return {
            success: true,
            message:
              input.action === "CONTINUE_TO_NEXT_STAGE"
                ? "Stage completed and next stage will begin shortly"
                : "Stage resumed successfully",
          };
        },
      );
    }),

  // Get stage status for UI display
  getStageStatus: organizationRoute
    .input(getStageStatusInput)
    .query(async ({ input, ctx }) => {
      const stageRepository = new StageRepository();
      const adSegmentRepository = new LinkedInAdSegmentRepository();
      const adProgramRepository = new LinkedInAdProgramRepository();
      const adAccountRepository = new LinkedInAdAccountRepository();

      let stage;

      if (input.stageId) {
        stage = await stageRepository.getStage(input.stageId);
      } else if (input.adSegmentId) {
        // Find the current running audience stage for this ad segment
        const stages = await stageRepository.getStagesForAdSegment(
          input.adSegmentId,
        );
        stage =
          stages.find(
            (s) => s.stageType === "audienceTest" && s.status === "PAUSED",
          ) ||
          stages.find(
            (s) => s.stageType === "audienceTest" && s.status === "RUNNING",
          ) ||
          stages.find((s) => s.stageType === "audienceTest");
      }

      if (!stage) {
        // Return null data instead of throwing error - this is normal for segments without audience stages
        return {
          stageId: null,
          adSegmentId: input.adSegmentId || null,
          stageType: null,
          status: null,
          index: null,
          isPaused: false,
        };
      }

      // Get organization ID for permission check
      const adSegment = await adSegmentRepository.getOne(stage.adSegmentId);
      if (!adSegment) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Ad segment not found",
        });
      }

      const adProgram = await adProgramRepository.getOne(
        adSegment.linkedInAdProgramId,
      );
      if (!adProgram) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Ad program not found",
        });
      }

      const adAccount = await adAccountRepository.getOneById(
        adProgram.linkedInAdAccountId,
      );
      if (!adAccount) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Ad account not found",
        });
      }

      // Verify user has access to this organization
      if (adAccount.organizationId !== ctx.organizationId) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You don't have access to this stage",
        });
      }

      return {
        stageId: stage.id,
        adSegmentId: stage.adSegmentId,
        stageType: stage.stageType,
        status: stage.status,
        index: stage.index,
        isPaused: stage.status === "PAUSED",
      };
    }),
};
