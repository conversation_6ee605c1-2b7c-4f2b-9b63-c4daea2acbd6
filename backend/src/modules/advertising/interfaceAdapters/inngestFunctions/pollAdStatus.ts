import { NonRetriableError } from "inngest";
import { z } from "zod";

import { getLinkedInApiClientFromOrganizationId } from "@kalos/linkedin-api";

import { inngest } from "../../../../../../services/advertising/src/inngest/client";
import { createUuid } from "../../../core/utils/uuid";
import { abTestTypesSchema } from "../../domain/entites/abTest";
import { AdSegmentService } from "../../domain/services/adSegment.service";
import { LinkedInAdAccountService } from "../../domain/services/linkedInAdAccount.service";
import { LinkedInAdProgramService } from "../../domain/services/linkedInAdProgram.service";
import { LinkedInAdAccountRepository } from "../../infrastructure/repositories/linkedInAdAccount.repository";
import { LinkedInAdAudienceRepository } from "../../infrastructure/repositories/linkedInAdAudience.repository";
import { LinkedInAdProgramRepository } from "../../infrastructure/repositories/linkedInAdProgram.repository";
import { LinkedInAdSegmentRepository } from "../../infrastructure/repositories/linkedInAdSegment.repository";
import { LinkedInCampaignRepository } from "../../infrastructure/repositories/linkedInCampaign.repository";
import { LinkedInSponsoredCreativeRepository } from "../../infrastructure/repositories/linkedInSponsoredCreative.repository";
import { StageRepository } from "../../infrastructure/repositories/stage.repository";
import { LinkedInService } from "../../infrastructure/services/linkedIn.service";
import { advertisingInngestClient } from "../../utils/advertisingInngestClient";

const eventBodySchema = z.object({
  adSegmentId: z.string(),
  organizationId: z.number(),
  runStageAfterApproval: z.boolean().optional().nullable(),
  stageTypeToRun: abTestTypesSchema.nullable().optional(),
});
export const pollAdStatus = advertisingInngestClient.createFunction(
  { id: "poll-ad-status" },
  { event: "linkedin/poll-ad-status", retries: 0 },
  async ({ step, event }) => {
    const {
      adSegmentId,
      organizationId,
      runStageAfterApproval,
      stageTypeToRun,
    } = eventBodySchema.parse(event.data);
    const adSegment = await step.run("validate-segment", async () => {
      const adSegmentService = new AdSegmentService(
        new LinkedInAdSegmentRepository(),
      );
      const adSegment = await adSegmentService.getOne(adSegmentId);
      if (!adSegment) {
        throw new NonRetriableError("Ad segment not found");
      }
      return adSegment;
    });

    const adProgram = await step.run("get-ad-program", async () => {
      const adProgramService = new LinkedInAdProgramService(
        new LinkedInAdProgramRepository(),
      );
      const res = await adProgramService.getOne(adSegment.linkedInAdProgramId);
      if (!res) {
        throw new NonRetriableError("Ad program not found");
      }
      return res;
    });

    const adAccount = await step.run("get-ad-account", async () => {
      const adAccountService = new LinkedInAdAccountService(
        new LinkedInAdAccountRepository(),
      );
      const res = await adAccountService.getForOrganization(organizationId);
      if (!res[0]) {
        throw new NonRetriableError("No ad account found for organization");
      }
      return res[0];
    });

    let adStatus = "PENDING";
    let iteration = 0;

    while (adStatus !== "APPROVED" && adStatus !== "REJECTED") {
      adStatus = await step.run(
        `get-all-active-sponsored-creatives-${iteration}`,
        async () => {
          const linkedInSponsoredCreativeRepository =
            new LinkedInSponsoredCreativeRepository();

          const linkedInAdAudienceRepository =
            new LinkedInAdAudienceRepository();
          const linkedInAdAudiences =
            await linkedInAdAudienceRepository.getAllForAdSegment(adSegment.id);

          const linkedInClient =
            await getLinkedInApiClientFromOrganizationId(organizationId);
          if (!linkedInClient) {
            throw new NonRetriableError("Failed to get LinkedIn client");
          }

          const linkedInService = new LinkedInService(linkedInClient);

          for (const linkedInAdAudience of linkedInAdAudiences) {
            if (!linkedInAdAudience.toBeUsed) {
              continue;
            }
            const sponsoredCreatives =
              await linkedInSponsoredCreativeRepository.getAllForCampaign(
                linkedInAdAudience.id,
              );
            console.log("CREATIVES", sponsoredCreatives);
            for (const sponsoredCreative of sponsoredCreatives) {
              const status = await linkedInService.getSponsoredCreativeStatus({
                adAccountUrn: adAccount.linkedInAdAccountUrn,
                adUrn: sponsoredCreative.linkedInSponseredCreativeUrn,
              });

              if (status.review.status == "REJECTED") {
                return "REJECTED";
              }

              if (status.review.status !== "APPROVED") {
                return "PENDING";
              }
            }
          }
          console.log(`ADS APPROVED FOR AD SEGMENT ${adSegment.id}`);
          return "APPROVED";
        },
      );
      if (adStatus === "REJECTED") {
        throw new NonRetriableError("Ad rejected");
      }
      if (adStatus !== "APPROVED") {
        console.log(
          `ADS NOT APPROVED YET FOR AD SEGMENT ${adSegment.id}, SLEEPING FOR 5 MINUTES`,
        );
        await step.sleep("sleep", 5 * 60 * 1000);
      }
      iteration++;
    }

    adStatus = await step.run(
      "create-stages-and-run-mannual-bidding",
      async () => {
        await advertisingInngestClient.send({
          name: "manualBidding.wait-for-next-event",
          data: {
            adSegmentId: adSegment.id,
          },
        });
        if (runStageAfterApproval && !stageTypeToRun) {
          const stageRepository = new StageRepository();
          await stageRepository.createStage({
            id: createUuid(),
            adSegmentId: adSegment.id,
            stageType: "audienceTest",
            status: "NOT_STATED",
            index: 1,
          });
          if (adProgram.adFormat.type === "SPONSORED_CONTENT") {
            await stageRepository.createStage({
              id: createUuid(),
              adSegmentId: adSegment.id,
              stageType: "creativeTest",
              status: "NOT_STATED",
              index: 2,
            });
          }
          if (adProgram.adFormat.type === "SPONSORED_INMAIL") {
            await stageRepository.createStage({
              id: createUuid(),
              adSegmentId: adSegment.id,
              stageType: "conversationSubjectTest",
              status: "NOT_STATED",
              index: 2,
            });
          }
          await stageRepository.createStage({
            id: createUuid(),
            adSegmentId: adSegment.id,
            stageType: "valuePropTest",
            status: "NOT_STATED",
            index: adProgram.adFormat.type == "SPONSORED_CONVERSATION" ? 2 : 3,
          });

          await advertisingInngestClient.send({
            name: "linkedin/stage.run",
            data: {
              adSegmentId: adSegment.id,
              organizationId: organizationId,
            },
          });

          // RUNSTAGE
        }

        if (stageTypeToRun) {
          const stageRepository = new StageRepository();
          if (stageTypeToRun === "valueProp") {
            const stages = await stageRepository.getStagesForAdSegment(
              adSegment.id,
            );
            const index =
              stages
                .map((stage) => stage.index)
                .reduce((a, b) => Math.max(a, b), 0) + 1;
            await stageRepository.createStage({
              id: createUuid(),
              adSegmentId: adSegment.id,
              stageType: "valuePropTest",
              status: "NOT_STATED",
              index: index,
            });
          } else if (stageTypeToRun === "socialPostBodyCopy") {
            const stages = await stageRepository.getStagesForAdSegment(
              adSegment.id,
            );
            const index =
              stages
                .map((stage) => stage.index)
                .reduce((a, b) => Math.max(a, b), 0) + 1;

            await stageRepository.createStage({
              id: createUuid(),
              adSegmentId: adSegment.id,
              stageType: "socialPostBodyCopyTest",
              status: "NOT_STATED",
              index: index,
            });
          }
          const currentRunning = await stageRepository.getCurrentRunningStage(
            adSegment.id,
          );
          if (!currentRunning) {
            await advertisingInngestClient.send({
              name: "linkedin/stage.run",
              data: {
                adSegmentId: adSegment.id,
                organizationId: organizationId,
              },
            });
          }
        }

        return adStatus;
      },
    );
    return adStatus;
  },
);
