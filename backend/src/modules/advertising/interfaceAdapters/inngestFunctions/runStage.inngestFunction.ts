import { NonRetriableError } from "inngest";

import { InngestJobTriggerPublisher } from "../../../shared/inngestJobTriggerPublisher";
import { startAbTestJobTrigger } from "../../abTest/internal/jobTriggers/startAbTest.jobTrigger";
import { StageRepository } from "../../infrastructure/repositories/stage.repository";
import { advertisingInngestClient } from "../../utils/advertisingInngestClient";

interface EventData {
  adSegmentId: string;
  organizationId: number;
}

export const runStage = advertisingInngestClient.createFunction(
  { id: "run-linked-in-stage" },
  { event: "linkedin/stage.run" },
  async ({ step, event }) => {
    const eventData: EventData = event.data;
    const stage = await step.run("getStage", async () => {
      const stageRepository = new StageRepository();
      const stages = await stageRepository.getStagesForAdSegment(
        eventData.adSegmentId,
      );
      const stagesToRun = stages.filter(
        (stage) => stage.status === "NOT_STATED",
      );
      const runningStages = stages.filter(
        (stage) => stage.status === "RUNNING",
      );
      const pausedStages = stages.filter(
        (stage) => stage.status === "PAUSED",
      );
      
      if (runningStages.length > 0) {
        throw new NonRetriableError(
          "Cannot run stage while another stage is running",
        );
      }
      
      // If there are paused stages, don't run any new stages
      if (pausedStages.length > 0) {
        console.log(`Stage execution paused - found ${pausedStages.length} stage(s) waiting for user input`);
        return {
          stage: null,
        };
      }
      
      console.log(stages);
      const firstStage = stagesToRun.sort((a, b) => a.index - b.index)[0];
      if (!firstStage) {
        return {
          stage: null,
        };
      }
      return {
        stage: firstStage,
      };
    });

    if (!stage.stage) {
      return "No stage to run";
    }

    await step.run("runStage", async () => {
      const stageRepository = new StageRepository();
      await stageRepository.updateStageStatus(stage.stage.id, "RUNNING");
      const jobTriggerPublisher = InngestJobTriggerPublisher(
        advertisingInngestClient,
      );
      switch (stage.stage.stageType) {
        case "audienceTest":
          await jobTriggerPublisher.publish(
            startAbTestJobTrigger.build({
              stageId: stage.stage.id,
              abTestType: "audience",
              adSegmentId: eventData.adSegmentId,
            }),
          );

          break;
        case "valuePropTest":
          await jobTriggerPublisher.publish(
            startAbTestJobTrigger.build({
              stageId: stage.stage.id,
              abTestType: "valueProp",
              adSegmentId: eventData.adSegmentId,
            }),
          );
          break;
        case "creativeTest":
          await jobTriggerPublisher.publish(
            startAbTestJobTrigger.build({
              stageId: stage.stage.id,
              abTestType: "creative",
              adSegmentId: eventData.adSegmentId,
            }),
          );
          break;
        case "conversationSubjectTest":
          await jobTriggerPublisher.publish(
            startAbTestJobTrigger.build({
              stageId: stage.stage.id,
              abTestType: "conversationSubject",
              adSegmentId: eventData.adSegmentId,
            }),
          );
          break;
        case "socialPostBodyCopyTest":
          await jobTriggerPublisher.publish(
            startAbTestJobTrigger.build({
              stageId: stage.stage.id,
              abTestType: "socialPostBodyCopy",
              adSegmentId: eventData.adSegmentId,
            }),
          );
          break;
        default:
          throw new Error("Invalid stage type");
      }
    });
  },
);
