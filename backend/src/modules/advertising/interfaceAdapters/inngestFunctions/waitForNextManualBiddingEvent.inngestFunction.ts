import { z } from "zod";

import { TransactionManagerService } from "../../../core/infrastructure/services/transcationManager.service";
import { AbTestFacadeService } from "../../application/services/abTest/abTest.facade.service";
import { AbTest, abTestTypesSchema } from "../../domain/entites/abTest";
import { advertisingInngestClient } from "../../utils/advertisingInngestClient";

const eventSchema = z.object({
  adSegmentId: z.string(),
});

export const waitForNextManualBiddingEvent =
  advertisingInngestClient.createFunction(
    { id: "wait-for-next-manual-bidding-event", retries: 0 },
    { event: "manualBidding.wait-for-next-event" },
    async ({ step, event }) => {
      const parsedEventData = eventSchema.parse(event.data);

      await step.sleep("do-wait-for-next-manual-bidding-event", "3h");

      await step.run("send-mannual-bidding-evdent", async () => {
        await advertisingInngestClient.send({
          name: "manualBidding.event",
          data: {
            adSegmentId: parsedEventData.adSegmentId,
          },
        });
      });
    },
  );
