import { err, ok, Result } from "neverthrow";

import { Stage } from "../../../domain/entites/stage";
import { AbTest } from "./abTest.entity";
import { AbTestRoundDomain } from "./abTestRound.domain";
import { AbTestRound } from "./abTestRound.entity";
import { AbTestType } from "./abTestType.valueObject";

export const AbTestDomain = {
  create: (input: {
    stage: Stage;
    type: AbTestType;
  }): Result<AbTest, { type: "STAGE_NOT_RUNNING" }> => {
    if (input.stage.status !== "RUNNING") {
      return err({ type: "STAGE_NOT_RUNNING" });
    }
    const abTest = AbTest({
      stageId: input.stage.id,
      type: input.type,
      status: "NOT_STARTED",
      winnerId: null,
    });
    return ok(abTest);
  },

  autoResolve: (input: {
    abTest: AbTest;
    winnerId: string;
  }): Result<AbTest, { type: "AB_TEST_ALREADY_STARTED" }> => {
    if (input.abTest.status !== "NOT_STARTED") {
      return err({ type: "AB_TEST_ALREADY_STARTED" });
    }
    const updatedAbTest = AbTest({
      ...input.abTest,
      status: "AUTO_RESOLVED",
      winnerId: input.winnerId,
    });
    return ok(updatedAbTest);
  },

  start: (input: {
    abTest: AbTest;
    variants: string[];
  }): Result<
    | {
        abTest: AbTest;
        rounds: null;
        outcome: "AUTO_RESOLVED";
      }
    | {
        abTest: AbTest;
        rounds: AbTestRound[];
        outcome: "IN_PROGRESS";
      },
    {
      type:
        | "AB_TEST_ALREADY_STARTED"
        | "EMPTY_VARIENTS_LIST_PROVIDED"
        | "DUPLICATES_EXIST_IN_VARIENTS_LIST"
        | "ERROR_CREATING_ROUNDS";
    }
  > => {
    if (input.abTest.status !== "NOT_STARTED") {
      return err({ type: "AB_TEST_ALREADY_STARTED" });
    }
    if (input.variants.length === 0) {
      return err({ type: "EMPTY_VARIENTS_LIST_PROVIDED" });
    }
    if (input.variants.length === 1) {
      const autoResolvedAbTest = AbTestDomain.autoResolve({
        abTest: input.abTest,
        winnerId: input.variants[0]!,
      });
      if (autoResolvedAbTest.isErr()) {
        return err(autoResolvedAbTest.error);
      }
      return ok({
        abTest: autoResolvedAbTest.value,
        rounds: null,
        outcome: "AUTO_RESOLVED",
      });
    }

    const rounds = AbTestRoundDomain.setupInitialRounds({
      abTest: input.abTest,
      variants: input.variants,
    });
    if (rounds.isErr()) {
      if (rounds.error.type === "DUPLICATES_EXIST_IN_VARIENTS_LIST") {
        return err({ type: "DUPLICATES_EXIST_IN_VARIENTS_LIST" });
      }
      if (rounds.error.type === "ERROR_CREATING_ROUNDS") {
        return err({ type: "ERROR_CREATING_ROUNDS" });
      }
      throw new Error(rounds.error.type);
    }
    const updatedAbTest = AbTest({
      ...input.abTest,
      status: "IN_PROGRESS",
    });
    return ok({
      abTest: updatedAbTest,
      rounds: rounds.value,
      outcome: "IN_PROGRESS",
    });
  },

  createAndStart: (input: {
    stage: Stage;
    type: AbTestType;
    variants: string[];
  }): Result<
    | {
        abTest: AbTest;
        rounds: null;
        outcome: "AUTO_RESOLVED";
      }
    | {
        abTest: AbTest;
        rounds: AbTestRound[];
        outcome: "IN_PROGRESS";
      },
    {
      type:
        | "STAGE_NOT_RUNNING"
        | "AB_TEST_ALREADY_STARTED"
        | "EMPTY_VARIENTS_LIST_PROVIDED"
        | "DUPLICATES_EXIST_IN_VARIENTS_LIST"
        | "ERROR_CREATING_ROUNDS";
    }
  > => {
    const createdAbTest = AbTestDomain.create({
      stage: input.stage,
      type: input.type,
    });
    if (createdAbTest.isErr()) {
      return err(createdAbTest.error);
    }
    const startedAbTest = AbTestDomain.start({
      abTest: createdAbTest.value,
      variants: input.variants,
    });
    if (startedAbTest.isErr()) {
      return err(startedAbTest.error);
    }
    return ok(startedAbTest.value);
  },

  complete: (input: {
    abTest: AbTest;
    abTestRoundsForAbTest: AbTestRound[];
  }): Result<
    AbTest,
    { type: "AB_TEST_NOT_IN_PROGRESS" | "NOT_ALL_AB_TEST_ROUNDS_COMPLETED" }
  > => {
    if (input.abTest.status !== "IN_PROGRESS") {
      return err({ type: "AB_TEST_NOT_IN_PROGRESS" });
    }

    const notCompletedRounds = input.abTestRoundsForAbTest.filter(
      (round) =>
        round.status !== "COMPLETED" &&
        round.status !== "AUTO_RESOLVED" &&
        round.status !== "USER_RESOLVED",
    );

    if (notCompletedRounds.length > 0) {
      return err({ type: "NOT_ALL_AB_TEST_ROUNDS_COMPLETED" });
    }

    const lastRound = input.abTestRoundsForAbTest.sort(
      (a, b) => a.roundIndex - b.roundIndex,
    )[input.abTestRoundsForAbTest.length - 1]!;

    if (
      lastRound.status !== "COMPLETED" &&
      lastRound.status !== "AUTO_RESOLVED" &&
      lastRound.status !== "USER_RESOLVED"
    ) {
      throw new Error("Last round is not completed, this should never happen");
    }

    const winnerId =
      lastRound.winner === "CURRENT_BEST"
        ? lastRound.currentBestId
        : lastRound.contenderId;

    const updatedAbTest = AbTest({
      ...input.abTest,
      status: "COMPLETED",
      winnerId: winnerId,
    });
    return ok(updatedAbTest);
  },

  reactivate: (input: {
    abTest: AbTest;
  }): Result<AbTest, { type: "AB_TEST_NOT_COMPLETED" }> => {
    if (input.abTest.status !== "COMPLETED") {
      return err({ type: "AB_TEST_NOT_COMPLETED" });
    }
    const updatedAbTest = AbTest({
      ...input.abTest,
      status: "IN_PROGRESS",
      winnerId: null,
    });
    return ok(updatedAbTest);
  },
};
