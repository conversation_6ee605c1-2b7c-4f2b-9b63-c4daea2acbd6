import { err, ok, Result } from "neverthrow";

import { AdProgram } from "../../../../domain/entites/adProgram";
import { AdSegmentValueProp } from "../../../../domain/entites/adSegmentValueProp";
import { ConversationSubjectCopy } from "../../../../domain/entites/conversationSubjectCopy";
import { LinkedInCampaign } from "../../../../domain/entites/linkedInCampaign";
import { ValuePropCreative } from "../../../../domain/entites/valuePropCreative";
import { SponsoredContentAdFormat } from "../../../../domain/valueObjects/linkedinAdFormat";
import { LinkedInStateInput } from "../../../../linkedInStateOrchestrator/domain/linkedInStateInput.valueObject";
import { LinkedInStateOutput } from "../../../../linkedInStateOrchestrator/domain/linkedInStateOutput.entity";

export const ValuePropAbTestDomain = {
  constructResourceIdsList: {
    forSponsoredContent: (input: {
      adSegmentValuePropCreatives: ValuePropCreative[];
      bestCreativeId: string | null;
    }): string[] => {
      console.log("input.bestCreativeId", input.bestCreativeId);
      console.log(
        "input.adSegmentValuePropCreatives",
        input.adSegmentValuePropCreatives,
      );
      const valuePropCreativesForBestCreative =
        input.adSegmentValuePropCreatives.filter(
          (valuePropCreative) =>
            valuePropCreative.adProgramCreativeId == input.bestCreativeId,
        );
      console.log(
        "valuePropCreativesForBestCreative",
        valuePropCreativesForBestCreative,
      );
      if (valuePropCreativesForBestCreative.length === 0) {
        return [];
      }

      const valuePropsSet = new Set<string>();
      for (const valuePropCreative of valuePropCreativesForBestCreative) {
        valuePropsSet.add(valuePropCreative.adSegmentValuePropId);
      }
      const valueProps = Array.from(valuePropsSet);
      return valueProps;
    },
    forSponsoredInmail: (input: {
      conversationSubjectCopies: ConversationSubjectCopy[];
      bestConversationSubjectType: string;
    }): string[] => {
      const valueProps = input.conversationSubjectCopies.filter(
        (conversationSubjectCopy) =>
          conversationSubjectCopy.type === input.bestConversationSubjectType,
      );
      return valueProps.map((valueProp) => valueProp.valuePropId);
    },
  },
  constructLinkerInStateOrchestratorInput: {
    forSponsoredContent: (input: {
      adSegmentId: string;
      audienceId: string;
      bestCreativeId: string;
      currentBestValuePropId: string;
      contenderValuePropId: string;
      bestSocialPostBodyCopyType: string | null;
      bestSocialPostCallToActionType: string | null;
      adProgram: AdProgram;
    }) => {
      let adFormat: "SINGLE_IMAGE" | "SINGLE_VIDEO" | "DOCUMENT" | undefined =
        undefined;
      if (input.adProgram.adFormat.type !== "SPONSORED_CONTENT") {
        throw new Error("Ad program is not a sponsored content");
      }
      if (input.adProgram.adFormat.format === "SINGLE_IMAGE") {
        adFormat = "SINGLE_IMAGE";
      } else if (input.adProgram.adFormat.format === "VIDEO") {
        adFormat = "SINGLE_VIDEO";
      } else if (input.adProgram.adFormat.format === "DOCUMENT") {
        adFormat = "DOCUMENT";
      }
      if (!adFormat) {
        throw new Error("Ad format is not supported");
      }
      const stateInput: LinkedInStateInput = {
        adFormatType: "SPONSORED_CONTENT",
        adSegmentId: input.adSegmentId,
        adFormat: adFormat,
        data: {
          campaigns: [
            {
              campaignId: input.audienceId,
              ads: [
                {
                  adCreativeId: input.bestCreativeId,
                  valuePropId: input.currentBestValuePropId,
                  bodyCopyType: input.bestSocialPostBodyCopyType ?? "standard",
                  callToActionCopyType:
                    input.bestSocialPostCallToActionType ?? "Standard",
                },
                {
                  adCreativeId: input.bestCreativeId,
                  valuePropId: input.contenderValuePropId,
                  bodyCopyType: input.bestSocialPostBodyCopyType ?? "standard",
                  callToActionCopyType:
                    input.bestSocialPostCallToActionType ?? "Standard",
                },
              ],
            },
          ],
        },
      };
      return stateInput;
    },
    forSponsoredInmail: (input: {
      adSegmentId: string;
      audienceId: string;
      conversationSubjectCopyType: string;
      currentBestValuePropId: string;
      contenderValuePropId: string;
      bestConversationMessageCopyType: string | null;
      bestConversationCallToActionCopyType: string | null;
    }) => {
      const stateInput: LinkedInStateInput = {
        adFormatType: "SPONSORED_INMAIL",
        adSegmentId: input.adSegmentId,
        data: {
          campaigns: [
            {
              campaignId: input.audienceId,
              ads: [
                {
                  valuePropId: input.currentBestValuePropId,
                  subjectCopyType: input.conversationSubjectCopyType,
                  messageCopyType:
                    input.bestConversationMessageCopyType ?? "standard",
                  callToActionCopyType:
                    input.bestConversationCallToActionCopyType ?? "standard",
                },
                {
                  valuePropId: input.contenderValuePropId,
                  subjectCopyType: input.conversationSubjectCopyType,
                  messageCopyType:
                    input.bestConversationMessageCopyType ?? "standard",
                  callToActionCopyType:
                    input.bestConversationCallToActionCopyType ?? "standard",
                },
              ],
            },
          ],
        },
      };
      return stateInput;
    },
  },
  constructMetricsForDecisionEngine: {
    forSponsoredContent: (input: {
      adProgram: AdProgram;
      linkedInStateOutput: LinkedInStateOutput;
      currentBestValuePropId: string;
      contenderValuePropId: string;
    }): Result<
      {
        currentBestMetrics: { primaryMetric: number; secondaryMetric: number };
        contenderMetrics: { primaryMetric: number; secondaryMetric: number };
      },
      {
        type:
          | "CURRENT_BEST_VALUE_PROP_NOT_FOUND"
          | "CONTENDER_VALUE_PROP_NOT_FOUND"
          | "NO_CAMPAIGN_FOUND";
      }
    > => {
      const campaign = input.linkedInStateOutput.data.campaigns[0];
      if (!campaign) {
        return err({ type: "NO_CAMPAIGN_FOUND" });
      }
      const currentBestValueProp = campaign.ads.find(
        (ad) => ad.valuePropId === input.currentBestValuePropId,
      );
      const contenderValueProp = campaign.ads.find(
        (ad) => ad.valuePropId === input.contenderValuePropId,
      );
      if (!currentBestValueProp || !contenderValueProp) {
        return err({
          type: !currentBestValueProp
            ? "CURRENT_BEST_VALUE_PROP_NOT_FOUND"
            : "CONTENDER_VALUE_PROP_NOT_FOUND",
        });
      }
      let currentBestPrimaryMetric: number | null = null;
      let currentBestSecondaryMetric: number | null = null;
      let contenderPrimaryMetric: number | null = null;
      let contenderSecondaryMetric: number | null = null;

      if (input.adProgram.objectiveType === "LEAD_GENERATION") {
        currentBestPrimaryMetric = currentBestValueProp.metrics.leads ?? 0;
        contenderPrimaryMetric = contenderValueProp.metrics.leads ?? 0;
      } else {
        currentBestPrimaryMetric =
          currentBestValueProp.metrics.totalEngagements ?? 0;
        contenderPrimaryMetric =
          contenderValueProp.metrics.totalEngagements ?? 0;
      }
      if (input.adProgram.adFormat.type !== "SPONSORED_CONTENT") {
        throw new Error("Ad program is not a sponsored content");
      }
      if (input.adProgram.adFormat.format === "VIDEO") {
        currentBestSecondaryMetric =
          (currentBestValueProp.metrics.videoCompletions ?? 0) /
          (currentBestValueProp.metrics.videoViews &&
          currentBestValueProp.metrics.videoViews > 0
            ? currentBestValueProp.metrics.videoViews
            : 1);
        contenderSecondaryMetric =
          (contenderValueProp.metrics.videoCompletions ?? 0) /
          (contenderValueProp.metrics.videoViews &&
          contenderValueProp.metrics.videoViews > 0
            ? contenderValueProp.metrics.videoViews
            : 1);
      } else {
        currentBestPrimaryMetric =
          (currentBestValueProp.metrics.videoCompletions ?? 0) /
          (currentBestValueProp.metrics.videoViews &&
          currentBestValueProp.metrics.videoViews > 0
            ? currentBestValueProp.metrics.videoViews
            : 1);
        contenderPrimaryMetric =
          (contenderValueProp.metrics.videoCompletions ?? 0) /
          (contenderValueProp.metrics.videoViews &&
          contenderValueProp.metrics.videoViews > 0
            ? contenderValueProp.metrics.videoViews
            : 1);
      }
      return ok({
        currentBestMetrics: {
          primaryMetric: currentBestPrimaryMetric ?? 0,
          secondaryMetric: currentBestSecondaryMetric ?? 0,
        },
        contenderMetrics: {
          primaryMetric: contenderPrimaryMetric ?? 0,
          secondaryMetric: contenderSecondaryMetric ?? 0,
        },
      });
    },

    forSponsoredInmail: (input: {
      adProgram: AdProgram;
      linkedInStateOutput: LinkedInStateOutput;
      currentBestValuePropId: string;
      contenderValuePropId: string;
    }): Result<
      {
        currentBestMetrics: { primaryMetric: number; secondaryMetric: number };
        contenderMetrics: { primaryMetric: number; secondaryMetric: number };
      },
      {
        type:
          | "CURRENT_BEST_VALUE_PROP_NOT_FOUND"
          | "CONTENDER_VALUE_PROP_NOT_FOUND"
          | "NO_CAMPAIGN_FOUND";
      }
    > => {
      const campaign = input.linkedInStateOutput.data.campaigns[0];
      if (!campaign) {
        return err({ type: "NO_CAMPAIGN_FOUND" });
      }
      const currentBestValuePropMetrics = campaign.ads.find(
        (ad) => ad.valuePropId === input.currentBestValuePropId,
      );
      const contenderValuePropMetrics = campaign.ads.find(
        (ad) => ad.valuePropId === input.contenderValuePropId,
      );
      if (!currentBestValuePropMetrics || !contenderValuePropMetrics) {
        return err({
          type: !currentBestValuePropMetrics
            ? "CURRENT_BEST_VALUE_PROP_NOT_FOUND"
            : "CONTENDER_VALUE_PROP_NOT_FOUND",
        });
      }
      if (input.adProgram.adFormat.type !== "SPONSORED_INMAIL") {
        throw new Error("Ad program is not a sponsored inmail");
      }
      let currentBestPrimaryMetric: number | null = null;
      let contenderPrimaryMetric: number | null = null;

      if (input.adProgram.objectiveType == "LEAD_GENERATION") {
        currentBestPrimaryMetric =
          currentBestValuePropMetrics.metrics.leads ?? 0;
        contenderPrimaryMetric = contenderValuePropMetrics.metrics.leads ?? 0;
      } else {
        currentBestPrimaryMetric =
          currentBestValuePropMetrics.metrics.totalEngagements ?? 0;
        contenderPrimaryMetric =
          contenderValuePropMetrics.metrics.totalEngagements ?? 0;
      }

      if (input.adProgram.adFormat.type !== "SPONSORED_INMAIL") {
        throw new Error("Ad program is not a sponsored inmail");
      }

      const currentBestSecondaryMetric =
        (currentBestValuePropMetrics.metrics.opens ?? 0) /
        (currentBestValuePropMetrics.metrics.sends ?? 1);
      const contenderSecondaryMetric =
        (contenderValuePropMetrics.metrics.opens ?? 0) /
        (contenderValuePropMetrics.metrics.sends ?? 1);

      return ok({
        currentBestMetrics: {
          primaryMetric: currentBestPrimaryMetric,
          secondaryMetric: currentBestSecondaryMetric,
        },
        contenderMetrics: {
          primaryMetric: contenderPrimaryMetric,
          secondaryMetric: contenderSecondaryMetric,
        },
      });
    },
  },
};
