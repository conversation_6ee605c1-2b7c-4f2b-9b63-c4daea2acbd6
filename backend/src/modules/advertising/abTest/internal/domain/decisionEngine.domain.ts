import { err, ok, Result } from "neverthrow";
import {
  mean,
  standardDeviation,
  tTest,
  tTestTwoSample,
  variance,
} from "simple-statistics";

const { jStat } = require("jstat");

const CONFIDENCE_INTERVAL = 0.95;

interface DecisionEngineOutput {
  decision: "FIRST_VARIANT" | "SECOND_VARIANT" | "NO_DECISION";
  decisionType:
    | "PRIMARY_METRIC_T_TEST"
    | "SECONDARY_METRIC_T_TEST"
    | "PRIMARY_METRIC_SUM"
    | "SECONDARY_METRIC_SUM"
    | "DATA_LENGTH_IS_ONE"
    | "NO_STATISTICAL_DIFFERENCE_IN_PRIMARY_AND_SECONDARY_METRICS_BEFORE_5_DAYS"
    | "RANDOM";
  primaryMetricTTestResult: number | null;
  secondaryMetricTTestResult: number | null;
  currentBest: {
    primaryMetric: {
      sum: number;
      mean: number;
    };
    secondaryMetric: {
      sum: number;
      mean: number;
    };
  };
  contender: {
    primaryMetric: {
      sum: number;
      mean: number;
    };
    secondaryMetric: {
      sum: number;
      mean: number;
    };
  };
}

export class DecisionEngine {
  getDecision(input: {
    firstVariant: {
      primaryMetric: number;
      secondaryMetric: number;
    }[];
    secondVariant: {
      primaryMetric: number;
      secondaryMetric: number;
    }[];
  }): Result<
    DecisionEngineOutput,
    {
      type: "DATA_LENGTH_MISMATCH" | "DATA_LENGTH_IS_ZERO";
    }
  > {
    if (input.firstVariant.length !== input.secondVariant.length) {
      return err({ type: "DATA_LENGTH_MISMATCH" });
    }
    if (input.firstVariant.length === 0) {
      return err({ type: "DATA_LENGTH_IS_ZERO" });
    }
    if (input.firstVariant.length === 1) {
      return ok({
        decision: "NO_DECISION",
        decisionType: "DATA_LENGTH_IS_ONE",
        primaryMetricTTestResult: null,
        secondaryMetricTTestResult: null,
        currentBest: {
          primaryMetric: {
            sum: input.firstVariant[0]!.primaryMetric,
            mean: input.firstVariant[0]!.primaryMetric,
          },
          secondaryMetric: {
            sum: input.firstVariant[0]!.secondaryMetric,
            mean: input.firstVariant[0]!.secondaryMetric,
          },
        },
        contender: {
          primaryMetric: {
            sum: input.secondVariant[0]!.primaryMetric,
            mean: input.secondVariant[0]!.primaryMetric,
          },
          secondaryMetric: {
            sum: input.secondVariant[0]!.secondaryMetric,
            mean: input.secondVariant[0]!.secondaryMetric,
          },
        },
      });
    }
    const primaryMetricTTestResult = this.completeTTest({
      firstVariantData: input.firstVariant.map(
        (variant) => variant.primaryMetric,
      ),
      secondVariantData: input.secondVariant.map(
        (variant) => variant.primaryMetric,
      ),
      confidenceInterval: CONFIDENCE_INTERVAL,
    });
    if (primaryMetricTTestResult.isErr()) {
      if (primaryMetricTTestResult.error.type == "DATA_IS_IDENTICAL") {
        return ok({
          decision:
            input.firstVariant.length == 5 ? "FIRST_VARIANT" : "NO_DECISION",
          decisionType:
            "NO_STATISTICAL_DIFFERENCE_IN_PRIMARY_AND_SECONDARY_METRICS_BEFORE_5_DAYS",
          primaryMetricTTestResult: 0,
          secondaryMetricTTestResult: null,
          currentBest: {
            primaryMetric: {
              sum: input.firstVariant.reduce(
                (acc, variant) => acc + variant.primaryMetric,
                0,
              ),
              mean: mean(
                input.firstVariant.map((variant) => variant.primaryMetric),
              ),
            },
            secondaryMetric: {
              sum: input.firstVariant.reduce(
                (acc, variant) => acc + variant.secondaryMetric,
                0,
              ),
              mean: mean(
                input.firstVariant.map((variant) => variant.secondaryMetric),
              ),
            },
          },
          contender: {
            primaryMetric: {
              sum: input.secondVariant.reduce(
                (acc, variant) => acc + variant.primaryMetric,
                0,
              ),
              mean: mean(
                input.secondVariant.map((variant) => variant.primaryMetric),
              ),
            },
            secondaryMetric: {
              sum: input.secondVariant.reduce(
                (acc, variant) => acc + variant.secondaryMetric,
                0,
              ),
              mean: mean(
                input.secondVariant.map((variant) => variant.secondaryMetric),
              ),
            },
          },
        });
      } else {
        throw new Error(
          "Failed to complete primary metric t-test" +
            primaryMetricTTestResult.error.type,
        );
      }
    }
    if (primaryMetricTTestResult.value.result !== "NO_STATISTICAL_DIFFERENCE") {
      return ok({
        decision:
          primaryMetricTTestResult.value.result === "FIRST_VARIANT"
            ? "FIRST_VARIANT"
            : "SECOND_VARIANT",
        decisionType: "PRIMARY_METRIC_T_TEST",
        primaryMetricTTestResult: primaryMetricTTestResult.value.pValue,
        secondaryMetricTTestResult: null,
        currentBest: {
          primaryMetric: {
            sum: input.firstVariant.reduce(
              (acc, variant) => acc + variant.primaryMetric,
              0,
            ),
            mean: mean(
              input.firstVariant.map((variant) => variant.primaryMetric),
            ),
          },
          secondaryMetric: {
            sum: input.firstVariant.reduce(
              (acc, variant) => acc + variant.secondaryMetric,
              0,
            ),
            mean: mean(
              input.firstVariant.map((variant) => variant.secondaryMetric),
            ),
          },
        },
        contender: {
          primaryMetric: {
            sum: input.secondVariant.reduce(
              (acc, variant) => acc + variant.primaryMetric,
              0,
            ),
            mean: mean(
              input.secondVariant.map((variant) => variant.primaryMetric),
            ),
          },
          secondaryMetric: {
            sum: input.secondVariant.reduce(
              (acc, variant) => acc + variant.secondaryMetric,
              0,
            ),
            mean: mean(
              input.secondVariant.map((variant) => variant.secondaryMetric),
            ),
          },
        },
      });
    }

    const secondaryMetricTTestResult = this.completeTTest({
      firstVariantData: input.firstVariant.map(
        (variant) => variant.secondaryMetric,
      ),
      secondVariantData: input.secondVariant.map(
        (variant) => variant.secondaryMetric,
      ),
    });
    if (secondaryMetricTTestResult.isErr()) {
      throw new Error("Failed to complete secondary metric t-test");
    }
    if (
      secondaryMetricTTestResult.value.result !== "NO_STATISTICAL_DIFFERENCE"
    ) {
      return ok({
        decision:
          secondaryMetricTTestResult.value.result === "FIRST_VARIANT"
            ? "FIRST_VARIANT"
            : "SECOND_VARIANT",
        decisionType: "SECONDARY_METRIC_T_TEST",
        primaryMetricTTestResult: primaryMetricTTestResult.value.pValue,
        secondaryMetricTTestResult: secondaryMetricTTestResult.value.pValue,
        currentBest: {
          primaryMetric: {
            sum: input.firstVariant.reduce(
              (acc, variant) => acc + variant.primaryMetric,
              0,
            ),
            mean: mean(
              input.firstVariant.map((variant) => variant.primaryMetric),
            ),
          },
          secondaryMetric: {
            sum: input.firstVariant.reduce(
              (acc, variant) => acc + variant.secondaryMetric,
              0,
            ),
            mean: mean(
              input.firstVariant.map((variant) => variant.secondaryMetric),
            ),
          },
        },
        contender: {
          primaryMetric: {
            sum: input.secondVariant.reduce(
              (acc, variant) => acc + variant.primaryMetric,
              0,
            ),
            mean: mean(
              input.secondVariant.map((variant) => variant.primaryMetric),
            ),
          },
          secondaryMetric: {
            sum: input.secondVariant.reduce(
              (acc, variant) => acc + variant.secondaryMetric,
              0,
            ),
            mean: mean(
              input.secondVariant.map((variant) => variant.secondaryMetric),
            ),
          },
        },
      });
    }
    if (input.firstVariant.length < 5) {
      return ok({
        decision: "NO_DECISION",
        decisionType:
          "NO_STATISTICAL_DIFFERENCE_IN_PRIMARY_AND_SECONDARY_METRICS_BEFORE_5_DAYS",
        primaryMetricTTestResult: primaryMetricTTestResult.value.pValue,
        secondaryMetricTTestResult: secondaryMetricTTestResult.value.pValue,
        currentBest: {
          primaryMetric: {
            sum: input.firstVariant.reduce(
              (acc, variant) => acc + variant.primaryMetric,
              0,
            ),
            mean: mean(
              input.firstVariant.map((variant) => variant.primaryMetric),
            ),
          },
          secondaryMetric: {
            sum: input.firstVariant.reduce(
              (acc, variant) => acc + variant.secondaryMetric,
              0,
            ),
            mean: mean(
              input.firstVariant.map((variant) => variant.secondaryMetric),
            ),
          },
        },
        contender: {
          primaryMetric: {
            sum: input.secondVariant.reduce(
              (acc, variant) => acc + variant.primaryMetric,
              0,
            ),
            mean: mean(
              input.secondVariant.map((variant) => variant.primaryMetric),
            ),
          },
          secondaryMetric: {
            sum: input.secondVariant.reduce(
              (acc, variant) => acc + variant.secondaryMetric,
              0,
            ),
            mean: mean(
              input.secondVariant.map((variant) => variant.secondaryMetric),
            ),
          },
        },
      });
    }

    if (
      input.firstVariant.reduce(
        (acc, variant) => acc + variant.primaryMetric,
        0,
      ) >
      input.secondVariant.reduce(
        (acc, variant) => acc + variant.primaryMetric,
        0,
      )
    ) {
      return ok({
        decision: "FIRST_VARIANT",
        decisionType: "PRIMARY_METRIC_SUM",
        primaryMetricTTestResult: primaryMetricTTestResult.value.pValue,
        secondaryMetricTTestResult: secondaryMetricTTestResult.value.pValue,
        currentBest: {
          primaryMetric: {
            sum: input.firstVariant.reduce(
              (acc, variant) => acc + variant.primaryMetric,
              0,
            ),
            mean: mean(
              input.firstVariant.map((variant) => variant.primaryMetric),
            ),
          },
          secondaryMetric: {
            sum: input.firstVariant.reduce(
              (acc, variant) => acc + variant.secondaryMetric,
              0,
            ),
            mean: mean(
              input.firstVariant.map((variant) => variant.secondaryMetric),
            ),
          },
        },
        contender: {
          primaryMetric: {
            sum: input.secondVariant.reduce(
              (acc, variant) => acc + variant.primaryMetric,
              0,
            ),
            mean: mean(
              input.secondVariant.map((variant) => variant.primaryMetric),
            ),
          },
          secondaryMetric: {
            sum: input.secondVariant.reduce(
              (acc, variant) => acc + variant.secondaryMetric,
              0,
            ),
            mean: mean(
              input.secondVariant.map((variant) => variant.secondaryMetric),
            ),
          },
        },
      });
    } else if (
      input.firstVariant.reduce(
        (acc, variant) => acc + variant.primaryMetric,
        0,
      ) <
      input.secondVariant.reduce(
        (acc, variant) => acc + variant.primaryMetric,
        0,
      )
    ) {
      return ok({
        decision: "SECOND_VARIANT",
        decisionType: "PRIMARY_METRIC_SUM",
        primaryMetricTTestResult: primaryMetricTTestResult.value.pValue,
        secondaryMetricTTestResult: secondaryMetricTTestResult.value.pValue,
        currentBest: {
          primaryMetric: {
            sum: input.firstVariant.reduce(
              (acc, variant) => acc + variant.primaryMetric,
              0,
            ),
            mean: mean(
              input.firstVariant.map((variant) => variant.primaryMetric),
            ),
          },
          secondaryMetric: {
            sum: input.firstVariant.reduce(
              (acc, variant) => acc + variant.secondaryMetric,
              0,
            ),
            mean: mean(
              input.firstVariant.map((variant) => variant.secondaryMetric),
            ),
          },
        },
        contender: {
          primaryMetric: {
            sum: input.secondVariant.reduce(
              (acc, variant) => acc + variant.primaryMetric,
              0,
            ),
            mean: mean(
              input.secondVariant.map((variant) => variant.primaryMetric),
            ),
          },
          secondaryMetric: {
            sum: input.secondVariant.reduce(
              (acc, variant) => acc + variant.secondaryMetric,
              0,
            ),
            mean: mean(
              input.secondVariant.map((variant) => variant.secondaryMetric),
            ),
          },
        },
      });
    } else if (
      input.firstVariant.reduce(
        (acc, variant) => acc + variant.secondaryMetric,
        0,
      ) >
      input.secondVariant.reduce(
        (acc, variant) => acc + variant.secondaryMetric,
        0,
      )
    ) {
      return ok({
        decision: "FIRST_VARIANT",
        decisionType: "SECONDARY_METRIC_SUM",
        primaryMetricTTestResult: primaryMetricTTestResult.value.pValue,
        secondaryMetricTTestResult: secondaryMetricTTestResult.value.pValue,
        currentBest: {
          primaryMetric: {
            sum: input.firstVariant.reduce(
              (acc, variant) => acc + variant.primaryMetric,
              0,
            ),
            mean: mean(
              input.firstVariant.map((variant) => variant.primaryMetric),
            ),
          },
          secondaryMetric: {
            sum: input.firstVariant.reduce(
              (acc, variant) => acc + variant.secondaryMetric,
              0,
            ),
            mean: mean(
              input.firstVariant.map((variant) => variant.secondaryMetric),
            ),
          },
        },
        contender: {
          primaryMetric: {
            sum: input.secondVariant.reduce(
              (acc, variant) => acc + variant.primaryMetric,
              0,
            ),
            mean: mean(
              input.secondVariant.map((variant) => variant.primaryMetric),
            ),
          },
          secondaryMetric: {
            sum: input.secondVariant.reduce(
              (acc, variant) => acc + variant.secondaryMetric,
              0,
            ),
            mean: mean(
              input.secondVariant.map((variant) => variant.secondaryMetric),
            ),
          },
        },
      });
    } else if (
      input.firstVariant.reduce(
        (acc, variant) => acc + variant.secondaryMetric,
        0,
      ) <
      input.secondVariant.reduce(
        (acc, variant) => acc + variant.secondaryMetric,
        0,
      )
    ) {
      return ok({
        decision: "SECOND_VARIANT",
        decisionType: "SECONDARY_METRIC_SUM",
        primaryMetricTTestResult: primaryMetricTTestResult.value.pValue,
        secondaryMetricTTestResult: secondaryMetricTTestResult.value.pValue,
        currentBest: {
          primaryMetric: {
            sum: input.firstVariant.reduce(
              (acc, variant) => acc + variant.primaryMetric,
              0,
            ),
            mean: mean(
              input.firstVariant.map((variant) => variant.primaryMetric),
            ),
          },
          secondaryMetric: {
            sum: input.firstVariant.reduce(
              (acc, variant) => acc + variant.secondaryMetric,
              0,
            ),
            mean: mean(
              input.firstVariant.map((variant) => variant.secondaryMetric),
            ),
          },
        },
        contender: {
          primaryMetric: {
            sum: input.secondVariant.reduce(
              (acc, variant) => acc + variant.primaryMetric,
              0,
            ),
            mean: mean(
              input.secondVariant.map((variant) => variant.primaryMetric),
            ),
          },
          secondaryMetric: {
            sum: input.secondVariant.reduce(
              (acc, variant) => acc + variant.secondaryMetric,
              0,
            ),
            mean: mean(
              input.secondVariant.map((variant) => variant.secondaryMetric),
            ),
          },
        },
      });
    }

    const randomNumber = Math.random();
    if (randomNumber < 0.5) {
      return ok({
        decision: "FIRST_VARIANT",
        decisionType: "RANDOM",
        primaryMetricTTestResult: primaryMetricTTestResult.value.pValue,
        secondaryMetricTTestResult: secondaryMetricTTestResult.value.pValue,
        currentBest: {
          primaryMetric: {
            sum: input.firstVariant.reduce(
              (acc, variant) => acc + variant.primaryMetric,
              0,
            ),
            mean: mean(
              input.firstVariant.map((variant) => variant.primaryMetric),
            ),
          },
          secondaryMetric: {
            sum: input.firstVariant.reduce(
              (acc, variant) => acc + variant.secondaryMetric,
              0,
            ),
            mean: mean(
              input.firstVariant.map((variant) => variant.secondaryMetric),
            ),
          },
        },
        contender: {
          primaryMetric: {
            sum: input.secondVariant.reduce(
              (acc, variant) => acc + variant.primaryMetric,
              0,
            ),
            mean: mean(
              input.secondVariant.map((variant) => variant.primaryMetric),
            ),
          },
          secondaryMetric: {
            sum: input.secondVariant.reduce(
              (acc, variant) => acc + variant.secondaryMetric,
              0,
            ),
            mean: mean(
              input.secondVariant.map((variant) => variant.secondaryMetric),
            ),
          },
        },
      });
    } else {
      return ok({
        decision: "SECOND_VARIANT",
        decisionType: "RANDOM",
        primaryMetricTTestResult: primaryMetricTTestResult.value.pValue,
        secondaryMetricTTestResult: secondaryMetricTTestResult.value.pValue,
        currentBest: {
          primaryMetric: {
            sum: input.firstVariant.reduce(
              (acc, variant) => acc + variant.primaryMetric,
              0,
            ),
            mean: mean(
              input.firstVariant.map((variant) => variant.primaryMetric),
            ),
          },
          secondaryMetric: {
            sum: input.firstVariant.reduce(
              (acc, variant) => acc + variant.secondaryMetric,
              0,
            ),
            mean: mean(
              input.firstVariant.map((variant) => variant.secondaryMetric),
            ),
          },
        },
        contender: {
          primaryMetric: {
            sum: input.secondVariant.reduce(
              (acc, variant) => acc + variant.primaryMetric,
              0,
            ),
            mean: mean(
              input.secondVariant.map((variant) => variant.primaryMetric),
            ),
          },
          secondaryMetric: {
            sum: input.secondVariant.reduce(
              (acc, variant) => acc + variant.secondaryMetric,
              0,
            ),
            mean: mean(
              input.secondVariant.map((variant) => variant.secondaryMetric),
            ),
          },
        },
      });
    }
  }

  private completeTTest(input: {
    firstVariantData: number[];
    secondVariantData: number[];
    confidenceInterval?: number;
  }): Result<
    {
      result: "FIRST_VARIANT" | "SECOND_VARIANT" | "NO_STATISTICAL_DIFFERENCE";
      pValue: number;
    },
    {
      type:
        | "DATA_LENGTH_MISMATCH"
        | "DATA_LENGTH_IS_ZERO"
        | "DATA_LENGTH_IS_ONE"
        | "DATA_IS_IDENTICAL"
        | "NO_VARIANCE"
        | "NO_T_TEST_RESULT";
    }
  > {
    if (input.firstVariantData.length !== input.secondVariantData.length) {
      return err({ type: "DATA_LENGTH_MISMATCH" });
    }

    if (input.firstVariantData.length === 0) {
      return err({ type: "DATA_LENGTH_IS_ZERO" });
    }

    if (input.firstVariantData.length === 1) {
      return err({ type: "DATA_LENGTH_IS_ONE" });
    }

    if (
      this.areArraysIdentical(input.firstVariantData, input.secondVariantData)
    ) {
      return err({ type: "DATA_IS_IDENTICAL" });
    }
    if (
      !this.doesArrayHaveVariance(input.firstVariantData) &&
      !this.doesArrayHaveVariance(input.secondVariantData)
    ) {
      return err({ type: "NO_VARIANCE" });
    }

    const firstVariantMean = mean(input.firstVariantData);
    const secondVariantMean = mean(input.secondVariantData);

    const confidenceInterval = input.confidenceInterval ?? 0.95;

    const tStat = tTestTwoSample(
      input.firstVariantData,
      input.secondVariantData,
    );

    if (tStat === null) {
      return err({ type: "NO_T_TEST_RESULT" });
    }

    const df =
      input.firstVariantData.length + input.secondVariantData.length - 2;

    const pValue = 2 * jStat.studentt.cdf(-Math.abs(tStat), df);

    if (pValue < 1 - confidenceInterval) {
      if (firstVariantMean > secondVariantMean) {
        return ok({ result: "FIRST_VARIANT", pValue: pValue });
      } else {
        return ok({ result: "SECOND_VARIANT", pValue: pValue });
      }
    } else {
      return ok({ result: "NO_STATISTICAL_DIFFERENCE", pValue: pValue });
    }
  }

  private areArraysIdentical(arr1: number[], arr2: number[]): boolean {
    if (arr1.length !== arr2.length) {
      return false;
    }

    return arr1.every((value, index) => value === arr2[index]);
  }

  private doesArrayHaveVariance(arr: number[]): boolean {
    return arr.some((value) => value !== arr[0]);
  }
}
