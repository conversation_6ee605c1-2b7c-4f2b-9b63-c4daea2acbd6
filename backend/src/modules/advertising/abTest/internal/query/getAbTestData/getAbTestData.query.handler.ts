import { ITransaction } from "../../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { IAdCreativeRepository } from "../../../../application/interfaces/infrastructure/repositories/adCreative.repository.interface";
import { IAdSegmentValuePropRepository } from "../../../../application/interfaces/infrastructure/repositories/adSegmentValueProp.repository.interface";
import { IConversationSubjectCopyRepository } from "../../../../application/interfaces/infrastructure/repositories/conversationSubjectCopy.repository.interface";
import { ILinkedInAdAudienceRepository } from "../../../../application/interfaces/infrastructure/repositories/linkedInAdAudience.repository.interface";
import { LinkedInAdProgramRepositoryInterface } from "../../../../application/interfaces/infrastructure/repositories/linkedInAdProgram.repository.interface";
import { ILinkedInAdProgramCreativeRepository } from "../../../../application/interfaces/infrastructure/repositories/linkedInAdProgramCreative.repository.interface";
import { ILinkedInAdSegmentRepository } from "../../../../application/interfaces/infrastructure/repositories/linkedInAdSegment.repository.interface";
import { IStageRepository } from "../../../../application/interfaces/infrastructure/repositories/stage.repository.interface";
import { LinkedInSponsoredCreative } from "../../../../domain/entites/linkedInSponsoredCreative";
import { Stage } from "../../../../domain/entites/stage";
import { LinkedInService } from "../../../../infrastructure/services/linkedIn.service";
import { MapLinkedInStateInputToSponsoredCreativesService } from "../../../../linkedInStateOrchestrator/application/services/mapLinkedInStateInputToSponsoredCreatives.service";
import { AbTest } from "../../domain/abTest.entity";
import { AbTestRound } from "../../domain/abTestRound.entity";
import { AbTestType } from "../../domain/abTestType.valueObject";
import { AbTestDataDto } from "../../dtos/abTestData.dto";
import { IAbTestRepository } from "../../repositories/abTest.repository.interface";
import { IAbTestRoundRepository } from "../../repositories/abTestRound.repository.interface";
import { DataProviderService } from "../../services/abTestDataProviders/dataProvider.service";
import { GetAbTestDataCommand } from "./getAbTestData.query.interface";

export class GetAbTestDataQueryHandler {
  constructor(
    private readonly abTestRepository: IAbTestRepository,
    private readonly abTestRoundRepository: IAbTestRoundRepository,
    private readonly dataProvider: DataProviderService,
    private readonly mapLinkedInStateInputToSponsoredCreativesService: MapLinkedInStateInputToSponsoredCreativesService,
    private readonly linkedInService: LinkedInService,
    private readonly adAudienceRepository: ILinkedInAdAudienceRepository,
    private readonly adSegmentValuePropRepository: IAdSegmentValuePropRepository,
    private readonly adProgramCreativeRepository: ILinkedInAdProgramCreativeRepository,
    private readonly adCreateiveRepository: IAdCreativeRepository,
  ) {}
  async execute(command: GetAbTestDataCommand): Promise<AbTestDataDto | null> {
    const abTest = await this.abTestRepository.getOne(
      command.stage.id,
      command.type,
    );
    if (!abTest) {
      return null;
    }

    if (abTest.status !== "AUTO_RESOLVED") {
      return this.getAbTestDataForCurrentRunningAbTest(
        abTest,
        command.type,
        command.tx,
      );
    }
    if (abTest.status === "AUTO_RESOLVED") {
      return this.getAbTestDataForAutoResolvedAbTest(
        abTest,
        command.stage,
        command.type,
        command.tx,
      );
    }
    return null;
  }

  private async getAbTestDataForAutoResolvedAbTest(
    abTest: AbTest,
    stage: Stage,
    type: AbTestType,
    tx: ITransaction,
  ): Promise<AbTestDataDto | null> {
    if (!abTest.winnerId) {
      return null;
    }

    const linkedInStateOrchestratorInput =
      await this.dataProvider.getLinkedInStateOrchestratorInput({
        adSegmentId: stage.adSegmentId,
        abTestType: type,
        currentBestVariantId: abTest.winnerId,
        contenderVariantId: abTest.winnerId,
      });
    if (linkedInStateOrchestratorInput.isErr()) {
      return null;
    }
    if (
      linkedInStateOrchestratorInput.value.adFormatType == "SPONSORED_CONTENT"
    ) {
      const mappedSponsoredCreatives =
        await this.mapLinkedInStateInputToSponsoredCreativesService.setupLinkedInStateForSponsoredContent(
          linkedInStateOrchestratorInput.value,
        );
      const metrics = await this.linkedInService.getAnalyticsForCreatives({
        sponsoredCreativeUrns: mappedSponsoredCreatives.map(
          (creative) =>
            creative.linkedInSponsoredCreative.linkedInSponseredCreativeUrn,
        ),
        startDate: new Date("2025-01-01"),
        endDate: new Date("2027-01-01"),
        timeGranularity: "ALL",
      });
      const mappedSponsoredCreativesWithMetrics = mappedSponsoredCreatives.map(
        (creative) => {
          const metric = metrics.find(
            (metric) =>
              metric.sponsoredCreatieUrn ===
              creative.linkedInSponsoredCreative.linkedInSponseredCreativeUrn,
          );
          if (!metric) {
            throw new Error("Metrics not found");
          }
          return {
            ...creative,
            metrics: metric,
          };
        },
      );
      const winnerData = await mapForSponsoredContent(
        mappedSponsoredCreativesWithMetrics,
        type,
        abTest.winnerId,
      );
      if (!winnerData) {
        return null;
      }

      const varientMap = await this.getVarientMapForSponsoredContent({
        audienceId: winnerData.audienceId,
        valuePropId: winnerData.valuePropId,
        adProgramCreativeId: winnerData.adProgramCreativeId,
        socialPostBodyCopyType: winnerData.socialPostBodyCopyType,
        socialPostCallToActionType: winnerData.socialPostCallToActionType,
      });
      if (!varientMap) {
        return null;
      }
      return {
        abTestId: abTest.stageId,
        status: abTest.status,
        type: abTest.type,
        autoResolvedData: {
          adId: winnerData.linkedInSponsoredCreative
            .linkedInSponseredCreativeUrn,
          adName: getAdNameForSponsoredContent(
            {
              audienceName: varientMap.audienceType,
              valuePropName: varientMap.valuePropType,
              adCreativeName: varientMap.creativeType,
              socialPostBodyCopyType: varientMap.socialPostBodyCopyType,
              socialPostCallToActionType: varientMap.socialPostCallToActionType,
            },
            type,
          ),
          metrics: {
            clicks: winnerData.metrics.clicks,
            impressions: winnerData.metrics.impressions,
            cost: winnerData.metrics.costInUsd,
            leads: winnerData.metrics.oneClickLeads,
            actionClicks: winnerData.metrics.actionClicks,
            oneClickLeadFormOpens: winnerData.metrics.oneClickLeadFormOpens,
            landingPageClicks: winnerData.metrics.landingPageClicks,
            externalWebsiteConversions:
              winnerData.metrics.externalWebsiteConversions,
            videoViews: winnerData.metrics.videoViews,
            sends: winnerData.metrics.sends,
            opens: winnerData.metrics.opens,
            videoCompletions: winnerData.metrics.videoCompletions,
            videoFirstQuartileCompletions:
              winnerData.metrics.videoFirstQuartileCompletions,
            videoMidpointCompletions:
              winnerData.metrics.videoMidpointCompletions,
            videoThirdQuartileCompletions:
              winnerData.metrics.videoThirdQuartileCompletions,
            videoStarts: winnerData.metrics.videoStarts,
            totalEngagements: winnerData.metrics.totalEngagements,
            conversions: winnerData.metrics.externalWebsiteConversions,
          },
          varientId: abTest.winnerId,
          varientsUsedInSponsoredCreative: {
            adFormatType: "SPONSORED_CONTENT",
            ...varientMap,
          },
        },
        currentRound: null,
        pastRounds: [],
        upcomingRounds: [],
      };
    } else if (
      linkedInStateOrchestratorInput.value.adFormatType == "SPONSORED_INMAIL"
    ) {
      const mappedSponsoredCreatives =
        await this.mapLinkedInStateInputToSponsoredCreativesService.setupLinkedInStateForSponsoredInmail(
          linkedInStateOrchestratorInput.value,
        );
      const metrics = await this.linkedInService.getAnalyticsForCreatives({
        sponsoredCreativeUrns: mappedSponsoredCreatives.map(
          (creative) =>
            creative.linkedInSponsoredCreative.linkedInSponseredCreativeUrn,
        ),
        startDate: new Date("2025-01-01"),
        endDate: new Date("2027-01-01"),
        timeGranularity: "ALL",
      });
      const mappedSponsoredCreativesWithMetrics = mappedSponsoredCreatives.map(
        (creative) => {
          const metric = metrics.find(
            (metric) =>
              metric.sponsoredCreatieUrn ===
              creative.linkedInSponsoredCreative.linkedInSponseredCreativeUrn,
          );
          if (!metric) {
            throw new Error("Metrics not found");
          }
          return {
            ...creative,
            metrics: metric,
          };
        },
      );
      const winnerData = await mapForSponsoredInmail(
        mappedSponsoredCreativesWithMetrics,
        type,
        abTest.winnerId,
      );
      if (!winnerData) {
        return null;
      }
      const varientMap = await this.getVarientMapForSponsoredInmail({
        audienceId: winnerData.audienceId,
        valuePropId: winnerData.valuePropId,
        conversationSubjectCopyType: winnerData.conversationSubjectCopyType,
        conversationMessageCopyType: winnerData.conversationMessageCopyType,
        conversationCallToActionType:
          winnerData.conversationCallToActionCopyType,
      });
      if (!varientMap) {
        return null;
      }
      return {
        abTestId: abTest.stageId,
        status: abTest.status,
        type: abTest.type,
        autoResolvedData: {
          adId: winnerData.linkedInSponsoredCreative
            .linkedInSponseredCreativeUrn,
          adName: getAdNameForSponsoredInmail(
            {
              audienceName: varientMap.audienceType,
              valuePropName: varientMap.valuePropType,
              conversationSubjectCopyType:
                varientMap.conversationSubjectCopyType,
              conversationMessageCopyType:
                varientMap.conversationMessageCopyType,
              conversationCallToActionCopyType:
                varientMap.conversationCallToActionType,
            },
            type,
          ),
          metrics: {
            clicks: winnerData.metrics.clicks,
            impressions: winnerData.metrics.impressions,
            cost: winnerData.metrics.costInUsd,
            leads: winnerData.metrics.oneClickLeads,
            actionClicks: winnerData.metrics.actionClicks,
            oneClickLeadFormOpens: winnerData.metrics.oneClickLeadFormOpens,
            landingPageClicks: winnerData.metrics.landingPageClicks,
            externalWebsiteConversions:
              winnerData.metrics.externalWebsiteConversions,
            videoViews: winnerData.metrics.videoViews,
            sends: winnerData.metrics.sends,
            opens: winnerData.metrics.opens,
            videoCompletions: winnerData.metrics.videoCompletions,
            videoFirstQuartileCompletions:
              winnerData.metrics.videoFirstQuartileCompletions,
            videoMidpointCompletions:
              winnerData.metrics.videoMidpointCompletions,
            videoThirdQuartileCompletions:
              winnerData.metrics.videoThirdQuartileCompletions,
            videoStarts: winnerData.metrics.videoStarts,
            totalEngagements: winnerData.metrics.totalEngagements,
            conversions: winnerData.metrics.externalWebsiteConversions,
          },
          varientId: abTest.winnerId,
          varientsUsedInSponsoredCreative: {
            adFormatType: "SPONSORED_INMAIL",
            ...varientMap,
          },
        },
        currentRound: null,
        pastRounds: [],
        upcomingRounds: [],
      };
    }
    return null;
  }

  private async getAbTestDataForCurrentRunningAbTest(
    abTest: AbTest,
    type: AbTestType,
    tx: ITransaction,
  ): Promise<AbTestDataDto | null> {
    const [abTestRounds] = await Promise.all([
      this.abTestRoundRepository.getAllForAbTest(abTest.stageId, type, tx),
    ]);

    const currentRound = abTestRounds.find(
      (round) => round.status === "IN_PROGRESS",
    );

    const pastRounds = abTestRounds.filter(
      (round) =>
        round.status == "COMPLETED" ||
        round.status === "AUTO_RESOLVED" ||
        round.status === "USER_RESOLVED" ||
        round.status === "FAILED" ||
        round.status === "CANCELLED",
    );

    const upcomingRounds = abTestRounds.filter(
      (round) => round.status === "NOT_STARTED",
    );

    const res: AbTestDataDto = {
      abTestId: abTest.stageId,
      status: abTest.status,
      type: abTest.type,
      currentRound: currentRound
        ? {
            id: currentRound.id,
            roundIndex: currentRound.roundIndex,
            currentBestId: currentRound.currentBestId,
            contenderId: currentRound.contenderId,
          }
        : null,
      autoResolvedData: null,
      pastRounds: pastRounds.map((round) => ({
        id: round.id,
        roundIndex: round.roundIndex,
        currentBestId: round.currentBestId,
        contenderId: round.contenderId,
        winner: round.winner == "CONTENDER" ? "CONTENDER" : "CURRENT_BEST",
      })),
      upcomingRounds: upcomingRounds.map((round) => ({
        id: round.id,
        roundIndex: round.roundIndex,
        currentBestId: round.currentBestId,
        contenderId: round.contenderId,
      })),
    };
    return res;
  }

  async getVarientMapForSponsoredContent(input: {
    audienceId: string;
    valuePropId: string;
    adProgramCreativeId: string;
    socialPostBodyCopyType: string;
    socialPostCallToActionType: string;
  }) {
    const [audience, valueProp, adProgramCreative] = await Promise.all([
      this.adAudienceRepository.getOne(input.audienceId),
      this.adSegmentValuePropRepository.getOne(input.valuePropId, "ACTIVE"),
      this.adProgramCreativeRepository.getOne(input.adProgramCreativeId),
    ]);
    if (!audience || !valueProp || !adProgramCreative) {
      return null;
    }

    const adCreative = await this.adCreateiveRepository.getOneById(
      adProgramCreative.adCreativeId,
    );
    if (!adCreative) {
      return null;
    }

    return {
      audienceType: audience.audienceTargetCriteria.include.and
        .map((each) => each.or.map((each) => each.facetName))
        .join(", "),
      valuePropType: valueProp.valueProp,
      creativeType: adCreative.fileName,
      socialPostBodyCopyType: input.socialPostBodyCopyType,
      socialPostCallToActionType: input.socialPostCallToActionType,
    };
  }

  async getVarientMapForSponsoredInmail(input: {
    audienceId: string;
    valuePropId: string;
    conversationSubjectCopyType: string;
    conversationMessageCopyType: string;
    conversationCallToActionType: string;
  }) {
    const [audience, valueProp] = await Promise.all([
      this.adAudienceRepository.getOne(input.audienceId),
      this.adSegmentValuePropRepository.getOne(input.valuePropId, "ACTIVE"),
    ]);
    if (!audience || !valueProp) {
      return null;
    }

    return {
      audienceType: audience.audienceTargetCriteria.include.and
        .map((each) => each.or.map((each) => each.facetName))
        .join(", "),
      valuePropType: valueProp.valueProp,
      conversationSubjectCopyType: input.conversationSubjectCopyType,
      conversationMessageCopyType: input.conversationMessageCopyType,
      conversationCallToActionType: input.conversationCallToActionType,
    };
  }
}

function getAdNameForSponsoredContent(
  input: {
    audienceName: string;
    valuePropName: string;
    adCreativeName: string;
    socialPostBodyCopyType: string;
    socialPostCallToActionType: string;
  },
  type: AbTestType,
) {
  if (type == "audience") {
    return input.audienceName;
  } else if (type == "valueProp") {
    return input.valuePropName;
  } else if (type == "creative") {
    return input.adCreativeName;
  } else if (type == "socialPostBodyCopy") {
    return input.socialPostBodyCopyType;
  } else if (type == "socialPostCallToAction") {
    return input.socialPostCallToActionType;
  } else {
    throw new Error("Invalid type");
  }
}

function getAdNameForSponsoredInmail(
  input: {
    audienceName: string;
    valuePropName: string;
    conversationSubjectCopyType: string;
    conversationMessageCopyType: string;
    conversationCallToActionCopyType: string;
  },
  type: AbTestType,
) {
  if (type == "audience") {
    return input.audienceName;
  } else if (type == "valueProp") {
    return input.valuePropName;
  } else if (type == "conversationSubject") {
    return input.conversationSubjectCopyType;
  } else if (type == "conversationMessageCopy") {
    return input.conversationMessageCopyType;
  } else if (type == "conversationCallToAction") {
    return input.conversationCallToActionCopyType;
  } else {
    throw new Error("Invalid type");
  }
}

async function mapForSponsoredContent(
  input: {
    linkedInSponsoredCreative: LinkedInSponsoredCreative;
    audienceId: string;
    valuePropId: string;
    adProgramCreativeId: string;
    socialPostBodyCopyType: string;
    socialPostCallToActionType: string;
    metrics: {
      sponsoredCreatieUrn: string;
      clicks: number;
      impressions: number;
      costInUsd: number;
      oneClickLeads: number;
      actionClicks: number;
      oneClickLeadFormOpens: number;
      landingPageClicks: number;
      externalWebsiteConversions: number;
      videoViews: number;
      sends: number;
      opens: number;
      videoCompletions: number;
      videoFirstQuartileCompletions: number;
      videoMidpointCompletions: number;
      videoThirdQuartileCompletions: number;
      videoStarts: number;
      totalEngagements: number;
    };
  }[],
  type: AbTestType,
  target: string,
) {
  if (type == "audience") {
    return input.find((each) => each.audienceId == target);
  } else if (type == "valueProp") {
    return input.find((each) => each.valuePropId == target);
  } else if (type == "creative") {
    return input.find((each) => each.adProgramCreativeId == target);
  } else if (type == "socialPostBodyCopy") {
    return input.find((each) => each.socialPostBodyCopyType == target);
  } else if (type == "socialPostCallToAction") {
    return input.find((each) => each.socialPostCallToActionType == target);
  } else {
    return null;
  }
}

async function mapForSponsoredInmail(
  input: {
    linkedInSponsoredCreative: LinkedInSponsoredCreative;
    audienceId: string;
    valuePropId: string;
    conversationSubjectCopyType: string;
    conversationMessageCopyType: string;
    conversationCallToActionCopyType: string;
    metrics: {
      sponsoredCreatieUrn: string;
      clicks: number;
      impressions: number;
      costInUsd: number;
      oneClickLeads: number;
      actionClicks: number;
      oneClickLeadFormOpens: number;
      landingPageClicks: number;
      externalWebsiteConversions: number;
      videoViews: number;
      sends: number;
      opens: number;
      videoCompletions: number;
      videoFirstQuartileCompletions: number;
      videoMidpointCompletions: number;
      videoThirdQuartileCompletions: number;
      videoStarts: number;
      totalEngagements: number;
    };
  }[],
  type: AbTestType,
  target: string,
) {
  if (type == "audience") {
    return input.find((each) => each.audienceId == target);
  } else if (type == "valueProp") {
    return input.find((each) => each.valuePropId == target);
  } else if (type == "conversationSubject") {
    return input.find((each) => each.conversationSubjectCopyType == target);
  } else if (type == "conversationMessageCopy") {
    return input.find((each) => each.conversationMessageCopyType == target);
  } else if (type == "conversationCallToAction") {
    return input.find(
      (each) => each.conversationCallToActionCopyType == target,
    );
  } else {
    return null;
  }
}
