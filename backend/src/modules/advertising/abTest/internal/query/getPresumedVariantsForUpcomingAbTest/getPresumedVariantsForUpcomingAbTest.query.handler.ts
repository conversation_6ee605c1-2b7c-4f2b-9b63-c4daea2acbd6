import { AbTestType } from "../../domain/abTestType.valueObject";
import { IAbTestRepository } from "../../repositories/abTest.repository.interface";
import { DataProviderService } from "../../services/abTestDataProviders/dataProvider.service";
import { getPresumedVariantsForUpcomingAbTestQuery } from "./getPresumedVariantsForUpcomingAbTest.query.interface";

export class GetPresumedVariantsForUpcomingAbTestQueryHandler {
  constructor(
    private readonly abTestRepository: IAbTestRepository,
    private readonly dataProvider: DataProviderService,
  ) {}

  async execute(query: getPresumedVariantsForUpcomingAbTestQuery) {
    const abTest = await this.abTestRepository.getOne(
      query.stage.id,
      query.type,
      query.tx,
    );
    if (abTest) {
      if (abTest.status !== "NOT_STARTED") {
        return null;
      }
    }
    let abTestType: AbTestType | null = null;
    if (query.stage.stageType === "audienceTest") {
      abTestType = "audience";
    } else if (query.stage.stageType === "valuePropTest") {
      abTestType = "valueProp";
    } else if (query.stage.stageType === "creativeTest") {
      abTestType = "creative";
    } else if (query.stage.stageType === "conversationSubjectTest") {
      abTestType = "conversationSubject";
    } else if (query.stage.stageType === "conversationMessageCopyTest") {
      abTestType = "conversationMessageCopy";
    } else if (query.stage.stageType === "conversationCallToActionTest") {
      abTestType = "conversationCallToAction";
    } else if (query.stage.stageType === "socialPostBodyCopyTest") {
      abTestType = "socialPostBodyCopy";
    } else {
      throw new Error("Invalid stage type");
    }
    if (abTestType !== query.type) {
      throw new Error("Invalid ab test type");
    }

    const variants = await this.dataProvider.getVariantsToSetupRounds({
      adSegmentId: query.stage.adSegmentId,
      abTestType: query.type,
    });

    if (variants.isErr()) {
      return null;
    }

    if (variants.value.length === 0) {
      throw new Error("No variants found");
      return null;
    }

    const names = await this.dataProvider.getNamesFromVariantIds({
      variantIds: variants.value,
      abTestType: query.type,
    });
    if (names.length === 0) {
      throw new Error("No names found");
      return null;
    }
    return names;
  }
}
