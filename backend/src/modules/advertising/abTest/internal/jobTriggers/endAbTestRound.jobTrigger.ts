import { z } from "zod";

import { defineJobTrigger } from "../../../../shared/jobTrigger.interface";
import { abTestTypeSchema } from "../domain/abTestType.valueObject";

export const endAbTestRoundJobTrigger = defineJobTrigger(
  "ab-test.end-ab-test-round",
  z.object({
    abTestRoundId: z.string(),
    abTestType: abTestTypeSchema,
    winner: z.enum(["CURRENT_BEST", "CONTENDER"]),
    adSegmentId: z.string(),
  }),
);
