import { err, ok, Result } from "neverthrow";

import { Transaction } from "../../../../../../database/db";
import { createUuid } from "../../../../../core/utils/uuid";
import { IJobTriggerPublisher } from "../../../../../shared/jobTriggerPublisher.interface";
import { ILinkedInAdAccountRepository } from "../../../../application/interfaces/infrastructure/repositories/linkedInAdAccount.repository.interface";
import { LinkedInAdProgramRepositoryInterface } from "../../../../application/interfaces/infrastructure/repositories/linkedInAdProgram.repository.interface";
import { ILinkedInAdSegmentRepository } from "../../../../application/interfaces/infrastructure/repositories/linkedInAdSegment.repository.interface";
import { IStageRepository } from "../../../../application/interfaces/infrastructure/repositories/stage.repository.interface";
import { AdSegment } from "../../../../domain/entites/adSegment";
import { AbTestDomain } from "../../domain/abTest.domain";
import { waitForStageUserInputTimeoutJobTrigger } from "../../jobTriggers/waitForStageUserInputTimeout.jobTrigger";
import { IAbTestRepository } from "../../repositories/abTest.repository.interface";
import { IAbTestRoundRepository } from "../../repositories/abTestRound.repository.interface";
import { AdSegmentBestVariantRepository } from "../../repositories/adSegmentBestVariant.repository";
import { IAbTestCompletionService } from "../../services/abTestCompletion.service";
import { RunWinnerService } from "../../services/runWinner.service";
import { EndAbTestCommand } from "./endAbTest.command.interface";

type EndAbTestError = {
  type:
    | "AB_TEST_NOT_FOUND"
    | "AB_TEST_NOT_IN_PROGRESS"
    | "NOT_ALL_AB_TEST_ROUNDS_COMPLETED";
};

interface EndAbTestResult {
  shouldPauseForUserInput: boolean;
  completionData?: {
    adSegmentId: string;
    winnerId: string;
    totalRounds: number;
  };
}

export class EndAbTestCommandHandler {
  constructor(
    private readonly ctx: {
      abTestRepository: IAbTestRepository;
      abTestRoundRepository: IAbTestRoundRepository;
      abTestCompletionService: IAbTestCompletionService;
      stageRepository: IStageRepository;
      runWinnerService: RunWinnerService;
      adSegmentRepository: ILinkedInAdSegmentRepository;
      adProgramRepository: LinkedInAdProgramRepositoryInterface;
      adAccountRepository?: ILinkedInAdAccountRepository;
      jobTriggerPublisher?: IJobTriggerPublisher;
    },
  ) {}

  async execute(
    command: EndAbTestCommand,
  ): Promise<Result<EndAbTestResult, EndAbTestError>> {
    const abTest = await this.ctx.abTestRepository.getOne(
      command.abTestId,
      command.abTestType,
    );
    if (!abTest) {
      return err({ type: "AB_TEST_NOT_FOUND" });
    }

    if (abTest.status !== "IN_PROGRESS") {
      return err({ type: "AB_TEST_NOT_IN_PROGRESS" });
    }

    const abTestRounds = await this.ctx.abTestRoundRepository.getAllForAbTest(
      command.abTestId,
      command.abTestType,
      command.tx,
    );

    const completedAbTest = AbTestDomain.complete({
      abTest: abTest,
      abTestRoundsForAbTest: abTestRounds,
    });
    if (completedAbTest.isErr()) {
      return err(completedAbTest.error);
    }

    await this.ctx.abTestRepository.updateOne(
      completedAbTest.value,
      command.tx,
    );

    // Handle test completion using domain service
    let shouldPauseForUserInput = false;
    let completionData: any = undefined;

    if (this.ctx.abTestCompletionService) {
      const completionResult =
        await this.ctx.abTestCompletionService.handleTestCompletion({
          abTest: completedAbTest.value,
          abTestType: command.abTestType,
          abTestRounds,
          tx: command.tx,
        });

      if (completionResult.isOk()) {
        shouldPauseForUserInput =
          completionResult.value.shouldPauseForUserInput;
        completionData = completionResult.value.completionData;
      }
    }

    const adSegmentBestVariant = new AdSegmentBestVariantRepository();

    const stage = await this.ctx.stageRepository.getStage(abTest.stageId);
    if (!stage) {
      throw new Error("Stage not found");
    }

    if (completedAbTest.value.winnerId == null) {
      throw new Error("Winner not found");
    }

    await adSegmentBestVariant.upsertOne(
      {
        id: createUuid(),
        adSegmentId: stage.adSegmentId,
        type: command.abTestType,
        variantId: completedAbTest.value.winnerId,
      },
      command.tx as Transaction,
    );

    const adSegment = await this.ctx.adSegmentRepository.getOne(
      stage.adSegmentId,
      command.tx,
    );
    if (!adSegment) {
      throw new Error("AdSegment not found");
    }

    const adProgram = await this.ctx.adProgramRepository.getOne(
      adSegment.linkedInAdProgramId,
    );
    if (!adProgram) {
      throw new Error("AdProgram not found");
    }

    // Only run winner service (deactivate contender) when NOT pausing for user input
    if (!shouldPauseForUserInput) {
      await this.ctx.runWinnerService.execute({
        abTest: completedAbTest.value,
        adSegmentId: stage.adSegmentId,
        adProgramType: adProgram.adFormat.type,
        tx: command.tx,
      });
    }

    // Schedule timeout job if pausing for user input
    if (
      shouldPauseForUserInput &&
      this.ctx.jobTriggerPublisher &&
      this.ctx.adAccountRepository
    ) {
      console.log(
        `[Stage Timeout] Scheduling user action timeout for stage ${stage.id}`,
      );

      // Get organization ID from ad account
      const adAccount = await this.ctx.adAccountRepository.getOneById(
        adProgram.linkedInAdAccountId,
      );

      if (adAccount?.organizationId) {
        // Schedule timeout job to automatically advance to next stage in 3 days
        this.ctx.jobTriggerPublisher
          .publish(
            waitForStageUserInputTimeoutJobTrigger.build({
              stageId: stage.id,
              adSegmentId: stage.adSegmentId,
              organizationId: adAccount.organizationId,
            }),
          )
          .catch((error) => {
            console.error(
              `[Stage Timeout] Failed to schedule timeout for stage ${stage.id}:`,
              error,
            );
          });
      } else {
        console.error(
          `[Stage Timeout] Could not get organization ID for stage ${stage.id}, timeout not scheduled`,
        );
      }
    }

    return ok({
      shouldPauseForUserInput,
      completionData,
    });
  }
}
