import { err, ok, Result } from "neverthrow";

import { IStageRepository } from "../../../../application/interfaces/infrastructure/repositories/stage.repository.interface";
import { Stage } from "../../../../domain/entites/stage";
import { createUuid } from "../../../../../core/utils/uuid";
import { AbTestService } from "../../../../domain/services/abTest/abTest.service";
import { AbTestDomain } from "../../domain/abTest.domain";
import { AbTest } from "../../domain/abTest.entity";
import { AbTestRoundDomain } from "../../domain/abTestRound.domain";
import { IAbTestRepository } from "../../repositories/abTest.repository.interface";
import { IAbTestRoundRepository } from "../../repositories/abTestRound.repository.interface";
import { IJobTriggerPublisher } from "../../../../../shared/jobTriggerPublisher.interface";
import { IVariantValidationService } from "../../services/variantValidation.service";
import { IVariantAdditionRoundService } from "../../services/variantAdditionRound.service";
import { AddVariantToRunningTestCommand } from "./addVariantToRunningTest.command.interface";

type AddVariantToRunningTestError = {
  type:
    | "AB_TEST_NOT_FOUND"
    | "AB_TEST_NOT_IN_DESIRED_STATE"
    | "VARIANT_ALREADY_EXISTS"
    | "VARIANT_NOT_FOUND"
    | "VARIANT_NOT_AUTHORIZED"
    | "VALIDATION_FAILED"
    | "FAILED_TO_ADD_VARIANT"
    | "FAILED_TO_CREATE_NEW_TEST";
};

// Track operations for rollback
interface CommandState {
  reactivatedTest?: AbTest;
  createdRoundId?: string;
  wasTestReactivated: boolean;
}

export class AddVariantToRunningTestCommandHandler {
  constructor(
    private readonly abTestRepository: IAbTestRepository,
    private readonly abTestRoundRepository: IAbTestRoundRepository,
    private readonly stageRepository: IStageRepository,
    private readonly variantValidationService: IVariantValidationService,
    private readonly variantAdditionRoundService: IVariantAdditionRoundService,
    private readonly abTestService: AbTestService,
  ) {}

  async execute(
    command: AddVariantToRunningTestCommand,
  ): Promise<
    Result<
      { 
        newRoundId?: string;
        currentLeaderId: string; 
        wasQueued: boolean;
        isNewTest: boolean;
      },
      AddVariantToRunningTestError
    >
  > {
    const commandState: CommandState = { wasTestReactivated: false };
    
    try {
      // Get the AB test
      const abTest = await this.abTestRepository.getOne(
        command.abTestId,
        command.abTestType,
        command.tx,
      );

      if (!abTest) {
        return err({ type: "AB_TEST_NOT_FOUND" });
      }

      if (abTest.status !== "IN_PROGRESS" && abTest.status !== "CANCELLED" && abTest.status !== "COMPLETED") {
        return err({ type: "AB_TEST_NOT_IN_DESIRED_STATE" });
      }

      console.log(`[AddVariant] ${command.abTestType} test detected:\n${JSON.stringify(abTest, null, 2)}`);

      // Get all rounds for this AB test
      const rounds = await this.abTestRoundRepository.getAllForAbTest(
        command.abTestId,
        command.abTestType,
        command.tx,
      );

      // Check if variant already exists in the test and validate variant
      const existingVariants = new Set<string>();
      rounds.forEach(round => {
        existingVariants.add(round.currentBestId);
        existingVariants.add(round.contenderId);
      });

      // Get stage information for organization context
      const stage = await this.stageRepository.getStage(command.abTestId, command.tx);
      if (!stage) {
        return err({ type: "FAILED_TO_ADD_VARIANT" });
      }

      // Use organizationId from command - passed down from use case
      const organizationId = command.organizationId;
      
      // Use validation service to validate the variant
      const variantValidationResult = await this.variantValidationService.validateVariantForAddition({
        newVariantId: command.newVariantId,
        organizationId,
        abTestType: command.abTestType,
        existingVariants,
        tx: command.tx,
      });

      if (variantValidationResult.isErr()) {
        const errorMap = {
          "VARIANT_NOT_FOUND": "VARIANT_NOT_FOUND",
          "VARIANT_NOT_AUTHORIZED": "VARIANT_NOT_AUTHORIZED", 
          "VARIANT_ALREADY_EXISTS": "VARIANT_ALREADY_EXISTS",
          "VALIDATION_FAILED": "VALIDATION_FAILED",
        } as const;
        
        return err({ 
          type: errorMap[variantValidationResult.error.type] 
        });
      }

      const { variant } = variantValidationResult.value;

      // If the AB test is cancelled or completed, check if we can still add to the stage
      if (abTest.status === "CANCELLED" || abTest.status === "COMPLETED") {
        console.log(`[AddVariant] ${abTest.status} test detected - checking if ${stage.stageType} stage is still active`);
        
        // Check if this stage type matches the test type and if it's still running
        const expectedStageType = this.abTestService.getExpectedStageType(command.abTestType);
        if (stage.stageType !== expectedStageType) {
          console.log(`[AddVariant] Stage is not ${expectedStageType} type (${stage.stageType}) - cannot add new variant`);
          return err({ type: "FAILED_TO_ADD_VARIANT" });
        }

        if (stage.status !== "RUNNING") {
          console.log(`[AddVariant] ${stage.stageType} stage is not running (${stage.status}) - campaign has moved on`);
          return err({ type: "FAILED_TO_ADD_VARIANT" });
        }

        console.log(`[AddVariant] ${stage.stageType} stage is still running - creating new test in same stage`);

        // Determine the current best performer from completed test
        const currentBestId = this.abTestService.determineCurrentBestPerformer(rounds, abTest);

        // Reactivate the existing test and add new round
        try {
          console.log(`[AddVariant] Reactivating existing test and adding new round`);
          
          // Update the existing test status to IN_PROGRESS
          const reactivatedTestResult = AbTestDomain.reactivate({
            abTest,
          });
          if (reactivatedTestResult.isErr()) {
            return err({ type: "FAILED_TO_ADD_VARIANT" });
          }
          
          await this.abTestRepository.updateOne(reactivatedTestResult.value, command.tx);
          commandState.reactivatedTest = reactivatedTestResult.value;
          commandState.wasTestReactivated = true;

          // Create and start new round using variant addition service
          const roundResult = await this.variantAdditionRoundService.createVariantAdditionRound({
            abTestId: command.abTestId,
            abTestType: command.abTestType,
            currentBestId,
            contenderId: command.newVariantId,
            rounds,
            shouldStartImmediately: true,
            adSegmentId: stage.adSegmentId,
            tx: command.tx,
          });

          if (roundResult.isErr()) {
            // Rollback test reactivation before returning error
            await this.rollbackTestOperations(commandState, command);
            return err({ type: "FAILED_TO_ADD_VARIANT" });
          }

          commandState.createdRoundId = roundResult.value.newRoundId;

          return ok({
            newRoundId: roundResult.value.newRoundId,
            currentLeaderId: currentBestId,
            wasQueued: roundResult.value.wasQueued,
            isNewTest: false, // Reactivating existing test
          });
        } catch (error) {
          console.error("Failed to reactivate test and add new round:", error);
          // Rollback any partial operations
          await this.rollbackTestOperations(commandState, command);
          return err({ type: "FAILED_TO_ADD_VARIANT" });
        }
      }

      console.log(`[AddVariant] IN_PROGRESS test - checking for running rounds`);
      // For IN_PROGRESS tests, check if there's actually a round running
      const runningRounds = rounds.filter(round => round.status === "IN_PROGRESS");
      const completedRounds = rounds
        .filter(round => round.status === "COMPLETED")
        .sort((a, b) => a.roundIndex - b.roundIndex);

      // Determine the current best performer
      let currentBestId: string;
      let wasQueued: boolean;

      if (runningRounds.length > 0) {
        // There's a round currently running - new round will be queued
        console.log(`[AddVariant] Round running - queueing new round`);
        const runningRound = runningRounds[0];
        if (!runningRound) {
          return err({ type: "FAILED_TO_ADD_VARIANT" });
        }
        currentBestId = runningRound.currentBestId;
        wasQueued = true;
      } else {
        // No round is currently running for an IN_PROGRESS test
        console.log(`[AddVariant] No round running - will trigger new round`);
        currentBestId = this.abTestService.determineCurrentBestPerformer(rounds, abTest);
        wasQueued = false;
      }

      // Create new round: current best vs new variant
      try {
        const roundResult = await this.variantAdditionRoundService.createVariantAdditionRound({
          abTestId: command.abTestId,
          abTestType: command.abTestType,
          adSegmentId: stage.adSegmentId,
          currentBestId,
          contenderId: command.newVariantId,
          rounds,
          shouldStartImmediately: !wasQueued, // Start immediately if no round is running
          tx: command.tx,
        });

        if (roundResult.isErr()) {
          return err({ type: "FAILED_TO_ADD_VARIANT" });
        }

        commandState.createdRoundId = roundResult.value.newRoundId;

        return ok({
          newRoundId: roundResult.value.newRoundId,
          currentLeaderId: currentBestId,
          wasQueued: roundResult.value.wasQueued,
          isNewTest: false,
        });
      } catch (error) {
        console.error("Failed to create variant addition round:", error);
        // Rollback any partial operations
        await this.rollbackTestOperations(commandState, command);
        return err({ type: "FAILED_TO_ADD_VARIANT" });
      }
    } catch (error) {
      console.error("Unexpected error in AddVariantToRunningTestCommandHandler:", error);
      // Rollback any partial operations
      await this.rollbackTestOperations(commandState, command);
      return err({ type: "FAILED_TO_ADD_VARIANT" });
    }
  }

  /**
   * Rollback test operations in case of failure
   */
  private async rollbackTestOperations(
    commandState: CommandState,
    command: AddVariantToRunningTestCommand,
  ): Promise<void> {
    console.log("Rolling back test operations...", commandState);
    
    try {
      // If we reactivated a test, revert it back to its original status
      if (commandState.wasTestReactivated && commandState.reactivatedTest) {
        const originalTest = await this.abTestRepository.getOne(
          command.abTestId,
          command.abTestType,
          command.tx,
        );
        
        if (originalTest) {
          // Revert the test status to its original state
          const revertedTest = {
            ...commandState.reactivatedTest,
            status: originalTest.status, // Keep the original status
          };
          
          try {
            await this.abTestRepository.updateOne(revertedTest as AbTest, command.tx);
            console.log(`Reverted AB test ${command.abTestId} to original status`);
          } catch (revertError) {
            console.error(`Failed to revert AB test status:`, revertError);
          }
        }
      }

      // Note: We don't rollback created rounds here as the variant addition service
      // should handle its own cleanup. If needed, specific round cleanup logic
      // could be added here in coordination with the variant addition service.
      
      console.log("Test operations rollback completed");
    } catch (rollbackError) {
      console.error("Critical error during test operations rollback:", rollbackError);
      // Log error but don't throw to avoid masking original error
    }
  }
} 