import { createUuid } from "../../../../../core/utils/uuid";
import { IStageRepository } from "../../../../application/interfaces/infrastructure/repositories/stage.repository.interface";
import { Stage } from "../../../../domain/entites/stage";
import { advertisingInngestClient } from "../../../../utils/advertisingInngestClient";
import { IAbTestRepository } from "../../repositories/abTest.repository.interface";
import { IAbTestRoundRepository } from "../../repositories/abTestRound.repository.interface";
import { RunDifferentAbTestCommand } from "./runDifferentAbTest.command.interface";

export class RunDifferentAbTestCommandHandler {
  constructor(
    private readonly stageRepository: IStageRepository,
    private readonly abTestRepository: IAbTestRepository,
  ) {}
  async execute(command: RunDifferentAbTestCommand) {
    const stages = await this.stageRepository.getStagesForAdSegment(
      command.adSegmentId,
    );
    const stage = stages.find((stage) => stage.status === "RUNNING");
    if (stage) {
      const abTest = await this.abTestRepository.getOne(
        stage.id,
        command.abTestType,
      );
      if (abTest) {
        await this.abTestRepository.updateOne(
          {
            status: "CANCELLED",
            stageId: stage.id,
            type: command.abTestType,
            winnerId: null,
          },
          command.tx,
        );
      }
      await this.stageRepository.updateStageStatus(
        stage.id,
        "FINISHED",
        command.tx,
      );

      const notStartedStages = stages.filter(
        (stage) => stage.status === "NOT_STATED",
      );
      for (const stage of notStartedStages) {
        await this.stageRepository.updateStage({
          ...stage,
          index: stage.index + 1,
        });
      }

      await advertisingInngestClient.send({
        name: "stages/cancel",
        data: {
          adSegmentId: command.adSegmentId,
        },
      });
    }
    let stageType: Stage["stageType"] | null = null;
    switch (command.abTestType) {
      case "audience":
        stageType = "audienceTest";
        break;
      case "valueProp":
        stageType = "valuePropTest";
        break;
      case "creative":
        stageType = "creativeTest";
        break;
      case "conversationSubject":
        stageType = "conversationSubjectTest";
        break;
      case "conversationMessageCopy":
        stageType = "conversationMessageCopyTest";
        break;
      case "conversationCallToAction":
        stageType = "conversationCallToActionTest";
        break;
      case "socialPostBodyCopy":
        stageType = "socialPostBodyCopyTest";
        break;
      case "socialPostCallToAction":
        stageType = "socialPostCallToActionTest";
        break;
      default:
        throw new Error("Invalid ab test type");
    }

    const lastStage = stages.sort((a, b) => a.index - b.index)[
      stages.length - 1
    ];

    await this.stageRepository.createStage(
      {
        id: createUuid(),
        adSegmentId: command.adSegmentId,
        stageType: stageType,
        index: stage ? stage.index + 1 : lastStage ? lastStage.index + 1 : 1,
        status: "NOT_STATED",
      },
      command.tx,
    );
    await advertisingInngestClient.send({
      name: "linkedin/stage.run",
      data: {
        adSegmentId: command.adSegmentId,
        organizationId: command.organizationId,
      },
    });
  }
}
