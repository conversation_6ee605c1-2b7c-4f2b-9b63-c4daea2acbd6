import { IAbTestRepository } from "../../repositories/abTest.repository.interface";
import { IAbTestRoundRepository } from "../../repositories/abTestRound.repository.interface";
import { RemoveAbTestRoundCommand } from "./removeAbTestRound.command.interface";

export class RemoveAbTestRoundCommandHandler {
  constructor(
    private readonly abTestRepository: IAbTestRepository,
    private readonly abTestRoundRepository: IAbTestRoundRepository,
  ) {}
  async execute(command: RemoveAbTestRoundCommand) {
    const abTestRound = await this.abTestRoundRepository.getOne(
      command.abTestRoundId,
      command.abTestType,
      command.tx,
    );
    if (!abTestRound) {
      throw new Error("AbTestRound not found");
    }

    const abTest = await this.abTestRepository.getOne(
      abTestRound.abTestId,
      command.abTestType,
      command.tx,
    );
    if (!abTest) {
      throw new Error("AbTest not found");
    }

    if (abTest.status !== "IN_PROGRESS") {
      throw new Error("AbTest is not in progress");
    }

    if (abTestRound.status !== "NOT_STARTED") {
      throw new Error("AbTestRound is not not started");
    }

    await this.abTestRoundRepository.deleteOne(
      command.abTestRoundId,
      command.abTestType,
      command.tx,
    );
  }
}
