import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { AbTestRound } from "../domain/abTestRound.entity";
import { AbTestType } from "../domain/abTestType.valueObject";

export interface IAbTestRoundRepository {
  getAllForAbTest(
    abTestId: string,
    type: AbTestType,
    tx: ITransaction,
  ): Promise<AbTestRound[]>;

  createMany(
    rounds: AbTestRound[],
    type: AbTestType,
    tx: ITransaction,
  ): Promise<AbTestRound[]>;

  update(
    round: AbTestRound,
    type: AbTestType,
    tx: ITransaction,
  ): Promise<AbTestRound>;

  getOne(
    abTestRoundId: string,
    type: AbTestType,
    tx: ITransaction,
  ): Promise<AbTestRound | null>;

  updateCurrentBestIdForNonStartedRoundsForAbTest(
    abTestId: string,
    currentBestId: string,
    type: AbTestType,
    tx: ITransaction,
  ): Promise<void>;

  deleteOne(
    abTestRoundId: string,
    type: AbTestType,
    tx: ITransaction,
  ): Promise<void>;
}
