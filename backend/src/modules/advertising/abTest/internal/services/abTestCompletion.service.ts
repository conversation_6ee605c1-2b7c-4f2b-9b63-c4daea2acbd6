import { err, ok, Result } from "neverthrow";

import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { IStageRepository } from "../../../application/interfaces/infrastructure/repositories/stage.repository.interface";
import { AbTest } from "../domain/abTest.entity";
import { AbTestType } from "../domain/abTestType.valueObject";
import { DataProviderService } from "./abTestDataProviders/dataProvider.service";

type AbTestCompletionError = {
  type:
    | "STAGE_NOT_FOUND"
    | "COMPLETION_FAILED";
};

interface AbTestCompletionResult {
  shouldPauseForUserInput: boolean;
  completionData?: {
    adSegmentId: string;
    winnerId: string;
    totalRounds: number;
  };
}

export interface IAbTestCompletionService {
  handleTestCompletion(input: {
    abTest: AbTest;
    abTestType: AbTestType;
    abTestRounds: any[];
    tx: ITransaction;
  }): Promise<Result<AbTestCompletionResult, AbTestCompletionError>>;
}

export class AbTestCompletionService implements IAbTestCompletionService {
  constructor(
    private readonly ctx: {
      stageRepository: IStageRepository;
      dataProviderService: DataProviderService;
    },
  ) {}

  async handleTestCompletion(input: {
    abTest: AbTest;
    abTestType: AbTestType;
    abTestRounds: any[];
    tx: ITransaction;
  }): Promise<Result<AbTestCompletionResult, AbTestCompletionError>> {
    // Get stage information
    const stage = await this.ctx.stageRepository.getStage(input.abTest.stageId, input.tx);
    if (!stage) {
      return err({ type: "STAGE_NOT_FOUND" });
    }

    // Delegate type-specific completion logic to data provider
    const completionResult = await this.ctx.dataProviderService.handleTestTypeCompletion({
      abTest: input.abTest,
      abTestType: input.abTestType,
      abTestRounds: input.abTestRounds,
      stage,
      tx: input.tx,
    });

    if (completionResult.isErr()) {
      return err({ type: "COMPLETION_FAILED" });
    }

    // Handle stage status updates if needed
    if (completionResult.value.shouldPauseStage) {
      await this.ctx.stageRepository.updateStageStatus(
        stage.id,
        "PAUSED",
        input.tx,
      );
    }

    return ok({
      shouldPauseForUserInput: completionResult.value.shouldPauseStage,
      completionData: completionResult.value.completionData,
    });
  }
} 