import { err, ok, Result } from "neverthrow";

import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { ILinkedInCampaignRepositoryInterface } from "../../../application/interfaces/infrastructure/repositories/linkedInCampaign.repository.interface";
import { ILinkedInSponsoredCreativeRepository } from "../../../application/interfaces/infrastructure/repositories/linkedInSponsoredCreative.repository.interface";
import { MapLinkedInStateInputToSponsoredCreativesService } from "../../../linkedInStateOrchestrator/application/services/mapLinkedInStateInputToSponsoredCreatives.service";
import { SetLinkedInStateService } from "../../../linkedInStateOrchestrator/application/services/setLinkedInState.service";
import { AbTest } from "../domain/abTest.entity";
import { AbTestType } from "../domain/abTestType.valueObject";
import { DataProviderService } from "./abTestDataProviders/dataProvider.service";

export class RunWinnerService {
  constructor(
    private readonly dataProvider: DataProviderService,
    private readonly setLinkedInStateService: SetLinkedInStateService,
    private readonly mapLinkedInStateInputToSponsoredCreativesService: MapLinkedInStateInputToSponsoredCreativesService,
    private readonly linkedInCampaignRepository: ILinkedInCampaignRepositoryInterface,
    private readonly linkedInSponsoredCreativeRepository: ILinkedInSponsoredCreativeRepository,
  ) {}

  async execute(input: {
    abTest: AbTest;
    adSegmentId: string;
    adProgramType:
      | "SPONSORED_CONTENT"
      | "SPONSORED_INMAIL"
      | "SPONSORED_CONVERSATION";

    tx: ITransaction;
  }): Promise<
    Result<
      void,
      {
        type:
          | "AB_TEST_NOT_COMPLETED"
          | "COULD_NOT_GET_LINKEDIN_STATE_ORCHESTRATOR_INPUT";
      }
    >
  > {
    if (
      input.abTest.status !== "AUTO_RESOLVED" &&
      input.abTest.status !== "USER_RESOLVED" &&
      input.abTest.status !== "COMPLETED"
    ) {
      return err({ type: "AB_TEST_NOT_COMPLETED" });
    }
    const linkedInStateOrchestratorInput =
      await this.dataProvider.getLinkedInStateOrchestratorInput({
        adSegmentId: input.adSegmentId,
        abTestType: input.abTest.type,
        currentBestVariantId: input.abTest.winnerId,
        contenderVariantId: input.abTest.winnerId,
      });
    if (linkedInStateOrchestratorInput.isErr()) {
      return err({ type: "COULD_NOT_GET_LINKEDIN_STATE_ORCHESTRATOR_INPUT" });
    }

    const campaigns = await this.linkedInCampaignRepository.getManyForAdSegment(
      input.adSegmentId,
      input.tx,
    );

    const sponsoredCreatives =
      await this.linkedInSponsoredCreativeRepository.getManyForAdSegment(
        input.adSegmentId,
        input.tx,
      );

    const mappedSponsoredCreatives =
      linkedInStateOrchestratorInput.value.adFormatType == "SPONSORED_CONTENT"
        ? await this.mapLinkedInStateInputToSponsoredCreativesService.setupLinkedInStateForSponsoredContent(
            linkedInStateOrchestratorInput.value,
          )
        : linkedInStateOrchestratorInput.value.adFormatType ==
            "SPONSORED_INMAIL"
          ? await this.mapLinkedInStateInputToSponsoredCreativesService.setupLinkedInStateForSponsoredInmail(
              linkedInStateOrchestratorInput.value,
            )
          : null;
    if (!mappedSponsoredCreatives) {
      throw new Error("Invalid ad format");
    }

    const campaignDesiredState: {
      campaignId: string;
      campaignUrn: string;
      status: "ACTIVE" | "PAUSED";
    }[] = campaigns.map((campaign) => {
      if (
        mappedSponsoredCreatives.find(
          (creative) =>
            creative.linkedInSponsoredCreative.cmapaignId ===
            campaign.linkedInAudienceId,
        )
      ) {
        return {
          campaignId: campaign.linkedInAudienceId,
          campaignUrn: campaign.linkedInCampaignUrn,
          status: "ACTIVE",
        };
      } else {
        return {
          campaignId: campaign.linkedInAudienceId,
          campaignUrn: campaign.linkedInCampaignUrn,
          status: "PAUSED",
        };
      }
    });

    const sponsoredCreativeDesiredState: {
      sponsoredCreativeId: string;
      sponsoredCreativeUrn: string;
      status: "ACTIVE" | "PAUSED";
    }[] = sponsoredCreatives.map((sponsoredCreative) => {
      if (
        mappedSponsoredCreatives.find(
          (creative) =>
            creative.linkedInSponsoredCreative.id === sponsoredCreative.id,
        )
      ) {
        return {
          sponsoredCreativeId: sponsoredCreative.id,
          sponsoredCreativeUrn: sponsoredCreative.linkedInSponseredCreativeUrn,
          status: "ACTIVE",
        };
      } else {
        return {
          sponsoredCreativeId: sponsoredCreative.id,
          sponsoredCreativeUrn: sponsoredCreative.linkedInSponseredCreativeUrn,
          status: "PAUSED",
        };
      }
    });

    await this.setLinkedInStateService.execute({
      desiredState: {
        campaigns: campaignDesiredState,
        sponsoredCreatives: sponsoredCreativeDesiredState,
      },
      adSegmentId: input.adSegmentId,
      tx: input.tx,
    });
    return ok();
  }
}
