import { err, ok, Result } from "neverthrow";

import { ILinkedInAdSegmentRepository } from "../../../../application/interfaces/infrastructure/repositories/linkedInAdSegment.repository.interface";
import { AdProgram } from "../../../../domain/entites/adProgram";
import { AdSegment } from "../../../../domain/entites/adSegment";
import { Stage } from "../../../../domain/entites/stage";
import { LinkedInStateInput } from "../../../../linkedInStateOrchestrator/domain/linkedInStateInput.valueObject";
import { LinkedInStateOutput } from "../../../../linkedInStateOrchestrator/domain/linkedInStateOutput.entity";
import { AbTest } from "../../domain/abTest.entity";
import { ITransaction } from "../../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";

export interface IAbTestTypeDataProviderService {
  getVariantsToSetupRounds: IGetVariantsToSetupRounds;
  getNamesFromVariantIds: IGetNamesFromVariantIds;
  getLinkedInStateOrchestratorInput: IGetLinkedInState;
  getMetricsForDecisionEngine: IGetMetricsForDecisionEngine;
  handleTestCompletion: IHandleTestCompletion;
}

interface IGetVariantsToSetupRounds {
  (input: { adProgram: AdProgram; adSegment: AdSegment }): Promise<
    Result<
      {
        [K in AdProgram["adFormat"]["type"]]: () => Promise<
          Result<
            string[],
            {
              type:
                | "COULD_NOT_GET_DATA_TO_SETUP_ROUNDS"
                | "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED";
            }
          >
        >;
      },
      {
        type: "COULD_NOT_GET_DATA_TO_SETUP_ROUNDS";
      }
    >
  >;
}

interface IGetNamesFromVariantIds {
  (input: { variantIds: string[] }): Promise<
    {
      variantId: string;
      name: string;
    }[]
  >;
}

interface IGetMetricsForDecisionEngine {
  (input: {
    adProgram: AdProgram;
    adSegment: AdSegment;
    contenderVariantId: string;
    currentBestVariantId: string;
    linkedInStateOutput: LinkedInStateOutput;
  }): Promise<
    Result<
      {
        [K in AdProgram["adFormat"]["type"]]: () => Promise<
          Result<
            {
              currentBestMetrics: {
                primaryMetric: number;
                secondaryMetric: number;
              };
              contenderMetrics: {
                primaryMetric: number;
                secondaryMetric: number;
              };
            },
            {
              type:
                | "COULD_NOT_GET_METRICS_FOR_DECISION_ENGINE"
                | "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED";
            }
          >
        >;
      },
      {
        type: "COULD_NOT_GET_METRICS_FOR_DECISION_ENGINE";
      }
    >
  >;
}

interface IGetLinkedInState {
  (input: {
    contenderVariantId: string;
    currentBestVariantId: string;
    adProgram: AdProgram;
    adSegment: AdSegment;
  }): Promise<
    Result<
      {
        [K in AdProgram["adFormat"]["type"]]: () => Promise<
          Result<
            LinkedInStateInput,
            {
              type:
                | "COULD_NOT_GET_LINKEDIN_STATE_ORCHESTRATOR_INPUT"
                | "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED";
            }
          >
        >;
      },
      {
        type: "COULD_NOT_GET_LINKEDIN_STATE_ORCHESTRATOR_INPUT";
      }
    >
  >;
}

interface IHandleTestCompletion {
  (input: {
    abTest: AbTest;
    abTestRounds: any[];
    stage: Stage;
    tx: ITransaction;
  }): Promise<
    Result<
      {
        shouldPauseStage: boolean;
        completionData?: any;
      },
      {
        type: "COMPLETION_FAILED";
      }
    >
  >;
}
