import { err, ok, Result } from "neverthrow";

import { IAdSegmentBestVariantRepository } from "../../../../../application/interfaces/infrastructure/repositories/adSegmentBestVariant.repository.interface";
import { IAdSegmentValuePropRepository } from "../../../../../application/interfaces/infrastructure/repositories/adSegmentValueProp.repository.interface";
import { IAdSegmentValuePropCreativeRepository } from "../../../../../application/interfaces/infrastructure/repositories/adSegmentValuePropCreative.repository.interface";
import { IConversationSubjectCopyRepository } from "../../../../../application/interfaces/infrastructure/repositories/conversationSubjectCopy.repository.interface";
import { ILinkedInAdSegmentRepository } from "../../../../../application/interfaces/infrastructure/repositories/linkedInAdSegment.repository.interface";
import { ILinkedInCampaignRepositoryInterface } from "../../../../../application/interfaces/infrastructure/repositories/linkedInCampaign.repository.interface";
import { AdProgram } from "../../../../../domain/entites/adProgram";
import { AdSegment } from "../../../../../domain/entites/adSegment";
import { LinkedInAdProgramRepository } from "../../../../../infrastructure/repositories/linkedInAdProgram.repository";
import { LinkedInStateInput } from "../../../../../linkedInStateOrchestrator/domain/linkedInStateInput.valueObject";
import { LinkedInStateOutput } from "../../../../../linkedInStateOrchestrator/domain/linkedInStateOutput.entity";
import { ValuePropAbTestDomain } from "../../../domain/abTestTypeDataProcessors/valuePropTestDataProcessor.domain";
import { IAbTestTypeDataProviderService } from "../abTestTypeDataProvider.serivce.interface";

export const ValuePropAbTestGetVariantsToSetupRounds = (deps: {
  adSegmentValuePropRepository: IAdSegmentValuePropRepository;
  adSegmentValuePropCreativeRepository: IAdSegmentValuePropCreativeRepository;
  adSegmentBestVariantRepository: IAdSegmentBestVariantRepository;
  adSegmentRepository: ILinkedInAdSegmentRepository;
  adProgramRepository: LinkedInAdProgramRepository;
  linkedInCampaignRepository: ILinkedInCampaignRepositoryInterface;
  conversationSubjectCopyRepository: IConversationSubjectCopyRepository;
}): IAbTestTypeDataProviderService => ({
  async getVariantsToSetupRounds(input: {
    adProgram: AdProgram;
    adSegment: AdSegment;
  }) {
    // Get value props for SPONSORED_CONTENT
    const SPONSORED_CONTENT = async (): Promise<
      Result<
        string[],
        {
          type:
            | "COULD_NOT_GET_DATA_TO_SETUP_ROUNDS"
            | "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED";
        }
      >
    > => {
      const bestCreative = await deps.adSegmentBestVariantRepository.getOne(
        input.adSegment.id,
        "creative",
      );

      if (!bestCreative) {
        return err({ type: "COULD_NOT_GET_DATA_TO_SETUP_ROUNDS" });
      }
      const valuePropCreatives =
        await deps.adSegmentValuePropCreativeRepository.getAllForAdSegment(
          input.adSegment.id,
        );

      return ok(
        ValuePropAbTestDomain.constructResourceIdsList.forSponsoredContent({
          adSegmentValuePropCreatives: valuePropCreatives,
          bestCreativeId: bestCreative.variantId,
        }),
      );
    };

    // Get value props for SPONSORED_INMAIL
    const SPONSORED_INMAIL = async (): Promise<
      Result<
        string[],
        {
          type:
            | "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED"
            | "COULD_NOT_GET_DATA_TO_SETUP_ROUNDS";
        }
      >
    > => {
      const bestConversationSubject =
        await deps.adSegmentBestVariantRepository.getOne(
          input.adSegment.id,
          "conversationSubject",
        );
      if (!bestConversationSubject) {
        return err({ type: "COULD_NOT_GET_DATA_TO_SETUP_ROUNDS" });
      }
      const bestConversationSubjectType = (
        await deps.conversationSubjectCopyRepository.getOneById(
          bestConversationSubject.variantId,
          "ACTIVE",
        )
      )?.type;
      if (!bestConversationSubjectType) {
        return err({ type: "COULD_NOT_GET_DATA_TO_SETUP_ROUNDS" });
      }

      const conversationSubjectCopies =
        await deps.conversationSubjectCopyRepository.getAllForAdSegment(
          input.adSegment.id,
          "ACTIVE",
        );

      return ok(
        ValuePropAbTestDomain.constructResourceIdsList.forSponsoredInmail({
          conversationSubjectCopies,
          bestConversationSubjectType,
        }),
      );
    };

    // Get value props for SPONSORED_CONVERSATION
    const SPONSORED_CONVERSATION = async (): Promise<
      Result<string[], { type: "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED" }>
    > => {
      return err({ type: "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED" });
    };

    return ok({
      SPONSORED_CONTENT,
      SPONSORED_CONVERSATION,
      SPONSORED_INMAIL,
    });
  },

  async getNamesFromVariantIds(input: { variantIds: string[] }) {
    const valueProps = await deps.adSegmentValuePropRepository.getManyByIds(
      input.variantIds,
      "ACTIVE",
    );
    return valueProps.map((each) => ({
      variantId: each.id,
      name: each.valueProp,
    }));
  },

  async getLinkedInStateOrchestratorInput(input: {
    contenderVariantId: string;
    currentBestVariantId: string;
    adProgram: AdProgram;
    adSegment: AdSegment;
  }) {
    const SPONSORED_CONTENT = async (): Promise<
      Result<
        LinkedInStateInput,
        {
          type:
            | "COULD_NOT_GET_LINKEDIN_STATE_ORCHESTRATOR_INPUT"
            | "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED";
        }
      >
    > => {
      const bestAudience = await deps.adSegmentBestVariantRepository.getOne(
        input.adSegment.id,
        "audience",
      );
      const audienceId =
        bestAudience?.variantId ??
        (
          await deps.linkedInCampaignRepository.getManyForAdSegment(
            input.adSegment.id,
          )
        )[0]?.linkedInAudienceId;

      if (!audienceId) {
        return err({ type: "COULD_NOT_GET_LINKEDIN_STATE_ORCHESTRATOR_INPUT" });
      }

      const bestCreative = await deps.adSegmentBestVariantRepository.getOne(
        input.adSegment.id,
        "creative",
      );
      if (!bestCreative) {
        return err({ type: "COULD_NOT_GET_LINKEDIN_STATE_ORCHESTRATOR_INPUT" });
      }

      const valuePropCreativeForCurrentBestValueProp =
        await deps.adSegmentValuePropCreativeRepository.getOneByAdProgramCreativeIdAndValuePropId(
          bestCreative.variantId,
          input.currentBestVariantId,
        );

      const valuePropCreativeForContenderValueProp =
        await deps.adSegmentValuePropCreativeRepository.getOneByAdProgramCreativeIdAndValuePropId(
          bestCreative.variantId,
          input.contenderVariantId,
        );

      if (
        !valuePropCreativeForCurrentBestValueProp ||
        !valuePropCreativeForContenderValueProp
      ) {
        return err({ type: "COULD_NOT_GET_LINKEDIN_STATE_ORCHESTRATOR_INPUT" });
      }

      return ok(
        ValuePropAbTestDomain.constructLinkerInStateOrchestratorInput.forSponsoredContent(
          {
            audienceId: audienceId,
            adSegmentId: input.adSegment.id,
            bestCreativeId: bestCreative.variantId,
            currentBestValuePropId: input.currentBestVariantId,
            contenderValuePropId: input.contenderVariantId,
            bestSocialPostBodyCopyType: null,
            bestSocialPostCallToActionType: null,
            adProgram: input.adProgram,
          },
        ),
      );
    };

    const SPONSORED_INMAIL = async (): Promise<
      Result<
        LinkedInStateInput,
        {
          type:
            | "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED"
            | "COULD_NOT_GET_LINKEDIN_STATE_ORCHESTRATOR_INPUT";
        }
      >
    > => {
      const bestAudience = await deps.adSegmentBestVariantRepository.getOne(
        input.adSegment.id,
        "audience",
      );
      const audienceId =
        bestAudience?.variantId ??
        (
          await deps.linkedInCampaignRepository.getManyForAdSegment(
            input.adSegment.id,
          )
        )[0]?.linkedInAudienceId;

      if (!audienceId) {
        return err({ type: "COULD_NOT_GET_LINKEDIN_STATE_ORCHESTRATOR_INPUT" });
      }
      const bestConversationSubject =
        await deps.adSegmentBestVariantRepository.getOne(
          input.adSegment.id,
          "conversationSubject",
        );
      if (!bestConversationSubject) {
        return err({ type: "COULD_NOT_GET_LINKEDIN_STATE_ORCHESTRATOR_INPUT" });
      }
      const bestConversationSubjectType = (
        await deps.conversationSubjectCopyRepository.getOneById(
          bestConversationSubject.variantId,
          "ACTIVE",
        )
      )?.type;
      if (!bestConversationSubjectType) {
        return err({ type: "COULD_NOT_GET_LINKEDIN_STATE_ORCHESTRATOR_INPUT" });
      }

      const conversationSubjectForCurrentBest =
        await deps.conversationSubjectCopyRepository.getOne({
          valuePropId: input.currentBestVariantId,
          conversationCopyType: bestConversationSubjectType,
          status: "ACTIVE",
        });

      const conversationSubjectForContender =
        await deps.conversationSubjectCopyRepository.getOne({
          valuePropId: input.contenderVariantId,
          conversationCopyType: bestConversationSubjectType,
          status: "ACTIVE",
        });

      if (
        !conversationSubjectForCurrentBest ||
        !conversationSubjectForContender
      ) {
        return err({ type: "COULD_NOT_GET_LINKEDIN_STATE_ORCHESTRATOR_INPUT" });
      }

      return ok(
        ValuePropAbTestDomain.constructLinkerInStateOrchestratorInput.forSponsoredInmail(
          {
            adSegmentId: input.adSegment.id,
            audienceId: audienceId,
            conversationSubjectCopyType: bestConversationSubjectType,
            currentBestValuePropId: input.currentBestVariantId,
            contenderValuePropId: input.contenderVariantId,
            bestConversationMessageCopyType: null,
            bestConversationCallToActionCopyType: null,
          },
        ),
      );
    };

    const SPONSORED_CONVERSATION = async (): Promise<
      Result<
        LinkedInStateInput,
        { type: "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED" }
      >
    > => {
      return err({ type: "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED" });
    };
    return ok({
      SPONSORED_CONTENT,
      SPONSORED_INMAIL,
      SPONSORED_CONVERSATION,
    });
  },

  async getMetricsForDecisionEngine(input: {
    adProgram: AdProgram;
    adSegment: AdSegment;
    contenderVariantId: string;
    currentBestVariantId: string;
    linkedInStateOutput: LinkedInStateOutput;
  }) {
    const SPONSORED_CONTENT = async (): Promise<
      Result<
        {
          currentBestMetrics: {
            primaryMetric: number;
            secondaryMetric: number;
          };
          contenderMetrics: { primaryMetric: number; secondaryMetric: number };
        },
        {
          type:
            | "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED"
            | "COULD_NOT_GET_METRICS_FOR_DECISION_ENGINE";
        }
      >
    > => {
      const res =
        ValuePropAbTestDomain.constructMetricsForDecisionEngine.forSponsoredContent(
          {
            adProgram: input.adProgram,
            linkedInStateOutput: input.linkedInStateOutput,
            currentBestValuePropId: input.currentBestVariantId,
            contenderValuePropId: input.contenderVariantId,
          },
        );
      if (res.isErr()) {
        return err({ type: "COULD_NOT_GET_METRICS_FOR_DECISION_ENGINE" });
      }
      return ok(res.value);
    };

    const SPONSORED_INMAIL = async (): Promise<
      Result<
        {
          currentBestMetrics: {
            primaryMetric: number;
            secondaryMetric: number;
          };
          contenderMetrics: { primaryMetric: number; secondaryMetric: number };
        },
        {
          type:
            | "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED"
            | "COULD_NOT_GET_METRICS_FOR_DECISION_ENGINE";
        }
      >
    > => {
      const res =
        ValuePropAbTestDomain.constructMetricsForDecisionEngine.forSponsoredContent(
          {
            adProgram: input.adProgram,
            linkedInStateOutput: input.linkedInStateOutput,
            currentBestValuePropId: input.currentBestVariantId,
            contenderValuePropId: input.contenderVariantId,
          },
        );
      if (res.isErr()) {
        return err({ type: "COULD_NOT_GET_METRICS_FOR_DECISION_ENGINE" });
      }
      return ok(res.value);
    };

    const SPONSORED_CONVERSATION = async (): Promise<
      Result<
        {
          currentBestMetrics: {
            primaryMetric: number;
            secondaryMetric: number;
          };
          contenderMetrics: { primaryMetric: number; secondaryMetric: number };
        },
        {
          type:
            | "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED"
            | "COULD_NOT_GET_METRICS_FOR_DECISION_ENGINE";
        }
      >
    > => {
      return err({ type: "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED" });
    };

    return ok({
      SPONSORED_CONTENT: SPONSORED_CONTENT,
      SPONSORED_INMAIL: SPONSORED_INMAIL,
      SPONSORED_CONVERSATION: SPONSORED_CONVERSATION,
    });
  },

  async handleTestCompletion(input: {
    abTest: any;
    abTestRounds: any[];
    stage: any;
    tx: any;
  }) {
    // Value prop tests proceed normally without pausing
    return ok({
      shouldPauseStage: false,
      completionData: null,
    });
  },
});
