import { err, ok, Result } from "neverthrow";

import { IAdSegmentBestVariantRepository } from "../../../../../application/interfaces/infrastructure/repositories/adSegmentBestVariant.repository.interface";
import { IAdSegmentValuePropRepository } from "../../../../../application/interfaces/infrastructure/repositories/adSegmentValueProp.repository.interface";
import { IAdSegmentValuePropCreativeRepository } from "../../../../../application/interfaces/infrastructure/repositories/adSegmentValuePropCreative.repository.interface";
import { ILinkedInAdSegmentRepository } from "../../../../../application/interfaces/infrastructure/repositories/linkedInAdSegment.repository.interface";
import { ILinkedInCampaignRepositoryInterface } from "../../../../../application/interfaces/infrastructure/repositories/linkedInCampaign.repository.interface";
import { AdProgram } from "../../../../../domain/entites/adProgram";
import { AdSegment } from "../../../../../domain/entites/adSegment";
import { LinkedInAdProgramRepository } from "../../../../../infrastructure/repositories/linkedInAdProgram.repository";
import { LinkedInStateInput } from "../../../../../linkedInStateOrchestrator/domain/linkedInStateInput.valueObject";
import { LinkedInStateOutput } from "../../../../../linkedInStateOrchestrator/domain/linkedInStateOutput.entity";
import { CreativeAbTestDomain } from "../../../domain/abTestTypeDataProcessors/creativeTestDataProcessor.domain";
import { IAbTestTypeDataProviderService } from "../abTestTypeDataProvider.serivce.interface";

export const CreativeAbTestGetVariantsToSetupRounds = (deps: {
  adSegmentValuePropRepository: IAdSegmentValuePropRepository;
  adSegmentValuePropCreativeRepository: IAdSegmentValuePropCreativeRepository;
  adSegmentBestVariantRepository: IAdSegmentBestVariantRepository;
  adSegmentRepository: ILinkedInAdSegmentRepository;
  adProgramRepository: LinkedInAdProgramRepository;
  linkedInCampaignRepository: ILinkedInCampaignRepositoryInterface;
}): IAbTestTypeDataProviderService => ({
  async getVariantsToSetupRounds(input: {
    adProgram: AdProgram;
    adSegment: AdSegment;
  }) {
    // Get value props for SPONSORED_CONTENT
    const SPONSORED_CONTENT = async (): Promise<
      Result<
        string[],
        {
          type:
            | "COULD_NOT_GET_DATA_TO_SETUP_ROUNDS"
            | "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED";
        }
      >
    > => {
      const valuePropCreatives =
        await deps.adSegmentValuePropCreativeRepository.getAllForAdSegment(
          input.adSegment.id,
        );
      return ok(
        CreativeAbTestDomain.constructResourceIdsList.forSponsoredContent({
          adSegmentValuePropCreatives: valuePropCreatives,
        }),
      );
    };

    // Get value props for SPONSORED_INMAIL
    const SPONSORED_INMAIL = async (): Promise<
      Result<
        string[],
        {
          type:
            | "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED"
            | "COULD_NOT_GET_DATA_TO_SETUP_ROUNDS";
        }
      >
    > => {
      return err({ type: "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED" });
    };

    // Get value props for SPONSORED_CONVERSATION
    const SPONSORED_CONVERSATION = async (): Promise<
      Result<string[], { type: "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED" }>
    > => {
      return err({ type: "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED" });
    };

    return ok({
      SPONSORED_CONTENT,
      SPONSORED_CONVERSATION,
      SPONSORED_INMAIL,
    });
  },

  async getNamesFromVariantIds(input: { variantIds: string[] }) {
    const adProgramCreatives =
      await deps.adSegmentValuePropCreativeRepository.getManyByAdProgramCreativeIds(
        input.variantIds,
      );

    const arr: {
      variantId: string;
      name: string;
    }[] = [];

    for (const each of adProgramCreatives) {
      const found = arr.find((e) => e.variantId === each.adProgramCreative.id);
      if (!found) {
        arr.push({
          variantId: each.adProgramCreative.id,
          name: each.name,
        });
      }
    }
    return arr;
  },

  async getLinkedInStateOrchestratorInput(input: {
    contenderVariantId: string;
    currentBestVariantId: string;
    adProgram: AdProgram;
    adSegment: AdSegment;
  }) {
    const SPONSORED_CONTENT = async (): Promise<
      Result<
        LinkedInStateInput,
        {
          type:
            | "COULD_NOT_GET_LINKEDIN_STATE_ORCHESTRATOR_INPUT"
            | "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED";
        }
      >
    > => {
      const bestAudience = await deps.adSegmentBestVariantRepository.getOne(
        input.adSegment.id,
        "audience",
      );
      const audienceId =
        bestAudience?.variantId ??
        (
          await deps.linkedInCampaignRepository.getManyForAdSegment(
            input.adSegment.id,
          )
        )[0]?.linkedInAudienceId;

      if (!audienceId) {
        return err({ type: "COULD_NOT_GET_LINKEDIN_STATE_ORCHESTRATOR_INPUT" });
      }

      const valuePropCreatives =
        await deps.adSegmentValuePropCreativeRepository.getAllForAdSegment(
          input.adSegment.id,
        );

      return ok(
        CreativeAbTestDomain.constructLinkerInStateOrchestratorInput.forSponsoredContent(
          {
            audienceId: audienceId,
            adSegmentId: input.adSegment.id,
            adSegmentValuePropCreatives: valuePropCreatives,
            currentBestCreativeId: input.currentBestVariantId,
            contenderCreativeId: input.contenderVariantId,
            bestSocialPostBodyCopyType: null,
            bestSocialPostCallToActionType: null,
            adProgram: input.adProgram,
          },
        ),
      );
    };

    const SPONSORED_INMAIL = async (): Promise<
      Result<
        LinkedInStateInput,
        {
          type:
            | "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED"
            | "COULD_NOT_GET_LINKEDIN_STATE_ORCHESTRATOR_INPUT";
        }
      >
    > => {
      return err({ type: "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED" });
    };

    const SPONSORED_CONVERSATION = async (): Promise<
      Result<
        LinkedInStateInput,
        { type: "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED" }
      >
    > => {
      return err({ type: "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED" });
    };
    return ok({
      SPONSORED_CONTENT,
      SPONSORED_INMAIL,
      SPONSORED_CONVERSATION,
    });
  },

  async getMetricsForDecisionEngine(input: {
    adProgram: AdProgram;
    adSegment: AdSegment;
    contenderVariantId: string;
    currentBestVariantId: string;
    linkedInStateOutput: LinkedInStateOutput;
  }) {
    const SPONSORED_CONTENT = async (): Promise<
      Result<
        {
          currentBestMetrics: {
            primaryMetric: number;
            secondaryMetric: number;
          };
          contenderMetrics: { primaryMetric: number; secondaryMetric: number };
        },
        {
          type:
            | "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED"
            | "COULD_NOT_GET_METRICS_FOR_DECISION_ENGINE";
        }
      >
    > => {
      const res =
        CreativeAbTestDomain.constructMetricsForDecisionEngine.forSponsoredContent(
          {
            adProgram: input.adProgram,
            linkedInStateOutput: input.linkedInStateOutput,
            currentBestCreativeId: input.currentBestVariantId,
            contenderCreativeId: input.contenderVariantId,
          },
        );
      if (res.isErr()) {
        return err({ type: "COULD_NOT_GET_METRICS_FOR_DECISION_ENGINE" });
      }
      return ok(res.value);
    };

    const SPONSORED_INMAIL = async (): Promise<
      Result<
        {
          currentBestMetrics: {
            primaryMetric: number;
            secondaryMetric: number;
          };
          contenderMetrics: { primaryMetric: number; secondaryMetric: number };
        },
        {
          type:
            | "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED"
            | "COULD_NOT_GET_METRICS_FOR_DECISION_ENGINE";
        }
      >
    > => {
      return err({ type: "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED" });
    };

    const SPONSORED_CONVERSATION = async (): Promise<
      Result<
        {
          currentBestMetrics: {
            primaryMetric: number;
            secondaryMetric: number;
          };
          contenderMetrics: { primaryMetric: number; secondaryMetric: number };
        },
        {
          type:
            | "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED"
            | "COULD_NOT_GET_METRICS_FOR_DECISION_ENGINE";
        }
      >
    > => {
      return err({ type: "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED" });
    };

    return ok({
      SPONSORED_CONTENT: SPONSORED_CONTENT,
      SPONSORED_INMAIL: SPONSORED_INMAIL,
      SPONSORED_CONVERSATION: SPONSORED_CONVERSATION,
    });
  },

  async handleTestCompletion(input: {
    abTest: any;
    abTestRounds: any[];
    stage: any;
    tx: any;
  }) {
    // Creative tests proceed normally without pausing
    return ok({
      shouldPauseStage: false,
      completionData: null,
    });
  },
});
