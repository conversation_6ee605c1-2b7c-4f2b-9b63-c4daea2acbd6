import { err, ok, Result } from "neverthrow";

import { ILinkedInAdSegmentRepository } from "../../../../application/interfaces/infrastructure/repositories/linkedInAdSegment.repository.interface";
import { Stage } from "../../../../domain/entites/stage";
import { LinkedInAdProgramRepository } from "../../../../infrastructure/repositories/linkedInAdProgram.repository";
import { LinkedInStateInput } from "../../../../linkedInStateOrchestrator/domain/linkedInStateInput.valueObject";
import { LinkedInStateOutput } from "../../../../linkedInStateOrchestrator/domain/linkedInStateOutput.entity";
import { AbTest } from "../../domain/abTest.entity";
import { AbTestType } from "../../domain/abTestType.valueObject";
import { DataProviderRegistry } from "./dataProvider.registry";
import { ITransaction } from "../../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";

export class DataProviderService {
  constructor(
    private readonly ctx: {
      adSegmentRepository: ILinkedInAdSegmentRepository;
      adProgramRepository: LinkedInAdProgramRepository;
    },
  ) {}
  async getVariantsToSetupRounds(input: {
    adSegmentId: string;
    abTestType: AbTestType;
  }): Promise<
    Result<
      string[],
      {
        type:
          | "COULD_NOT_GET_DATA_TO_SETUP_ROUNDS"
          | "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED";
      }
    >
  > {
    const adSegment = await this.ctx.adSegmentRepository.getOne(
      input.adSegmentId,
    );
    if (!adSegment) {
      console.log("adSegment not found");
      return err({
        type: "COULD_NOT_GET_DATA_TO_SETUP_ROUNDS",
      });
    }
    const adProgram = await this.ctx.adProgramRepository.getOne(
      adSegment.linkedInAdProgramId,
    );
    if (!adProgram) {
      console.log("adProgram not found");
      return err({
        type: "COULD_NOT_GET_DATA_TO_SETUP_ROUNDS",
      });
    }

    const abTestDataProvider = DataProviderRegistry[input.abTestType];
    const getVariantsToSetupRoundsFunctionForAbTestType =
      await abTestDataProvider.getVariantsToSetupRounds({
        adProgram,
        adSegment,
      });
    if (getVariantsToSetupRoundsFunctionForAbTestType.isErr()) {
      console.log(
        "getVariantsToSetupRoundsFunctionForAbTestType error",
        getVariantsToSetupRoundsFunctionForAbTestType.error,
      );
      return err({
        type: "COULD_NOT_GET_DATA_TO_SETUP_ROUNDS",
      });
    }
    const res =
      await getVariantsToSetupRoundsFunctionForAbTestType.value[
        adProgram.adFormat.type
      ]();
    return res;
  }

  async getNamesFromVariantIds(input: {
    variantIds: string[];
    abTestType: AbTestType;
  }): Promise<
    {
      variantId: string;
      name: string;
    }[]
  > {
    const abTestDataProvider = DataProviderRegistry[input.abTestType];
    const getVariantsToSetupRoundsFunctionForAbTestType =
      await abTestDataProvider.getNamesFromVariantIds({
        variantIds: input.variantIds,
      });
    return getVariantsToSetupRoundsFunctionForAbTestType;
  }

  async getLinkedInStateOrchestratorInput(input: {
    adSegmentId: string;
    abTestType: AbTestType;
    currentBestVariantId: string;
    contenderVariantId: string;
  }): Promise<
    Result<
      LinkedInStateInput,
      {
        type:
          | "COULD_NOT_GET_LINKEDIN_STATE_ORCHESTRATOR_INPUT"
          | "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED";
      }
    >
  > {
    const adSegment = await this.ctx.adSegmentRepository.getOne(
      input.adSegmentId,
    );
    if (!adSegment) {
      console.log("adSegment not found");
      return err({
        type: "COULD_NOT_GET_LINKEDIN_STATE_ORCHESTRATOR_INPUT",
      });
    }

    const adProgram = await this.ctx.adProgramRepository.getOne(
      adSegment.linkedInAdProgramId,
    );
    if (!adProgram) {
      console.log("adProgram not found");
      return err({
        type: "COULD_NOT_GET_LINKEDIN_STATE_ORCHESTRATOR_INPUT",
      });
    }
    const abTestDataProvider = DataProviderRegistry[input.abTestType];
    const getLinkedInStateOrchestratorInputFunctionForAbTestType =
      await abTestDataProvider.getLinkedInStateOrchestratorInput({
        adProgram,
        adSegment,
        currentBestVariantId: input.currentBestVariantId,
        contenderVariantId: input.contenderVariantId,
      });
    if (getLinkedInStateOrchestratorInputFunctionForAbTestType.isErr()) {
      console.log("Error getting linkedin state orchestrator input for ab test type", getLinkedInStateOrchestratorInputFunctionForAbTestType.error);
      return err({
        type: "COULD_NOT_GET_LINKEDIN_STATE_ORCHESTRATOR_INPUT",
      });
    }
    const res =
      await getLinkedInStateOrchestratorInputFunctionForAbTestType.value[
        adProgram.adFormat.type
      ]();
    return res;
  }

  async getMetricsForDecisionEngine(input: {
    currentBestVariantId: string;
    contenderVariantId: string;
    linkedInStateOutput: LinkedInStateOutput;
    abTestType: AbTestType;
  }): Promise<
    Result<
      {
        currentBestMetrics: {
          primaryMetric: number;
          secondaryMetric: number;
        };
        contenderMetrics: {
          primaryMetric: number;
          secondaryMetric: number;
        };
      },
      {
        type:
          | "COULD_NOT_GET_METRICS_FOR_DECISION_ENGINE"
          | "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED";
      }
    >
  > {
    const adSegment = await this.ctx.adSegmentRepository.getOne(
      input.linkedInStateOutput.adSegmentId,
    );
    if (!adSegment) {
      return err({
        type: "COULD_NOT_GET_METRICS_FOR_DECISION_ENGINE",
      });
    }

    const adProgram = await this.ctx.adProgramRepository.getOne(
      adSegment.linkedInAdProgramId,
    );
    if (!adProgram) {
      return err({
        type: "COULD_NOT_GET_METRICS_FOR_DECISION_ENGINE",
      });
    }

    const abTestDataProvider = DataProviderRegistry[input.abTestType];
    const getMetricsForDecisionEngineFunctionForAbTestType =
      await abTestDataProvider.getMetricsForDecisionEngine({
        contenderVariantId: input.contenderVariantId,
        currentBestVariantId: input.currentBestVariantId,
        adProgram,
        adSegment,
        linkedInStateOutput: input.linkedInStateOutput,
      });
    if (getMetricsForDecisionEngineFunctionForAbTestType.isErr()) {
      return err({
        type: "COULD_NOT_GET_METRICS_FOR_DECISION_ENGINE",
      });
    }

    const res =
      await getMetricsForDecisionEngineFunctionForAbTestType.value[
        adProgram.adFormat.type
      ]();
    return res;
  }

  async handleTestTypeCompletion(input: {
    abTest: AbTest;
    abTestType: AbTestType;
    abTestRounds: any[];
    stage: Stage;
    tx: ITransaction;
  }): Promise<
    Result<
      {
        shouldPauseStage: boolean;
        completionData?: any;
      },
      {
        type: "COMPLETION_FAILED";
      }
    >
  > {
    const abTestDataProvider = DataProviderRegistry[input.abTestType];
    return await abTestDataProvider.handleTestCompletion({
      abTest: input.abTest,
      abTestRounds: input.abTestRounds,
      stage: input.stage,
      tx: input.tx,
    });
  }
}
