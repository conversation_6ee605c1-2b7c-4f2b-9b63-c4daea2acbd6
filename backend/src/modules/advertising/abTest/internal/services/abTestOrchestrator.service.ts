import { err, ok, Result } from "neverthrow";

import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { IJobTriggerPublisher } from "../../../../shared/jobTriggerPublisher.interface";
import { SendAbTestCompletionNotificationUseCase } from "../../../application/useCase/notifications/sendAbTestCompletionNotification.useCase";
import { advertisingInngestClient } from "../../../utils/advertisingInngestClient";
import { CreateAndProvisionAbTestRoundDayCommandHandler } from "../commands/createAndProvisionAbTestRoundDay/createAndProvisionAbTestRoundDay.command.handler";
import { EndAbTestCommandHandler } from "../commands/endAbTest/endAbTest.command.handler";
import { EndAbTestRoundCommandHandler } from "../commands/endAbTestRound/endAbTestRound.command.handler";
import { EndAbTestRoundDayCommandHandler } from "../commands/endAbTestRoundDay/endAbTestRoundDay.command.hanlder";
import { SetCurrentBestVariantForAbTestHandler } from "../commands/setCurrentBestVariantForAbTest/setCurrentBestVariantForAbTest.command.handler";
import { StartAbTestCommandHandler } from "../commands/startAbTest/startAbTest.command.handler";
import { StartAbTestRoundDayCommandHandler } from "../commands/startAbTestRoundDay/startAbTestRoundDay.command";
import { StartNextAbTestRoundCommandHandler } from "../commands/startNextAbTestRound/startNextAbTestRound.command.handler";
import { AbTestType } from "../domain/abTestType.valueObject";
import { createAndProvisionRoundDayJobTrigger } from "../jobTriggers/createAndProvision.jobTrigger";
import { endAbTestJobTrigger } from "../jobTriggers/endAbTest.jobTrigger";
import { endAbTestRoundJobTrigger } from "../jobTriggers/endAbTestRound.jobTrigger";
import { startAbTestRoundDayJobTrigger } from "../jobTriggers/startAbTestRoundDay.jobTrigger";
import { startNextAbTestRoundJobTrigger } from "../jobTriggers/startNextAbTestRound.jobTrigger";
import { waitForAbTestRoundDayToEndJobTrigger } from "../jobTriggers/waitForAbTestRoundDayToEnd.jobTrigger";
import { IAbTestRoundRepository } from "../repositories/abTestRound.repository.interface";

interface StartAbTestError {
  type:
    | "STAGE_NOT_FOUND"
    | "STAGE_NOT_RUNNING"
    | "AB_TEST_ALREADY_EXISTS_FOR_STAGE"
    | "COULD_NOT_GET_DATA_TO_SETUP_ROUNDS"
    | "NO_DATA_AVAILABLE_TO_SETUP_ROUNDS";
}

interface StartNextAbTestRoundError {
  type:
    | "AB_TEST_NOT_FOUND"
    | "NO_ROUNDS_FOUND"
    | "AB_TEST_NOT_IN_PROGRESS"
    | "NO_ROUNDS_TO_START"
    | "ROUND_ALREADY_IN_PROGRESS";
}

interface CreateAndProvisionRoundDayError {
  type:
    | "COULD_NOT_CREATE_ROUND_DAY"
    | "AB_TEST_ROUND_NOT_FOUND"
    | "AB_TEST_NOT_FOUND"
    | "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED"
    | "COULD_NOT_GET_LINKEDIN_STATE_ORCHESTRATOR_INPUT"
    | "UNCOMPLETED_AB_TEST_ROUND_DAYS_ALREADY_EXIST"
    | "AB_TEST_ROUND_NOT_RUNNING";
}

interface StartAbTestRoundDayError {
  type:
    | "AB_TEST_ROUND_DAY_NOT_FOUND"
    | "STATE_CONFIG_NOT_FOUND"
    | "AD_SEGMENT_NOT_FOUND"
    | "AD_PROGRAM_NOT_FOUND"
    | "DEPLOYMENT_CONFIG_ID_DOES_NOT_MATCH"
    | "DEPLOYMENT_CONFIG_NOT_RUNNING"
    | "AB_TEST_ROUND_DAY_ALREADY_STARTED";
}

interface EndAbTestRoundDayError {
  type:
    | "AB_TEST_ROUND_DAY_NOT_FOUND"
    | "AB_TEST_ROUND_DAY_NOT_IN_PROGRESS"
    | "COULD_NOT_STOP_LINKEDIN_STATE"
    | "COULD_NOT_GET_LINKEDIN_STATE_METRICS"
    | "COULD_NOT_GET_METRICS_FOR_DECISION_ENGINE"
    | "COULD_NOT_GET_DECISION"
    | "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED";
}

interface EndAbTestRoundError {
  type: "AB_TEST_ROUND_NOT_FOUND" | "AB_TEST_ROUND_NOT_IN_PROGRESS";
}

interface EndAbTestError {
  type:
    | "AB_TEST_NOT_FOUND"
    | "AB_TEST_NOT_IN_PROGRESS"
    | "AB_TEST_ALREADY_ENDED"
    | "NOT_ALL_AB_TEST_ROUNDS_COMPLETED";
}

class AbTestOrchestratorService {
  constructor(
    private readonly startAbTestCommandHandler: StartAbTestCommandHandler,
    private readonly abTestType: AbTestType,
    private readonly jobTriggerPublisher: IJobTriggerPublisher,
    private readonly startNextAbTestRoundCommandHandler: StartNextAbTestRoundCommandHandler,
    private readonly createAndProvisionRoundDayCommandHandler: CreateAndProvisionAbTestRoundDayCommandHandler,
    private readonly startAbTestRoundDayCommandHandler: StartAbTestRoundDayCommandHandler,
    private readonly endAbTestRoundDayCommandHandler: EndAbTestRoundDayCommandHandler,
    private readonly endAbTestRoundCommandHandler: EndAbTestRoundCommandHandler,
    private readonly abTestRoundRepository: IAbTestRoundRepository,
    private readonly endAbTestCommandHandler: EndAbTestCommandHandler,
    private readonly setCurrentBestVariantForAbTestCommandHandler: SetCurrentBestVariantForAbTestHandler,
    private readonly sendAbTestCompletionNotificationUseCase?: SendAbTestCompletionNotificationUseCase,
  ) {}

  async handleStartAbTestEvent(
    stageId: string,
    adSegmentId: string,
    tx: ITransaction,
  ): Promise<Result<void, StartAbTestError>> {
    const startAbTestResult = await this.startAbTestCommandHandler.execute({
      stageId: stageId,
      type: this.abTestType,
      tx: tx,
    });

    console.log("Start ab test result", startAbTestResult);

    if (startAbTestResult.isErr()) {
      return err(startAbTestResult.error);
    }

    if (startAbTestResult.value.outcome == "AUTO_RESOLVED") {
      await advertisingInngestClient.send({
        name: "linkedin/stage.end",
        data: {
          stageId: startAbTestResult.value.abTest.stageId,
        },
      });
    }

    if (startAbTestResult.value.outcome == "IN_PROGRESS") {
      await this.jobTriggerPublisher.publish(
        startNextAbTestRoundJobTrigger.build({
          abTestId: startAbTestResult.value.abTest.stageId,
          abTestType: this.abTestType,
          adSegmentId: adSegmentId,
        }),
      );
    }

    return ok();
  }

  async handleStartNextRoundEvent(
    abTestId: string,
    adSegmentId: string,
    tx: ITransaction,
  ): Promise<Result<void, StartNextAbTestRoundError>> {
    const startNextRoundResult =
      await this.startNextAbTestRoundCommandHandler.execute({
        abTestId: abTestId,
        abTestType: this.abTestType,
        tx: tx,
      });

    if (startNextRoundResult.isErr()) {
      return err(startNextRoundResult.error);
    }

    await this.jobTriggerPublisher.publish(
      createAndProvisionRoundDayJobTrigger.build({
        abTestRoundId: startNextRoundResult.value.id,
        type: this.abTestType,
        adSegmentId: adSegmentId,
      }),
    );
    return ok();
  }

  async handleCreateAndProvisionRoundDayEvent(
    abTestRoundId: string,
    adSegmentId: string,
    tx: ITransaction,
  ): Promise<Result<void, CreateAndProvisionRoundDayError>> {
    const createAndProvisionRoundDayResult =
      await this.createAndProvisionRoundDayCommandHandler.execute({
        abTestRoundId: abTestRoundId,
        type: this.abTestType,
        tx: tx,
      });
    if (createAndProvisionRoundDayResult.isErr()) {
      return err({ type: createAndProvisionRoundDayResult.error.type });
    }

    await this.jobTriggerPublisher.publish(
      startAbTestRoundDayJobTrigger.build({
        abTestRoundDayId: createAndProvisionRoundDayResult.value.id,
        type: this.abTestType,
        adSegmentId: adSegmentId,
      }),
    );

    return ok();
  }

  async handleStartAbTestRoundDayEvent(
    abTestRoundDayId: string,
    adSegmentId: string,
    tx: ITransaction,
  ): Promise<Result<void, StartAbTestRoundDayError>> {
    const startAbTestRoundDayResult =
      await this.startAbTestRoundDayCommandHandler.execute({
        abTestRoundDayId: abTestRoundDayId,
        type: this.abTestType,
        tx: tx,
      });
    if (startAbTestRoundDayResult.isErr()) {
      return err(startAbTestRoundDayResult.error);
    }

    await this.jobTriggerPublisher.publish(
      waitForAbTestRoundDayToEndJobTrigger.build({
        abTestRoundDayId: abTestRoundDayId,
        abTestType: this.abTestType,
        adSegmentId: adSegmentId,
      }),
    );
    return ok();
  }

  async handleEndAbTestRoundDayEvent(
    abTestRoundDayId: string,
    adSegmentId: string,
    tx: ITransaction,
  ): Promise<Result<void, EndAbTestRoundDayError>> {
    const endAbTestRoundDayResult =
      await this.endAbTestRoundDayCommandHandler.execute({
        abTestRoundDayId: abTestRoundDayId,
        abTestType: this.abTestType,
        tx: tx,
      });
    if (endAbTestRoundDayResult.isErr()) {
      return err(endAbTestRoundDayResult.error);
    }

    if (endAbTestRoundDayResult.value.winner == "NO_DECISION") {
      await this.jobTriggerPublisher.publish(
        createAndProvisionRoundDayJobTrigger.build({
          abTestRoundId: endAbTestRoundDayResult.value.abTestRoundId,
          type: this.abTestType,
          adSegmentId: adSegmentId,
        }),
      );
    } else if (
      endAbTestRoundDayResult.value.winner == "CURRENT_BEST" ||
      endAbTestRoundDayResult.value.winner == "CONTENDER"
    ) {
      await this.jobTriggerPublisher.publish(
        endAbTestRoundJobTrigger.build({
          abTestRoundId: endAbTestRoundDayResult.value.abTestRoundId,
          abTestType: this.abTestType,
          winner: endAbTestRoundDayResult.value.winner,
          adSegmentId: adSegmentId,
        }),
      );
    } else {
      throw new Error("Invalid winner");
    }
    return ok();
  }

  async handleEndAbTestRoundEvent(
    abTestRoundId: string,
    winner: "CURRENT_BEST" | "CONTENDER",
    adSegmentId: string,
    tx: ITransaction,
  ): Promise<Result<void, EndAbTestRoundError>> {
    console.log("handleEndAbTestRoundEvent", abTestRoundId, winner);
    const endAbTestRoundResult =
      await this.endAbTestRoundCommandHandler.execute({
        abTestRoundId: abTestRoundId,
        abTestType: this.abTestType,
        winner: winner,
        tx: tx,
      });
    if (endAbTestRoundResult.isErr()) {
      return err(endAbTestRoundResult.error);
    }

    if (endAbTestRoundResult.value.status !== "COMPLETED") {
      throw new Error("AbTestRound is not completed");
    }

    console.log("DEBUG: endAbTestRoundResult.value:", JSON.stringify(endAbTestRoundResult.value, null, 2));
    console.log("DEBUG: abTestId from result:", endAbTestRoundResult.value.abTestId);

    await this.setCurrentBestVariantForAbTestCommandHandler.execute({
      abTestId: endAbTestRoundResult.value.abTestId,
      abTestType: this.abTestType,
      variantId:
        winner === "CURRENT_BEST"
          ? endAbTestRoundResult.value.currentBestId
          : endAbTestRoundResult.value.contenderId,
      tx: tx,
    });

    const notStartedRounds = (
      await this.abTestRoundRepository.getAllForAbTest(
        endAbTestRoundResult.value.abTestId,
        this.abTestType,
        tx,
      )
    ).filter((round) => round.status === "NOT_STARTED");
    console.log("notStartedRounds", notStartedRounds);

    if (notStartedRounds.length > 0) {
      await this.jobTriggerPublisher.publish(
        startNextAbTestRoundJobTrigger.build({
          abTestId: endAbTestRoundResult.value.abTestId,
          abTestType: this.abTestType,
          adSegmentId: adSegmentId,
        }),
      );
    } else {
      console.log("DEBUG: About to trigger endAbTestJobTrigger with abTestId:", endAbTestRoundResult.value.abTestId);
      await this.jobTriggerPublisher.publish(
        endAbTestJobTrigger.build({
          abTestId: endAbTestRoundResult.value.abTestId,
          abTestType: this.abTestType,
          adSegmentId: adSegmentId,
        }),
      );
    }

    return ok();
  }

  async handleEndAbTestEvent(
    abTestId: string,
    tx: ITransaction,
  ): Promise<Result<void, EndAbTestError>> {
    const endAbTestResult = await this.endAbTestCommandHandler.execute({
      abTestId: abTestId,
      abTestType: this.abTestType,
      tx: tx,
    });
    if (endAbTestResult.isErr()) {
      return err(endAbTestResult.error);
    }

    // Handle notifications at orchestrator level if completion data is available
    if (endAbTestResult.value.completionData && this.sendAbTestCompletionNotificationUseCase) {
      // Send notifications asynchronously (don't block completion)
      this.sendAbTestCompletionNotificationUseCase.execute(
        endAbTestResult.value.completionData
      ).catch((error: any) => {
        console.error("Failed to send AB test completion notification:", error);
      });
    }

    if (!endAbTestResult.value.shouldPauseForUserInput) {
      await advertisingInngestClient.send({
        name: "linkedin/stage.end",
        data: {
          stageId: abTestId,
        },
      });
    }

    return ok();
  }
}
export const AbTestOrchestratorServiceFactory = ({
  startAbTestCommandHandler,
  jobTriggerPublisher,
  startNextAbTestRoundCommandHandler,
  createAndProvisionRoundDayCommandHandler,
  startAbTestRoundDayCommandHandler,
  endAbTestRoundDayCommandHandler,
  endAbTestRoundCommandHandler,
  abTestRoundRepository,
  endAbTestCommandHandler,
  setCurrentBestVariantForAbTestCommandHandler,
  sendAbTestCompletionNotificationUseCase,
}: {
  startAbTestCommandHandler: StartAbTestCommandHandler;
  jobTriggerPublisher: IJobTriggerPublisher;
  startNextAbTestRoundCommandHandler: StartNextAbTestRoundCommandHandler;
  createAndProvisionRoundDayCommandHandler: CreateAndProvisionAbTestRoundDayCommandHandler;
  startAbTestRoundDayCommandHandler: StartAbTestRoundDayCommandHandler;
  endAbTestRoundDayCommandHandler: EndAbTestRoundDayCommandHandler;
  endAbTestRoundCommandHandler: EndAbTestRoundCommandHandler;
  abTestRoundRepository: IAbTestRoundRepository;
  endAbTestCommandHandler: EndAbTestCommandHandler;
  setCurrentBestVariantForAbTestCommandHandler: SetCurrentBestVariantForAbTestHandler;
  sendAbTestCompletionNotificationUseCase?: SendAbTestCompletionNotificationUseCase;
}) => {
  return (type: AbTestType) =>
    new AbTestOrchestratorService(
      startAbTestCommandHandler,
      type,
      jobTriggerPublisher,
      startNextAbTestRoundCommandHandler,
      createAndProvisionRoundDayCommandHandler,
      startAbTestRoundDayCommandHandler,
      endAbTestRoundDayCommandHandler,
      endAbTestRoundCommandHandler,
      abTestRoundRepository,
      endAbTestCommandHandler,
      setCurrentBestVariantForAbTestCommandHandler,
      sendAbTestCompletionNotificationUseCase,
    );
};
