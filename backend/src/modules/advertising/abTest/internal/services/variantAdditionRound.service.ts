import { err, ok, Result } from "neverthrow";

import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { IJobTriggerPublisher } from "../../../../shared/jobTriggerPublisher.interface";
import { AbTestRoundDomain } from "../domain/abTestRound.domain";
import { AbTestType } from "../domain/abTestType.valueObject";
import { IAbTestRoundRepository } from "../repositories/abTestRound.repository.interface";
import { startNextAbTestRoundJobTrigger } from "../jobTriggers/startNextAbTestRound.jobTrigger";

type VariantAdditionRoundError = {
  type:
    | "FAILED_TO_CREATE_ROUND"
    | "FAILED_TO_QUEUE_ROUND";
};

interface CreateVariantAdditionRoundResult {
  newRoundId: string;
  wasQueued: boolean;
}

// Track operations for rollback
interface RoundCreationState {
  createdRoundId?: string;
  triggeredJob?: boolean;
}

export interface IVariantAdditionRoundService {
  createVariantAdditionRound(input: {
    abTestId: string;
    abTestType: AbTestType;
    adSegmentId: string;
    currentBestId: string;
    contenderId: string;
    rounds: any[];
    shouldStartImmediately: boolean;
    tx: ITransaction;
  }): Promise<Result<CreateVariantAdditionRoundResult, VariantAdditionRoundError>>;
}

export class VariantAdditionRoundService implements IVariantAdditionRoundService {
  constructor(
    private readonly ctx: {
      abTestRoundRepository: IAbTestRoundRepository;
      jobTriggerPublisher: IJobTriggerPublisher;
    },
  ) {}

  async createVariantAdditionRound(input: {
    abTestId: string;
    adSegmentId: string;
    abTestType: AbTestType;
    currentBestId: string;
    contenderId: string;
    rounds: any[];
    shouldStartImmediately: boolean;
    tx: ITransaction;
  }): Promise<Result<CreateVariantAdditionRoundResult, VariantAdditionRoundError>> {
    const roundState: RoundCreationState = {};
    
    try {
      // Create the new round for variant addition
      const nextRoundIndex = Math.max(...input.rounds.map(r => r.roundIndex)) + 1;
      
      const newRoundResult = AbTestRoundDomain.createNewRound({
        abTestId: input.abTestId,
        currentBestId: input.currentBestId,
        contenderId: input.contenderId,
        roundIndex: nextRoundIndex,
      });

      if (newRoundResult.isErr()) {
        return err({ type: "FAILED_TO_CREATE_ROUND" });
      }

      // Save the new round
      await this.ctx.abTestRoundRepository.createMany(
        [newRoundResult.value],
        input.abTestType as any,
        input.tx,
      );
      
      roundState.createdRoundId = newRoundResult.value.id;

      // If should start immediately, queue it for starting
      if (input.shouldStartImmediately) {
        try {
          // Use existing job trigger to start the round via the established flow
          await this.ctx.jobTriggerPublisher.publish(
            startNextAbTestRoundJobTrigger.build({
              abTestId: input.abTestId,
              abTestType: input.abTestType as any,
              adSegmentId: input.adSegmentId,
            }),
          );
          
          roundState.triggeredJob = true;
          
          return ok({
            newRoundId: newRoundResult.value.id,
            wasQueued: false, // It will start via existing infrastructure
          });
        } catch (error) {
          console.error("Failed to trigger round start:", error);
          
          // Rollback the created round
          await this.rollbackRoundCreation(roundState, input);
          
          return err({ type: "FAILED_TO_QUEUE_ROUND" });
        }
      }

      // Round created but not started (will be picked up later by existing flow)
      return ok({
        newRoundId: newRoundResult.value.id,
        wasQueued: true,
      });
    } catch (error) {
      console.error("Unexpected error in variant addition round service:", error);
      
      // Rollback any partial operations
      await this.rollbackRoundCreation(roundState, input);
      
      return err({ type: "FAILED_TO_CREATE_ROUND" });
    }
  }

  /**
   * Rollback round creation in case of failure
   */
  private async rollbackRoundCreation(
    roundState: RoundCreationState,
    input: {
      abTestId: string;
      abTestType: AbTestType;
      tx: ITransaction;
    },
  ): Promise<void> {
    console.log("Rolling back round creation...", roundState);
    
    try {
      // Note: We don't rollback the created round record as it's within the same transaction
      // The transaction will be rolled back automatically if the overall operation fails
      
      // Note: Job triggers are typically idempotent and safe to leave as-is
      // The job system should handle cases where the round doesn't exist anymore
      
      console.log("Round creation rollback completed");
    } catch (rollbackError) {
      console.error("Critical error during round creation rollback:", rollbackError);
      // Log error but don't throw to avoid masking original error
    }
  }
} 