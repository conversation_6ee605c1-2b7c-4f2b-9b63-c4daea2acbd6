import { err, ok, Result } from "neverthrow";

import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { ILinkedInAdAudienceRepository } from "../../../application/interfaces/infrastructure/repositories/linkedInAdAudience.repository.interface";
import { AbTestType } from "../domain/abTestType.valueObject";

type VariantValidationError = {
  type:
    | "VARIANT_NOT_FOUND"
    | "VARIANT_NOT_AUTHORIZED"
    | "VARIANT_ALREADY_EXISTS"
    | "VALIDATION_FAILED";
};

interface VariantValidationResult {
  isValid: boolean;
  variant: any;
}

export interface IVariantValidationService {
  validateVariantForAddition(input: {
    newVariantId: string;
    organizationId: number;
    abTestType: AbTestType;
    existingVariants: Set<string>;
    tx: ITransaction;
  }): Promise<Result<VariantValidationResult, VariantValidationError>>;
}

export class VariantValidationService implements IVariantValidationService {
  constructor(
    private readonly ctx: {
      adAudienceRepository: ILinkedInAdAudienceRepository;
      // Add other variant repositories as needed
    },
  ) {}

  async validateVariantForAddition(input: {
    newVariantId: string;
    organizationId: number;
    abTestType: AbTestType;
    existingVariants: Set<string>;
    tx: ITransaction;
  }): Promise<Result<VariantValidationResult, VariantValidationError>> {
    // Check if variant already exists in the test
    if (input.existingVariants.has(input.newVariantId)) {
      return err({ type: "VARIANT_ALREADY_EXISTS" });
    }

    // Delegate to type-specific validation
    switch (input.abTestType) {
      case "audience":
        return this.validateAudienceVariant(input);
      case "valueProp":
        return this.validateValuePropVariant(input);
      case "creative":
        return this.validateCreativeVariant(input);
      default:
        return err({ type: "VALIDATION_FAILED" });
    }
  }

  private async validateAudienceVariant(input: {
    newVariantId: string;
    organizationId: number;
    tx: ITransaction;
  }): Promise<Result<VariantValidationResult, VariantValidationError>> {
    // Get the audience to ensure it exists
    const adAudience = await this.ctx.adAudienceRepository.getOne(
      input.newVariantId,
      input.tx,
    );
    if (!adAudience) {
      return err({ type: "VARIANT_NOT_FOUND" });
    }

    // Verify audience belongs to organization
    const audienceBelongsToOrg = await this.ctx.adAudienceRepository.checkIfAudienceExistsForOrganization(
      input.organizationId,
      input.newVariantId,
      input.tx,
    );
    
    if (!audienceBelongsToOrg) {
      return err({ type: "VARIANT_NOT_AUTHORIZED" });
    }

    return ok({
      isValid: true,
      variant: adAudience,
    });
  }

  private async validateValuePropVariant(input: {
    newVariantId: string;
    organizationId: number;
    tx: ITransaction;
  }): Promise<Result<VariantValidationResult, VariantValidationError>> {
    // TODO: Implement value prop validation
    return err({ type: "VALIDATION_FAILED" });
  }

  private async validateCreativeVariant(input: {
    newVariantId: string;
    organizationId: number;
    tx: ITransaction;
  }): Promise<Result<VariantValidationResult, VariantValidationError>> {
    // TODO: Implement creative validation
    return err({ type: "VALIDATION_FAILED" });
  }
} 