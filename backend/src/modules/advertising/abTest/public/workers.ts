// Import functions from the new worker files

// Import functions from the old interface adapters (these may need to be migrated)
import { startAbTest as runAbTestInngestFunction } from "../../interfaceAdapters/inngestFunctions/runAbTest.inngestFunction";
import { runAbTestRound as runAbTestRoundInngestFunction } from "../../interfaceAdapters/inngestFunctions/runAbTestRound.inngestFunction";
import { runAbTestRoundDay as runAbTestRoundDayInngestFunction } from "../../interfaceAdapters/inngestFunctions/runAbTestRoundDay.inngestFunction";
import { createAndProvisionAbTestRoundDayInngestFunction } from "./interfaceAdapters/inngest/workers/createAndProisionAbTestRoundDay.inngest.worker";
import { endAbTestInngestFunction } from "./interfaceAdapters/inngest/workers/endAbTest.inngest.worker";
import { endAbTestRoundInngestFunction } from "./interfaceAdapters/inngest/workers/endAbTestRound.inngest.worker";
import { endAbTestRoundDayInngestFunction } from "./interfaceAdapters/inngest/workers/endAbTestRoundDay.inngest.worker";
import { startAbTestInngestFunction } from "./interfaceAdapters/inngest/workers/startAbTest.inngest.worker";
import { startAbTestRoundDayInngestFunction } from "./interfaceAdapters/inngest/workers/startAbTestRoundDay.inngest.worker";
import { startNextAbTestRoundInngestFunction } from "./interfaceAdapters/inngest/workers/startNextAbTestRound.inngest.worker";
import { waitForAbTestRoundDatToEndInngestFunction } from "./interfaceAdapters/inngest/workers/waitForAbTestRoundDayToEnd.inngest.worker";
import { waitForStageUserInputTimeoutInngestFunction } from "./interfaceAdapters/inngest/workers/waitForStageUserInputTimeout.inngest.worker";

export const abTestWorkers = [
  endAbTestInngestFunction,
  endAbTestRoundInngestFunction,
  endAbTestRoundDayInngestFunction,
  runAbTestInngestFunction,
  runAbTestRoundInngestFunction,
  runAbTestRoundDayInngestFunction,
  startAbTestInngestFunction,
  startNextAbTestRoundInngestFunction,
  startAbTestRoundDayInngestFunction,
  createAndProvisionAbTestRoundDayInngestFunction,
  waitForAbTestRoundDatToEndInngestFunction,
  waitForStageUserInputTimeoutInngestFunction,
];
