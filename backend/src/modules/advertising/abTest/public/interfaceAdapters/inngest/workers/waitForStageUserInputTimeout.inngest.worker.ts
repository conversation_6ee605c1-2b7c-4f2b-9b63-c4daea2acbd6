import { NonRetriableError } from "inngest";

import { TransactionManagerService } from "../../../../../../core/infrastructure/services/transcationManager.service";
import { LinkedInAdAccountRepository } from "../../../../../infrastructure/repositories/linkedInAdAccount.repository";
import { LinkedInAdProgramRepository } from "../../../../../infrastructure/repositories/linkedInAdProgram.repository";
import { LinkedInAdSegmentRepository } from "../../../../../infrastructure/repositories/linkedInAdSegment.repository";
import { StageRepository } from "../../../../../infrastructure/repositories/stage.repository";
import { advertisingInngestClient } from "../../../../../utils/advertisingInngestClient";
import { waitForStageUserInputTimeoutJobTrigger } from "../../../../internal/jobTriggers/waitForStageUserInputTimeout.jobTrigger";

export const waitForStageUserInputTimeoutInngestFunction =
  advertisingInngestClient.createFunction(
    {
      id: "wait-for-stage-user-input-timeout",
      cancelOn: [
        {
          event: "stage/user-input-timeout.cancel",
          if: "async.data.stageId == event.data.stageId",
        },
      ],
    },
    { event: waitForStageUserInputTimeoutJobTrigger.NAME },
    async ({ step, event }) => {
      const input = waitForStageUserInputTimeoutJobTrigger.schema.parse(
        event.data,
      );

      console.log(`[Stage Timeout] Starting 3-day timeout for stage ${input.stageId}`);

      // Wait for 3 days
      await step.sleep("wait-for-user-input-timeout", "3d");

      console.log(`[Stage Timeout] 3-day timeout expired for stage ${input.stageId}, proceeding to next stage`);

      const transactionManager = new TransactionManagerService();

      await transactionManager.startTransaction(async (tx) => {
        const stageRepository = new StageRepository();
        const adSegmentRepository = new LinkedInAdSegmentRepository();
        const adProgramRepository = new LinkedInAdProgramRepository();
        const adAccountRepository = new LinkedInAdAccountRepository();

        // Get the stage
        const stage = await stageRepository.getStage(input.stageId, tx);
        if (!stage) {
          console.log(`[Stage Timeout] Stage ${input.stageId} not found, ending timeout`);
          return;
        }

        // Check if stage is still paused (user hasn't responded)
        if (stage.status !== "PAUSED") {
          console.log(`[Stage Timeout] Stage ${input.stageId} is no longer paused (status: ${stage.status}), ending timeout`);
          return;
        }

        // Verify organization access
        const adSegment = await adSegmentRepository.getOne(stage.adSegmentId, tx);
        if (!adSegment) {
          throw new NonRetriableError("Ad segment not found");
        }

        const adProgram = await adProgramRepository.getOne(adSegment.linkedInAdProgramId);
        if (!adProgram) {
          throw new NonRetriableError("Ad program not found");
        }

        const adAccount = await adAccountRepository.getOneById(adProgram.linkedInAdAccountId);
        if (!adAccount) {
          throw new NonRetriableError("Ad account not found");
        }

        if (adAccount.organizationId !== input.organizationId) {
          throw new NonRetriableError("Organization mismatch - potential security issue");
        }

        console.log(`[Stage Timeout] Auto-advancing stage ${input.stageId} to next stage due to timeout`);

        // Mark current stage as finished 
        await stageRepository.updateStageStatus(stage.id, "FINISHED", tx);
      });

      // Trigger next stage after transaction commits
      await step.run("trigger-next-stage", async () => {
        await advertisingInngestClient.send({
          name: "linkedin/stage.run",
          data: {
            adSegmentId: input.adSegmentId,
            organizationId: input.organizationId,
          },
        });
      });

      console.log(`[Stage Timeout] Successfully advanced stage ${input.stageId} to next stage`);
    },
  ); 