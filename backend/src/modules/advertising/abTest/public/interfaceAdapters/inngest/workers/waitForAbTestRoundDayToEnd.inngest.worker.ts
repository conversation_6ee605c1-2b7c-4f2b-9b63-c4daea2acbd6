import { TransactionManagerService } from "../../../../../../core/infrastructure/services/transcationManager.service";
import { InngestJobTriggerPublisher } from "../../../../../../shared/inngestJobTriggerPublisher";
import { advertisingInngestClient } from "../../../../../utils/advertisingInngestClient";
import { endAbTestRoundDayJobTrigger } from "../../../../internal/jobTriggers/endAbTestRoundDay.jobTrigger";
import { waitForAbTestRoundDayToEndJobTrigger } from "../../../../internal/jobTriggers/waitForAbTestRoundDayToEnd.jobTrigger";
import { AbTestRoundDayRepository } from "../../../../internal/repositories/abTestRoundDay.repository";

export const waitForAbTestRoundDatToEndInngestFunction =
  advertisingInngestClient.createFunction(
    {
      id: "wait-for-ab-test-round-day-to-end-2",
      cancelOn: [
        {
          event: "stages/cancel",
          if: "async.data.adSegmentId == event.data.adSegmentId",
        },
      ],
    },
    { event: waitForAbTestRoundDayToEndJobTrigger.NAME },
    async ({ step, event }) => {
      const input = waitForAbTestRoundDayToEndJobTrigger.schema.parse(
        event.data,
      );
      const transactionManager = new TransactionManagerService();

      const abTestRoundDay = await transactionManager.startTransaction(
        async (tx) => {
          const abTestRoundDayRepository = new AbTestRoundDayRepository();
          const abTestRoundDay = await abTestRoundDayRepository.getOne(
            input.abTestRoundDayId,
            input.abTestType,
            tx,
          );
          if (!abTestRoundDay) {
            throw new Error("AbTestRoundDay not found");
          }
          return abTestRoundDay;
        },
      );

      const abTestRoundDayLength =
        process.env.AB_TEST_ROUND_DAY_LENGTH ?? "24h";
      await step.sleep("wait-for-ab-test-round-day", abTestRoundDayLength);

      const inngestJobTriggerPublisher = InngestJobTriggerPublisher(
        advertisingInngestClient,
      );

      inngestJobTriggerPublisher.publish(
        endAbTestRoundDayJobTrigger.build({
          abTestRoundDayId: abTestRoundDay.id,
          type: input.abTestType,
          adSegmentId: input.adSegmentId,
        }),
      );
    },
  );
